import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserUpdateWithoutAvatarInputSchema } from './UserUpdateWithoutAvatarInputSchema';
import { UserUncheckedUpdateWithoutAvatarInputSchema } from './UserUncheckedUpdateWithoutAvatarInputSchema';
import { UserCreateWithoutAvatarInputSchema } from './UserCreateWithoutAvatarInputSchema';
import { UserUncheckedCreateWithoutAvatarInputSchema } from './UserUncheckedCreateWithoutAvatarInputSchema';
import { UserWhereInputSchema } from './UserWhereInputSchema';

export const UserUpsertWithoutAvatarInputSchema: z.ZodType<Prisma.UserUpsertWithoutAvatarInput> = z.object({
  update: z.union([ z.lazy(() => UserUpdateWithoutAvatarInputSchema),z.lazy(() => UserUncheckedUpdateWithoutAvatarInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutAvatarInputSchema),z.lazy(() => UserUncheckedCreateWithoutAvatarInputSchema) ]),
  where: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export default UserUpsertWithoutAvatarInputSchema;
