# Navigation Components

<cite>
**Referenced Files in This Document**   
- [Navbar.tsx](file://frontend/src/components/Navbar.tsx)
- [UserMenu.tsx](file://frontend/src/components/UserMenu.tsx)
- [LangSwitcher.tsx](file://frontend/src/components/LangSwitcher.tsx)
- [ProfileContainer.tsx](file://frontend/src/components/ProfileContainer.tsx)
- [ProfileSettings.tsx](file://frontend/src/components/ProfileSettings.tsx)
- [ProfilePage.tsx](file://frontend/src/pages/ProfilePage.tsx)
- [auth.ts](file://frontend/src/lib/auth.ts)
- [i18n.ts](file://frontend/src/i18n.ts)
- [store.ts](file://frontend/src/store.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Navbar Implementation](#navbar-implementation)
3. [UserMenu and Authentication Integration](#usermenu-and-authentication-integration)
4. [LangSwitcher and Multi-Language Support](#langswitcher-and-multi-language-support)
5. [Profile Container and Settings Components](#profile-container-and-settings-components)
6. [State Management and Responsive Design](#state-management-and-responsive-design)
7. [Accessibility and Theming](#accessibility-and-theming)

## Introduction
The navigation and user interface components in takeNpass provide a cohesive user experience across devices and locales. This document details the implementation of key navigation elements including the responsive Navbar, user authentication integration, language switching functionality, and profile management components. The system leverages modern React patterns with Zustand for state management, react-i18next for internationalization, and responsive design principles to ensure accessibility and usability across device sizes.

## Navbar Implementation

The Navbar component serves as the primary navigation interface in takeNpass, implementing responsive design patterns and mobile-first considerations. On smaller devices (under 680px), the navigation collapses into a sidebar drawer that is triggered by a menu icon button. The component uses `useMediaQuery` from `@uidotdev/usehooks` to detect screen size and adapt the layout accordingly.

The navbar includes several key features:
- Brand logo and home link with smooth scrolling behavior
- Conditional rendering of language switcher and theme toggle based on device size
- Authentication state-aware UI elements (login button or user menu)
- WebSocket status indicator for real-time connection monitoring
- Mobile sidebar that contains user profile information and navigation menu

The mobile menu behavior is controlled by the `showSidebar` state variable, which toggles the visibility of a Drawer component containing the UserMenu and other navigation elements. When the route changes, the sidebar automatically closes via useEffect cleanup.

```mermaid
flowchart TD
A[Navbar Render] --> B{isSmallDevice?}
B --> |Yes| C[Show Menu Icon]
B --> |No| D[Show LangSwitcher & Theme Toggle]
C --> E[Click Menu Icon]
E --> F[Toggle Sidebar]
F --> G[Open Drawer]
G --> H[Display UserMenu]
D --> I[Display Full Navigation]
```

**Diagram sources**
- [Navbar.tsx](file://frontend/src/components/Navbar.tsx#L51-L249)

**Section sources**
- [Navbar.tsx](file://frontend/src/components/Navbar.tsx#L1-L250)

## UserMenu and Authentication Integration

The UserMenu component provides secondary navigation options and logout functionality, tightly integrated with the application's authentication state through the `useUserStore` from `store.ts`. The component displays different navigation options based on the user's authentication status and fetches unread message counts via TRPC queries.

Authentication state is managed through the `authClient` from `better-auth`, which handles sign-in and sign-out operations. When a user logs out, the component:
1. Calls `authClient.signOut()` to invalidate the session
2. Clears user data from the Zustand store using `userStore.setData(null)`
3. Displays appropriate toast notifications using `addToast`
4. Redirects to the home page

The UserMenu is conditionally rendered in different locations based on screen size - within the mobile sidebar drawer on small devices and in the main navbar on larger screens. It includes navigation links to key user areas such as Messages, Profile, Settings, Reviews, and Favorites, with dynamic badge indicators for unread messages.

```mermaid
sequenceDiagram
participant UserMenu
participant AuthClient
participant UserStore
participant Toast
UserMenu->>UserMenu : Click Logout
UserMenu->>AuthClient : signOut()
AuthClient-->>UserMenu : Signout Result
UserMenu->>UserStore : setData(null)
UserMenu->>Toast : addToast(success)
UserMenu->>Router : navigate to /
```

**Diagram sources**
- [UserMenu.tsx](file://frontend/src/components/UserMenu.tsx#L1-L84)
- [auth.ts](file://frontend/src/lib/auth.ts#L1-L10)

**Section sources**
- [UserMenu.tsx](file://frontend/src/components/UserMenu.tsx#L1-L84)
- [auth.ts](file://frontend/src/lib/auth.ts#L1-L10)

## LangSwitcher and Multi-Language Support

The LangSwitcher component enables multi-language support in takeNpass, allowing users to switch between English, Russian, and Spanish interfaces. It integrates with the i18next internationalization framework through `react-i18next` and persists user language preferences to both localStorage and the backend via TRPC mutations.

The component's functionality includes:
- Displaying language options with native language names
- Using `i18next.changeLanguage()` to update the active language
- Storing the selected language in localStorage for persistence
- Updating the user's language preference on the server when authenticated
- Synchronizing with the user's stored language preference on mount

Language resources are defined in separate locale files (en.json, ru.json, es.json) and imported through corresponding TypeScript modules. The default language is determined by checking localStorage first, then falling back to the browser's language setting, with English as the final fallback.

```mermaid
flowchart LR
A[LangSwitcher] --> B[User Selects Language]
B --> C[updateLang function]
C --> D[i18next.changeLanguage]
C --> E[localStorage.setItem]
C --> F[updateUserLang mutation]
D --> G[UI Updates]
E --> H[Persistence]
F --> I[Server Sync]
```

**Diagram sources**
- [LangSwitcher.tsx](file://frontend/src/components/LangSwitcher.tsx#L1-L75)
- [i18n.ts](file://frontend/src/i18n.ts#L1-L46)

**Section sources**
- [LangSwitcher.tsx](file://frontend/src/components/LangSwitcher.tsx#L1-L75)
- [i18n.ts](file://frontend/src/i18n.ts#L1-L46)

## Profile Container and Settings Components

The ProfileContainer component serves as a layout wrapper for profile-related pages, implementing responsive design principles to optimize the user experience across device sizes. It conditionally renders the UserMenu in a sidebar on larger screens (above 700px) while hiding it on smaller devices, where navigation is handled through the main navbar or mobile menu.

The ProfileSettings component provides a form interface for users to edit their profile information, including:
- Username, email, and phone number fields with validation
- Avatar upload functionality through the Upload component
- About me textarea for personal description
- Form submission with error handling and success notifications

Form state is managed locally within the component, with changes synchronized to the backend via TRPC mutations. The component displays appropriate toast notifications for success and error states and updates the global user store upon successful profile updates.

```mermaid
classDiagram
class ProfileContainer {
+isVisibleUserMenuMQ : boolean
+children : JSX.Element
}
class ProfileSettings {
-username : string
-email : string
-phone : string
-avatar : string
-aboutMe : string
-errors : Object
+handleSave() : void
+onAvatarUpdate(base64string : string) : void
}
class Upload {
+Activator : Component
+onChangeBase64 : Function
+setValue : Function
}
ProfileContainer --> UserMenu : "contains"
ProfileContainer --> ProfileSettings : "wraps"
ProfileSettings --> Upload : "uses"
ProfileSettings --> trpc.users.updateProfile : "calls"
```

**Diagram sources**
- [ProfileContainer.tsx](file://frontend/src/components/ProfileContainer.tsx#L1-L32)
- [ProfileSettings.tsx](file://frontend/src/components/ProfileSettings.tsx#L1-L160)

**Section sources**
- [ProfileContainer.tsx](file://frontend/src/components/ProfileContainer.tsx#L1-L32)
- [ProfileSettings.tsx](file://frontend/src/components/ProfileSettings.tsx#L1-L160)
- [ProfilePage.tsx](file://frontend/src/pages/ProfilePage.tsx#L1-L37)

## State Management and Responsive Design

State management in the navigation components is implemented using Zustand with persistence middleware, allowing user authentication state to survive page refreshes. The `useUserStore` provides a centralized store for user data that is consumed by multiple components including Navbar, UserMenu, and Profile components.

Responsive design is achieved through media queries using `useMediaQuery`, with breakpoints defined at:
- 680px: Determines mobile vs desktop navbar layout
- 380px: Further optimizes icon-only buttons for very small devices
- 700px: Controls visibility of sidebar menu in profile pages

The theme toggle functionality allows users to switch between light and dark modes, with the preference persisted in localStorage and applied to the document root class. This implementation ensures consistent theming across all navigation elements and pages.

```mermaid
flowchart TB
A[Zustand Store] --> B[useUserStore]
B --> C[Navbar]
B --> D[UserMenu]
B --> E[Profile Components]
F[useMediaQuery] --> G[Responsive Layouts]
H[localStorage] --> I[Theme Persistence]
H --> J[Language Preference]
K[i18next] --> L[Multi-Language Support]
```

**Diagram sources**
- [store.ts](file://frontend/src/store.ts#L1-L27)
- [Navbar.tsx](file://frontend/src/components/Navbar.tsx#L51-L86)

**Section sources**
- [store.ts](file://frontend/src/store.ts#L1-L27)

## Accessibility and Theming

The navigation components implement several accessibility features to ensure usability for all users:
- Proper ARIA labels for interactive elements (theme toggle, language selector)
- Keyboard navigation support through standard HTML semantics
- Sufficient color contrast in both light and dark themes
- Semantic HTML structure with appropriate heading levels
- Focus indicators for interactive elements

Theming consistency is maintained across all navigation elements through the use of CSS variables and Tailwind CSS classes. The dark mode implementation toggles a 'dark' class on the HTML element, which cascades down to all components and adjusts colors accordingly. Iconography is consistent throughout the navigation system, with custom SVG components providing visual cues for key actions.

The components also support keyboard navigation, with logical tab order and appropriate focus management, particularly in the mobile drawer menu and dropdown interfaces. Error states in forms are communicated through both visual indicators and programmatic focus management to ensure accessibility for screen reader users.

**Section sources**
- [Navbar.tsx](file://frontend/src/components/Navbar.tsx#L1-L250)
- [UserMenu.tsx](file://frontend/src/components/UserMenu.tsx#L1-L84)
- [LangSwitcher.tsx](file://frontend/src/components/LangSwitcher.tsx#L1-L75)