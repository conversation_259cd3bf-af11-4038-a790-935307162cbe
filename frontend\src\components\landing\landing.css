/* Landing Page Custom Styles */

/* CSS Variables for RGB values */
:root {
  --primary-rgb: 59, 130, 246; /* blue-500 */
  --secondary-rgb: 168, 85, 247; /* purple-500 */
  --accent-rgb: 34, 197, 94; /* green-500 */
}

/* Glass effect */
.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Enhanced glass variants */
.glass-card-light {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.glass-card-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.glass-card-primary {
  background: rgba(var(--primary-rgb), 0.08);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(var(--primary-rgb), 0.2);
  box-shadow: 0 8px 32px rgba(var(--primary-rgb), 0.1);
}

/* Brutalist shadow effect */
.brutalist-shadow {
  box-shadow: 8px 8px 0px 0px rgba(0, 0, 0, 0.1);
}

.brutalist-shadow:hover {
  box-shadow: none;
}

/* Enhanced gradient text with better contrast */
.gradient-text {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)), hsl(var(--accent)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease-in-out infinite;
  /* Fallback for better accessibility */
  color: hsl(var(--primary));
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Glass text effect for better readability */
.glass-text {
  color: hsl(var(--foreground));
  text-shadow:
    0 1px 2px rgba(0, 0, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2),
    0 4px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(1px);
}

/* High contrast gradient text */
.gradient-text-contrast {
  background: linear-gradient(135deg,
    hsl(var(--primary)) 0%,
    hsl(var(--secondary)) 50%,
    hsl(var(--accent)) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease-in-out infinite;
  /* Enhanced shadow for better readability */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  position: relative;
}

.gradient-text-contrast::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  color: rgba(0, 0, 0, 0.2);
  transform: translate(2px, 2px);
  font-weight: inherit;
  font-size: inherit;
}

/* Enhanced contrast for better readability */
.gradient-text-contrast::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  z-index: -2;
  color: rgba(255, 255, 255, 0.8);
  transform: translate(-1px, -1px);
  font-weight: inherit;
  font-size: inherit;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Floating animation */
.floating {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Pulse glow effect */
.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  from {
    box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.3);
  }
  to {
    box-shadow: 0 0 40px rgba(var(--primary-rgb), 0.6);
  }
}

/* Hover lift effect */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Stagger animation for children */
.stagger-children > * {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-children > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-children > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-children > *:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Magnetic hover effect */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic:hover {
  transform: scale(1.05);
}

/* Shimmer effect */
.shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Typewriter effect */
.typewriter {
  overflow: hidden;
  border-right: 2px solid hsl(var(--primary));
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: hsl(var(--primary));
  }
}

/* Parallax scroll effect */
.parallax {
  transform: translateZ(0);
  will-change: transform;
}

/* Neon glow */
.neon-glow {
  text-shadow: 
    0 0 5px hsl(var(--primary)),
    0 0 10px hsl(var(--primary)),
    0 0 15px hsl(var(--primary)),
    0 0 20px hsl(var(--primary));
}

/* Morphing background */
.morphing-bg {
  background: linear-gradient(-45deg, 
    hsl(var(--primary)), 
    hsl(var(--secondary)), 
    hsl(var(--accent)), 
    hsl(var(--primary))
  );
  background-size: 400% 400%;
  animation: gradient-morph 15s ease infinite;
}

@keyframes gradient-morph {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .brutalist-shadow {
    box-shadow: 4px 4px 0px 0px rgba(0, 0, 0, 0.1);
  }
  
  .floating {
    animation-duration: 4s;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .glass-card {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-card-light {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  .glass-card-dark {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .glass-card-primary {
    background: rgba(var(--primary-rgb), 0.1);
    border: 1px solid rgba(var(--primary-rgb), 0.3);
  }

  .brutalist-shadow {
    box-shadow: 8px 8px 0px 0px rgba(255, 255, 255, 0.1);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .floating,
  .gradient-shift,
  .pulse-glow,
  .shimmer,
  .gradient-morph {
    animation: none;
  }
  
  .hover-lift:hover {
    transform: none;
  }
}
