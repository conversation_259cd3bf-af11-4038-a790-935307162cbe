# Chat Components

<cite>
**Referenced Files in This Document**   
- [ChatPage.tsx](file://frontend/src/pages/ChatPage.tsx)
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx)
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx)
- [ChatHeader.tsx](file://frontend/src/components/chat/ChatHeader.tsx)
- [ChatPartners.tsx](file://frontend/src/components/chat/ChatPartners.tsx)
- [MessageSkeleton.tsx](file://frontend/src/components/chat/MessageSkeleton.tsx)
- [PartnerSkeleton.tsx](file://frontend/src/components/chat/PartnerSkeleton.tsx)
- [useChat.ts](file://frontend/src/hooks/useChat.ts)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts)
- [chat.ts](file://frontend/src/types/chat.ts)
- [message.ts](file://server/src/services/message.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
This document provides comprehensive documentation for the chat interface components in the takeNpass application. It details the architecture, component interactions, state management, and real-time communication patterns that power the messaging system. The documentation covers message rendering, input handling, partner display, WebSocket integration, and performance optimization strategies.

## Project Structure

```mermaid
graph TD
subgraph "frontend/src/components/chat"
CH[ChatHeader.tsx]
CI[ChatInput.tsx]
CP[ChatPartners.tsx]
MC[MainChat.tsx]
MI[MessageItem.tsx]
MS[MessageSkeleton.tsx]
PS[PartnerSkeleton.tsx]
end
subgraph "frontend/src/hooks"
UC[useChat.ts]
UMR[useMessageRead.ts]
end
subgraph "frontend/src/types"
CT[chat.ts]
end
subgraph "frontend/src/pages"
CP[ChatPage.tsx]
end
subgraph "server/src/services"
MS[message.ts]
end
CP --> MC
MC --> CH
MC --> CI
MC --> MI
MC --> MS
CP --> CP
CP --> PS
MC --> UC
MI --> UMR
UC --> CT
UMR --> CT
MI --> CT
MS --> CT
```

**Diagram sources**
- [ChatPage.tsx](file://frontend/src/pages/ChatPage.tsx)
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx)
- [useChat.ts](file://frontend/src/hooks/useChat.ts)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts)

**Section sources**
- [ChatPage.tsx](file://frontend/src/pages/ChatPage.tsx)
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx)

## Core Components

The chat module consists of several key components that work together to provide a seamless messaging experience. These include the main chat container, message rendering components, input handling, partner selection, and loading states.

**Section sources**
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx)
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx)

## Architecture Overview

The chat architecture follows a component-based design with clear separation of concerns. The system integrates real-time communication through WebSocket connections and implements efficient state management patterns for optimal performance.

```mermaid
sequenceDiagram
participant UI as ChatPage
participant Partners as ChatPartners
participant Main as MainChat
participant Hook as useChat
participant Message as MessageItem
participant ReadHook as useMessageRead
participant API as TRPC API
participant Server as Server
UI->>Partners : Render with initUserId
Partners->>API : Query message.partners
API-->>Partners : Return partner list
Partners->>UI : Set activeUser via callback
UI->>Main : Pass activeUser
Main->>Hook : Initialize with partnerId
Hook->>API : Query messagesByPartner
API-->>Hook : Return messages
Hook-->>Main : Provide chatState
Main->>Message : Render messages
Message->>ReadHook : Check visibility
ReadHook->>API : Mark message as read
API->>Server : Update read status
Server-->>API : Confirmation
API-->>ReadHook : Success
ReadHook-->>Message : Update UI
```

**Diagram sources**
- [ChatPage.tsx](file://frontend/src/pages/ChatPage.tsx)
- [useChat.ts](file://frontend/src/hooks/useChat.ts)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts)
- [message.ts](file://server/src/services/message.ts)

## Detailed Component Analysis

### MainChat Component Analysis

The MainChat component serves as the central container for the chat interface, orchestrating message display, input handling, and state management.

#### Component Structure
```mermaid
classDiagram
class MainChat {
+activeUser : ChatPartner
-t : TranslationFunction
-chatState : ChatState
-inputValue : string
-isLoading : boolean
-memoizedMessages : ChatMessage[]
-emptyState : ReactNode
-chatInputProps : ChatInputProps
-chatContainerRef : RefObject
}
class ChatHeader {
+partner : ChatPartner
+onBack : () => void
+showBackButton : boolean
}
class MessageItem {
+message : ChatMessage
+isOwn : boolean
+sessionUserId : string
+showAvatar : boolean
+showTimestamp : boolean
}
class ChatInput {
+value : string
+onChange : (value : string) => void
+onSend : () => void
+isLoading : boolean
+isDisabled : boolean
+placeholder : string
}
class MessageSkeleton {
+No props
}
MainChat --> ChatHeader : "renders"
MainChat --> MessageItem : "maps messages"
MainChat --> ChatInput : "passes props"
MainChat --> MessageSkeleton : "shows during loading"
MainChat --> useChat : "uses hook"
```

**Diagram sources**
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx)
- [ChatHeader.tsx](file://frontend/src/components/chat/ChatHeader.tsx)
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx)
- [MessageSkeleton.tsx](file://frontend/src/components/chat/MessageSkeleton.tsx)
- [useChat.ts](file://frontend/src/hooks/useChat.ts)

**Section sources**
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx)

### ChatInput Component Analysis

The ChatInput component handles user input for sending messages, providing both keyboard and button interaction methods.

#### Input Flow
```mermaid
flowchart TD
Start([Component Mount]) --> FocusCheck["Check autoFocus and isDisabled"]
FocusCheck --> |autoFocus enabled| SetFocus["Set focus to input"]
FocusCheck --> |autoFocus disabled| SkipFocus["Skip focus"]
SetFocus --> InputEvent["User types in input"]
SkipFocus --> InputEvent
InputEvent --> KeyDown["User presses key"]
KeyDown --> EnterCheck{"Key is Enter?"}
EnterCheck --> |No| ContinueTyping["Continue typing"]
EnterCheck --> |Yes| ShiftCheck{"Shift key pressed?"}
ShiftCheck --> |Yes| NewLine["Add new line"]
ShiftCheck --> |No| PreventDefault["Prevent default"]
PreventDefault --> CanSendCheck{"Can send message?"}
CanSendCheck --> |Yes| SendAction["Call onSend"]
CanSendCheck --> |No| Ignore["Ignore send"]
SendAction --> ClearInput["Clear input value"]
NewLine --> ContinueTyping
ContinueTyping --> KeyDown
style Start fill:#f9f,stroke:#333
style SendAction fill:#bbf,stroke:#333
```

**Diagram sources**
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx)

**Section sources**
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx)

### MessageItem Component Analysis

The MessageItem component renders individual messages with read status tracking and visibility detection.

#### Message Read Status Flow
```mermaid
flowchart TD
Start([Message Rendered]) --> RefSet["Set element ref"]
RefSet --> Observer["Initialize Intersection Observer"]
Observer --> VisibilityCheck["Check if message is visible"]
VisibilityCheck --> IsVisible{"Message visible?"}
IsVisible --> |No| WaitVisible["Wait for visibility"]
IsVisible --> |Yes| IsOwn{"Message is own?"}
IsOwn --> |Yes| SkipRead["Skip read status update"]
IsOwn --> |No| IsRead{"Message already read?"}
IsRead --> |Yes| SkipRead
IsRead --> |No| Processed{"Already processed?"}
Processed --> |Yes| SkipRead
Processed --> |No| MarkProcessed["Add to processed set"]
MarkProcessed --> APIRequest["Call setAsRead mutation"]
APIRequest --> Success{"Success?"}
Success --> |Yes| UpdateUI["Update message status"]
Success --> |No| HandleError["Log error, remove from processed"]
UpdateUI --> End([Complete])
HandleError --> End
style Start fill:#f9f,stroke:#333
style UpdateUI fill:#bbf,stroke:#333
style HandleError fill:#f96,stroke:#333
```

**Diagram sources**
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts)

**Section sources**
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts)

### ChatPartners Component Analysis

The ChatPartners component manages the display and selection of chat partners with real-time updates.

#### Partner Selection Flow
```mermaid
flowchart TD
Start([Component Mount]) --> QueryPartners["Query message.partners"]
QueryPartners --> RenderList["Render partner list"]
RenderList --> UserClick["User clicks partner"]
UserClick --> ChoosePartner["choosePartner function"]
ChoosePartner --> SetUser["Call setUser callback"]
SetUser --> Navigate["Navigate to /messages"]
Navigate --> UpdateSearch["Update search param partnerId"]
UpdateSearch --> End([Partner selected])
subgraph "Real-time Updates"
EventListener["Listen for NEW_MESSAGE event"]
EventListener --> CheckPartner["Check if partner exists"]
CheckPartner --> |Not exists| Refetch["Refetch partners"]
Refetch --> UpdateList["Update partner list"]
end
style Start fill:#f9f,stroke:#333
style End fill:#bbf,stroke:#333
```

**Diagram sources**
- [ChatPartners.tsx](file://frontend/src/components/chat/ChatPartners.tsx)

**Section sources**
- [ChatPartners.tsx](file://frontend/src/components/chat/ChatPartners.tsx)

## Dependency Analysis

The chat components have a well-defined dependency structure that ensures proper data flow and component interaction.

```mermaid
graph TD
ChatPage --> MainChat
ChatPage --> ChatPartners
MainChat --> ChatHeader
MainChat --> MessageItem
MainChat --> ChatInput
MainChat --> MessageSkeleton
MessageItem --> useMessageRead
MainChat --> useChat
useChat --> trpc.message.messagesByPartner
useChat --> trpc.message.send
useMessageRead --> trpc.message.setAsRead
ChatPartners --> trpc.message.partners
ChatPartners --> trpc.message.getPartnerById
style ChatPage fill:#4CAF50,stroke:#333
style MainChat fill:#2196F3,stroke:#333
style ChatPartners fill:#2196F3,stroke:#333
style useChat fill:#FF9800,stroke:#333
style useMessageRead fill:#FF9800,stroke:#333
```

**Diagram sources**
- [ChatPage.tsx](file://frontend/src/pages/ChatPage.tsx)
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx)
- [ChatPartners.tsx](file://frontend/src/components/chat/ChatPartners.tsx)
- [useChat.ts](file://frontend/src/hooks/useChat.ts)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts)

**Section sources**
- [ChatPage.tsx](file://frontend/src/pages/ChatPage.tsx)
- [useChat.ts](file://frontend/src/hooks/useChat.ts)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts)

## Performance Considerations

The chat interface implements several performance optimizations to ensure smooth operation even with large message threads.

### Memoization Strategy
The components use React's `useMemo` and `memo` to prevent unnecessary re-renders:

- **MainChat**: Memoizes messages array and loading skeletons
- **MessageItem**: Wrapped with `memo` to prevent re-renders when parent updates
- **ChatInput**: Memoizes props passed from MainChat
- **useChat**: Memoizes chat state and actions

### Virtualization and Scrolling
The chat interface uses ScrollShadow component with the following optimizations:
- Limits message container height to 60vh
- Hides scrollbar for cleaner UI
- Auto-scrolls to bottom on new messages
- Uses ref to maintain scroll position

### Loading States
Skeleton loading states are implemented for:
- Message items (MessageSkeleton)
- Partner list items (PartnerSkeleton)
- Conditional rendering based on loading state

**Section sources**
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx)
- [MessageSkeleton.tsx](file://frontend/src/components/chat/MessageSkeleton.tsx)
- [PartnerSkeleton.tsx](file://frontend/src/components/chat/PartnerSkeleton.tsx)
- [useChat.ts](file://frontend/src/hooks/useChat.ts)

## Troubleshooting Guide

### Common Issues and Solutions

#### Messages Not Marking as Read
**Symptoms**: Messages remain in "delivered" state, not updating to "read"
**Possible Causes**:
- Message element not visible in viewport
- Intersection Observer not triggering
- Network issues with setAsRead mutation
- Session user ID not properly passed

**Solutions**:
1. Verify the message is actually visible in the viewport
2. Check browser console for errors in useMessageRead hook
3. Ensure sessionUserId is correctly passed to MessageItem
4. Test network connection and API endpoint

#### Input Not Focusing
**Symptoms**: Chat input doesn't automatically focus when opening chat
**Possible Causes**:
- autoFocus prop set to false
- Component not fully mounted when focus is attempted
- isDisabled prop preventing focus

**Solutions**:
1. Ensure autoFocus is true (default behavior)
2. Verify activeUser is set, enabling the input
3. Check for any errors in component mounting

#### Partner List Not Updating
**Symptoms**: New messages from unknown partners don't appear in the list
**Possible Causes**:
- NEW_MESSAGE event not triggering refetch
- initUserId not properly handled
- Cache issues with TRPC queries

**Solutions**:
1. Verify useBus is properly listening for NEW_MESSAGE events
2. Check that refetch is called in the event handler
3. Test with browser cache disabled

**Section sources**
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx)
- [ChatPartners.tsx](file://frontend/src/components/chat/ChatPartners.tsx)

## Conclusion

The chat interface in takeNpass provides a robust, real-time messaging system with thoughtful performance optimizations and user experience considerations. The component architecture follows React best practices with proper separation of concerns, memoization for performance, and clear data flow patterns. The integration with WebSocket through TRPC enables real-time updates, while the useMessageRead hook provides reliable read status tracking. The skeleton loading states enhance perceived performance, and the responsive design ensures usability across device sizes. This documentation provides a comprehensive understanding of the chat components, enabling effective maintenance and future enhancements.