import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserCreateWithoutSendedNotificationsInputSchema } from './UserCreateWithoutSendedNotificationsInputSchema';
import { UserUncheckedCreateWithoutSendedNotificationsInputSchema } from './UserUncheckedCreateWithoutSendedNotificationsInputSchema';
import { UserCreateOrConnectWithoutSendedNotificationsInputSchema } from './UserCreateOrConnectWithoutSendedNotificationsInputSchema';
import { UserUpsertWithoutSendedNotificationsInputSchema } from './UserUpsertWithoutSendedNotificationsInputSchema';
import { UserWhereInputSchema } from './UserWhereInputSchema';
import { UserWhereUniqueInputSchema } from './UserWhereUniqueInputSchema';
import { UserUpdateToOneWithWhereWithoutSendedNotificationsInputSchema } from './UserUpdateToOneWithWhereWithoutSendedNotificationsInputSchema';
import { UserUpdateWithoutSendedNotificationsInputSchema } from './UserUpdateWithoutSendedNotificationsInputSchema';
import { UserUncheckedUpdateWithoutSendedNotificationsInputSchema } from './UserUncheckedUpdateWithoutSendedNotificationsInputSchema';

export const UserUpdateOneWithoutSendedNotificationsNestedInputSchema: z.ZodType<Prisma.UserUpdateOneWithoutSendedNotificationsNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutSendedNotificationsInputSchema),z.lazy(() => UserUncheckedCreateWithoutSendedNotificationsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutSendedNotificationsInputSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutSendedNotificationsInputSchema).optional(),
  disconnect: z.union([ z.boolean(),z.lazy(() => UserWhereInputSchema) ]).optional(),
  delete: z.union([ z.boolean(),z.lazy(() => UserWhereInputSchema) ]).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => UserUpdateToOneWithWhereWithoutSendedNotificationsInputSchema),z.lazy(() => UserUpdateWithoutSendedNotificationsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutSendedNotificationsInputSchema) ]).optional(),
}).strict();

export default UserUpdateOneWithoutSendedNotificationsNestedInputSchema;
