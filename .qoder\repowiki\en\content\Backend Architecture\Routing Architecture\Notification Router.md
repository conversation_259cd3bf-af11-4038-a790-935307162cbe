# Notification Router

<cite>
**Referenced Files in This Document**  
- [notification.ts](file://server/src/router/routers/notification.ts)
- [notification.ts](file://server/src/services/notification.ts)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx)
- [MessagesWidget.tsx](file://frontend/src/components/MessagesWidget.tsx)
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [tRPC Procedures Overview](#trpc-procedures-overview)
3. [Notification Types and Structure](#notification-types-and-structure)
4. [Integration with NotificationService](#integration-with-notificationservice)
5. [WebSocket Real-Time Updates](#websocket-real-time-updates)
6. [Frontend Consumption: Notifications.tsx](#frontend-consumption-notificationstsx)
7. [Frontend Consumption: MessagesWidget.tsx](#frontend-consumption-messageswidgettsx)
8. [Unread Count and Pagination](#unread-count-and-pagination)
9. [Batch Operations and State Management](#batch-operations-and-state-management)
10. [Conclusion](#conclusion)

## Introduction
The Notification Router is a core component of the application responsible for managing user notifications through tRPC endpoints. It enables retrieval, marking as read, and real-time delivery of various notification types such as case updates, messages, and system alerts. This document details the implementation, integration points, and frontend usage patterns for the notification system.

## tRPC Procedures Overview
The notification router exposes three primary tRPC procedures:
- **list**: Retrieves all notifications for the authenticated user, ordered by creation time (descending), limited to 50 records.
- **markAsRead**: Marks a specific notification as read using its ID.
- **markAllAsRead**: Batch operation that marks all unread notifications as read for the current user.

These procedures are protected by `authedProcedure`, ensuring only authenticated users can access them. Input validation is enforced via Zod, particularly for the `markAsRead` mutation which expects a numeric ID.

**Section sources**
- [notification.ts](file://server/src/router/routers/notification.ts#L6-L25)

## Notification Types and Structure
The system supports several notification types defined in `SocketNotificationType`:
- `NEW_REQUEST`: Triggered when a new request is sent to a user's case.
- `CANCEL_REQUEST`: Notifies when a request is canceled.
- `NEW_CASES_ON_SUBSCRIBE`: Alerts users about new cases matching their subscription criteria.
- `NEW_REVIEW`: Informs users they have received a new review.
- `NEW_MESSAGE`: Indicates a new message has been received from another user.

Each notification includes structured data such as sender, recipient, associated case (if applicable), translation key, and localized message content based on user language preferences.

```mermaid
flowchart TD
A["Notification Type"] --> B["NEW_REQUEST"]
A --> C["CANCEL_REQUEST"]
A --> D["NEW_CASES_ON_SUBSCRIBE"]
A --> E["NEW_REVIEW"]
A --> F["NEW_MESSAGE"]
B --> G["Includes: user, sender, case"]
C --> G
D --> H["Includes: user, case"]
E --> I["Includes: user?, sender?, case?"]
F --> J["Includes: user, sender, content"]
```

**Diagram sources**
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts#L2-L48)

## Integration with NotificationService
The `NotificationService` class serves as the business logic layer for all notification operations. It interacts directly with the Prisma ORM to perform CRUD operations on the `Notification` model. Key methods include:
- `getNotificationsForUser`: Fetches notifications with related entities (sender, user, case).
- `markNotificationAsRead`: Updates a single notification's read status.
- `markAllNotificationAsRead`: Performs bulk update of unread notifications.
- `getUnreadNotificationsForUser`: Returns only unread notifications.
- `createNotification`: Internal method used to generate and emit new notifications.

All service methods include proper type safety and error handling, particularly around required user context.

**Section sources**
- [notification.ts](file://server/src/services/notification.ts#L22-L197)

## WebSocket Real-Time Updates
Real-time notification delivery is achieved through WebSocket integration using the `IO` provider. When a new notification is created via `createNotification`, it is immediately emitted to the target user's socket connection under the event name `xnotification`. The payload conforms to the `SocketNotification` interface, containing both structured data and a pre-translated message.

The frontend listens for these events in `Notifications.tsx`, triggering a refetch of the notification list and displaying toast notifications for incoming messages. Special handling exists for `NEW_MESSAGE` events, which also dispatch global events for chat UI updates.

```mermaid
sequenceDiagram
participant Backend
participant NotificationService
participant WebSocket
participant Frontend
Backend->>NotificationService : createNotification(type, data)
NotificationService->>NotificationService : Create DB record
NotificationService->>WebSocket : Emit "xnotification" event
WebSocket->>Frontend : Send notification payload
Frontend->>Frontend : Show toast, update badge count
```

**Diagram sources**
- [notification.ts](file://server/src/services/notification.ts#L22-L50)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L120-L140)

## Frontend Consumption: Notifications.tsx
The `Notifications` component renders a badge with unread count and a sidebar drawer for viewing all notifications. It uses tRPC hooks to:
- Query notifications via `trpc.notification.list.useQuery`
- Mutate state via `markAsRead` and `markAllAsRead` mutations

The component implements tabbed navigation between "Unread" and "Read" notifications, with conditional rendering of action buttons. Template functions render different UIs based on notification type, and navigation links direct users to relevant pages (e.g., `/messages`, `/reviews`).

Real-time updates are handled by subscribing to the `xnotification` WebSocket event, which triggers automatic refetching and toast display.

**Section sources**
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L1-L323)

## Frontend Consumption: MessagesWidget.tsx
The `MessagesWidget` component displays an unread message count badge on the message icon. It uses `trpc.message.unreadCount.useQuery` to fetch the count from the backend. Clicking the button navigates to the `/messages` page using TanStack Router.

Unlike the main notifications system, this widget focuses specifically on message-related notifications and does not handle marking as read—this occurs within the messages interface itself.

**Section sources**
- [MessagesWidget.tsx](file://frontend/src/components/MessagesWidget.tsx#L1-L34)

## Unread Count and Pagination
Unread notification counts are calculated client-side in `Notifications.tsx` by filtering the fetched data array. The count is capped at "99+" for display purposes when exceeding 100. Server-side, the notification list is paginated to 50 most recent items, though infinite scrolling could be implemented by extending the service with cursor-based pagination.

The unread count badge updates automatically when new notifications arrive via WebSocket or when mutations change read status.

```mermaid
flowchart LR
A["Fetch Notifications"] --> B["Filter unread"]
B --> C["Count = data.filter(n => !n.read).length"]
C --> D["Display: count > 100 ? '99+' : count"]
```

**Diagram sources**
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L55-L60)

## Batch Operations and State Management
Batch operations are supported through the `markAllAsRead` mutation, which performs a bulk database update. The frontend triggers this when the user clicks "Mark all as read" in the unread tab.

State management is handled using React hooks:
- `useState` for local UI state (sidebar visibility, tab selection)
- tRPC's built-in query/mutation hooks for server state synchronization
- `useEffect` for side effects like WebSocket subscription and count calculation

Optimistic updates could be implemented to improve perceived performance during mutation operations.

**Section sources**
- [notification.ts](file://server/src/router/routers/notification.ts#L19-L25)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L75-L85)

## Conclusion
The Notification Router provides a robust, real-time notification system integrated across backend and frontend layers. By leveraging tRPC for type-safe APIs, Prisma for data access, and WebSockets for instant delivery, it ensures users stay informed about important events. The modular design allows easy extension with new notification types and improved pagination strategies.