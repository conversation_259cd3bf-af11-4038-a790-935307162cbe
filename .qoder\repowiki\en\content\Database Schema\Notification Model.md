# Notification Model

<cite>
**Referenced Files in This Document**   
- [notification.ts](file://server/src/services/notification.ts)
- [NotificationSchema.ts](file://server/prisma/generated/zod/modelSchema/NotificationSchema.ts)
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts)
- [types.ts](file://frontend/src/types.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Fields](#core-fields)
3. [Relationships](#relationships)
4. [Polymorphic Nature and Notification Types](#polymorphic-nature-and-notification-types)
5. [Read Status Management](#read-status-management)
6. [Indexing and Query Performance](#indexing-and-query-performance)
7. [Notification Lifecycle and Archival](#notification-lifecycle-and-archival)
8. [User Preferences and Notification Settings](#user-preferences-and-notification-settings)
9. [Sample Queries](#sample-queries)
10. [Data Model Diagram](#data-model-diagram)

## Introduction
The Notification entity in the takeNpass system is responsible for managing user alerts and real-time communication events. It supports various types of user-facing messages such as request updates, case changes, and new messages. The model is designed to be lightweight, efficient, and scalable, with support for internationalization through translation keys and real-time delivery via WebSocket. This document provides a comprehensive overview of the Notification data model, its relationships, behavior, and usage patterns.

**Section sources**
- [notification.ts](file://server/src/services/notification.ts#L1-L198)
- [NotificationSchema.ts](file://server/prisma/generated/zod/modelSchema/NotificationSchema.ts#L1-L115)

## Core Fields
The Notification model contains the following core fields that define its structure and behavior:

- **id**: Unique numeric identifier for the notification (auto-incremented)
- **createdAt**: Timestamp when the notification was created (automatically set)
- **userId**: Identifier of the recipient user (required)
- **read**: Boolean flag indicating whether the notification has been viewed (default: false)
- **caseId**: Optional reference to a Case entity that provides context
- **link**: Optional URL link associated with the notification
- **senderId**: Optional identifier of the user who triggered the notification
- **translationKey**: String key used to resolve localized message content

These fields are defined in the Prisma schema and validated using Zod through the `NotificationSchema`. The `translationKey` enables dynamic message generation based on user language preferences without storing redundant text.

**Section sources**
- [NotificationSchema.ts](file://server/prisma/generated/zod/modelSchema/NotificationSchema.ts#L21-L29)
- [notification.ts](file://server/src/services/notification.ts#L3-L30)

## Relationships
The Notification entity maintains several key relationships with other system entities:

- **User (recipient)**: Every notification is associated with a recipient user via `userId`. The relationship is mandatory and ensures notifications are always tied to a valid user.
- **Sender (User)**: Optional relationship via `senderId`, representing the user who initiated the action that triggered the notification.
- **Case**: Optional relationship via `caseId`, providing contextual information for notifications related to specific cases.

These relationships are eagerly loaded in service methods using Prisma's `include` syntax, allowing efficient access to related data without N+1 query issues.

```mermaid
erDiagram
USER {
string id PK
string email
string username
string language
boolean confirmed
string avatar
}
CASE {
string id PK
string title
string description
string status
string ownerId FK
}
NOTIFICATION {
number id PK
timestamp createdAt
string userId FK
string senderId FK
string caseId FK
boolean read
string translationKey
string link
}
USER ||--o{ NOTIFICATION : "receives"
USER ||--o{ NOTIFICATION : "sends" as sender
CASE ||--o{ NOTIFICATION : "context for"
```

**Diagram sources**
- [NotificationSchema.ts](file://server/prisma/generated/zod/modelSchema/NotificationSchema.ts#L70-L114)
- [notification.ts](file://server/src/services/notification.ts#L45-L60)

## Polymorphic Nature and Notification Types
Notifications in the system are polymorphic, meaning they can represent different types of events using a common structure. The type is determined by the `translationKey` field, which maps to specific event types defined in `SocketNotificationType`. Supported notification types include:

- **NEW_REQUEST**: Notifies a user of a new request from another user on a case
- **CANCEL_REQUEST**: Indicates that a previously sent request has been canceled
- **NEW_CASES_ON_SUBSCRIBE**: Alerts users of new cases matching their subscription criteria
- **NEW_REVIEW**: Notifies when a new review has been posted
- **NEW_MESSAGE**: Signals the arrival of a new message in a conversation

Each type carries a specific payload structure with relevant data such as user, sender, case, and content. This design enables flexible event handling while maintaining a consistent notification interface.

**Section sources**
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts#L2-L27)
- [notification.ts](file://server/src/services/notification.ts#L4-L20)

## Read Status Management
The system provides comprehensive read/unread state management through the `read` boolean field. Key operations include:

- **Mark as Read (Single)**: Users can mark individual notifications as read using `markNotificationAsRead`
- **Mark All as Read**: Users can clear all unread notifications at once via `markAllNotificationAsRead`
- **Unread Querying**: The system can efficiently retrieve all unread notifications for a user using `getUnreadNotificationsForUser`

These operations are optimized for performance and atomicity, ensuring consistent state across the system. The read status is updated directly in the database and does not require complex transactions.

**Section sources**
- [notification.ts](file://server/src/services/notification.ts#L100-L150)

## Indexing and Query Performance
To ensure efficient notification retrieval, the system leverages database indexing strategies focused on the most common query patterns:

- **User-based Indexing**: An index on `userId` enables fast retrieval of all notifications for a specific user
- **Read Status Filtering**: Combined index on `userId` and `read` supports efficient unread notification queries
- **Temporal Ordering**: Index on `createdAt` allows for chronological sorting without full table scans

The `getNotificationsForUser` method retrieves up to 50 most recent notifications ordered by creation time, implementing pagination-ready behavior. All queries include related entities (user, sender, case) to minimize round trips.

**Section sources**
- [notification.ts](file://server/src/services/notification.ts#L65-L95)

## Notification Lifecycle and Archival
Notifications follow a simple lifecycle from creation to potential deletion:

1. **Creation**: Triggered by system events (e.g., new request, message) via `createNotification`
2. **Delivery**: Immediately pushed to the recipient's WebSocket connection if online
3. **Reading**: Status changes from unread to read upon user interaction
4. **Retention**: Notifications are retained indefinitely unless explicitly deleted
5. **Deletion**: Individual notifications can be removed via `deleteNotification`

There is currently no automated archival or expiration policy; notifications persist until manually deleted. This design prioritizes user access to historical alerts.

**Section sources**
- [notification.ts](file://server/src/services/notification.ts#L35-L60)
- [notification.ts](file://server/src/services/notification.ts#L155-L170)

## User Preferences and Notification Settings
While the current implementation does not include a dedicated `NotificationSettings` entity in the provided code, the architecture supports user preference integration through:

- **Language Preferences**: The recipient's `language` field is used to localize notification messages via i18next
- **Selective Field Inclusion**: The service selectively includes user fields (username, avatar, etc.) based on notification context
- **Real-time Delivery**: WebSocket integration allows for immediate push notifications based on user connectivity

Future extensions could introduce granular notification preferences (e.g., opt-out of certain types, delivery methods) through a dedicated settings model.

**Section sources**
- [notification.ts](file://server/src/services/notification.ts#L50-L60)
- [notification.ts](file://server/src/services/notification.ts#L40-L45)

## Sample Queries
The following are example queries that demonstrate common use cases:

**Get Unread Notification Count**
```sql
SELECT COUNT(*) FROM Notification 
WHERE userId = 'user-123' AND read = false;
```

**Retrieve Recent Notifications**
```sql
SELECT * FROM Notification 
WHERE userId = 'user-123' 
ORDER BY createdAt DESC 
LIMIT 50;
```

**Get Notifications by Case Context**
```sql
SELECT * FROM Notification 
WHERE caseId = 'case-456' AND userId = 'user-123';
```

**Mark All Notifications as Read**
```sql
UPDATE Notification 
SET read = true 
WHERE userId = 'user-123' AND read = false;
```

These patterns are implemented in the service layer and abstracted from direct SQL usage.

**Section sources**
- [notification.ts](file://server/src/services/notification.ts#L65-L95)
- [notification.ts](file://server/src/services/notification.ts#L100-L130)

## Data Model Diagram
The following diagram illustrates the Notification entity and its relationships within the system:

```mermaid
erDiagram
USER {
string id PK
string email
string username
string language
boolean confirmed
string avatar
}
CASE {
string id PK
string title
string description
string status
string ownerId FK
}
NOTIFICATION {
number id PK
timestamp createdAt
string userId FK
string senderId FK
string caseId FK
boolean read
string translationKey
string link
}
USER ||--o{ NOTIFICATION : "receives"
USER ||--o{ NOTIFICATION : "sends" as sender
CASE ||--o{ NOTIFICATION : "context for"
```

**Diagram sources**
- [NotificationSchema.ts](file://server/prisma/generated/zod/modelSchema/NotificationSchema.ts#L70-L114)
- [notification.ts](file://server/src/services/notification.ts#L45-L60)
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts#L2-L27)