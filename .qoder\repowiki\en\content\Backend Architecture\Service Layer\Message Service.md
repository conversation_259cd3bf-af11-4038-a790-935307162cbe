# Message Service

<cite>
**Referenced Files in This Document**   
- [message.ts](file://server/src/services/message.ts)
- [message.ts](file://server/src/router/routers/message.ts)
- [schema.prisma](file://server/prisma/schema.prisma)
- [ws.ts](file://server/src/providers/ws.ts)
- [useChat.ts](file://frontend/src/hooks/useChat.ts)
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx)
- [ChatPage.tsx](file://frontend/src/pages/ChatPage.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Domain Model](#domain-model)
3. [Service Interfaces](#service-interfaces)
4. [Real-Time Communication](#real-time-communication)
5. [Message Persistence](#message-persistence)
6. [Read Status Tracking](#read-status-tracking)
7. [Frontend Integration](#frontend-integration)
8. [Common Issues and Solutions](#common-issues-and-solutions)
9. [Conclusion](#conclusion)

## Introduction
The Message Service in the takeNpass backend provides a robust foundation for real-time chat functionality, message persistence, and read status tracking. This document details the implementation of the messaging system, focusing on the core components that enable users to exchange messages, maintain conversation history, and track message delivery and read status. The service integrates with Prisma ORM for database operations, WebSocket provider for real-time updates, and frontend chat components for user interaction. Designed to be accessible to beginners while offering technical depth for experienced developers, this documentation covers the domain model, service interfaces, real-time communication mechanisms, and solutions to common distributed system challenges.

**Section sources**
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [schema.prisma](file://server/prisma/schema.prisma#L200-L220)

## Domain Model
The domain model for the messaging system is centered around the Message entity, which captures the essential attributes of a chat message including sender, receiver, content, timestamp, and read status. The model establishes relationships between users and their messages through foreign key references, enabling efficient querying of conversation histories and participant information. Each message is timestamped upon creation, ensuring chronological ordering, and includes a boolean flag to track whether it has been read by the recipient. The model supports various message types through the content field, which can store text, metadata, or structured data for different communication scenarios.

```mermaid
erDiagram
User {
string id PK
string username
string email
string language
timestamp createdAt
timestamp updatedAt
}
Message {
int id PK
timestamp createdAt
string content
string senderId FK
string receiverId FK
boolean read
}
User ||--o{ Message : "sends"
User ||--o{ Message : "receives"
```

**Diagram sources**
- [schema.prisma](file://server/prisma/schema.prisma#L200-L220)

**Section sources**
- [schema.prisma](file://server/prisma/schema.prisma#L200-L220)
- [message.ts](file://server/src/services/message.ts#L1-L263)

## Service Interfaces
The Message Service exposes a set of well-defined interfaces for managing chat operations, including sending messages, retrieving conversation histories, and updating message read status. These interfaces are implemented as static methods in the MessageService class and are exposed through tRPC routers for secure, type-safe communication between the frontend and backend. The service methods accept parameters such as sender and receiver IDs, message content, and message IDs, and return appropriate responses or throw errors for invalid operations. Input validation is performed using Zod schemas to ensure data integrity and prevent malformed requests.

### sendMessage
The `sendMessage` method creates a new message in the database with the specified sender, receiver, and content. It returns the ID of the created message or throws an error if the operation fails.

**Parameters:**
- `receiverId`: string - ID of the message recipient
- `senderId`: string - ID of the message sender
- `content`: string - Message content (minimum length 1)

**Return Value:**
- `number` - ID of the created message

### getMessages
The `messagesByPartner` method retrieves all messages exchanged between two users, ordered by creation time in ascending order. It includes sender and receiver information with avatars for display purposes.

**Parameters:**
- `userId`: string - ID of the current user
- `partnerId`: string - ID of the conversation partner

**Return Value:**
- `Message[]` - Array of message objects with sender and receiver details

### markAsRead
The `setAsRead` method updates the read status of a specific message to true, indicating that the recipient has viewed it.

**Parameters:**
- `userId`: string - ID of the user marking the message as read
- `messageId`: number - ID of the message to mark as read

**Return Value:**
- `void`

```mermaid
classDiagram
class MessageService {
+static send(receiverId : string, senderId : string, content : string) Promise~number~
+static messagesByPartner(userId : string, partnerUserId : string) Promise~Message[]~
+static setAsRead(userId : string, messageId : number) Promise~void~
+static unreadCount(userId : string) Promise~number~
+static partners(userId : string) Promise~User[]~
}
class MessageRouter {
+send : authedProcedure.input(SendInputShema).mutation()
+messagesByPartner : authedProcedure.input(z.string()).query()
+setAsRead : authedProcedure.input(z.object({ messageId : z.number() })).mutation()
+unreadCount : authedProcedure.query()
+partners : authedProcedure.query()
}
MessageService --> MessageRouter : "implements"
```

**Diagram sources**
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [message.ts](file://server/src/router/routers/message.ts#L1-L70)

**Section sources**
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [message.ts](file://server/src/router/routers/message.ts#L1-L70)

## Real-Time Communication
Real-time communication in the takeNpass application is facilitated by a WebSocket provider that establishes persistent connections between clients and the server. When a user logs in, their WebSocket connection is registered with their user ID, allowing the server to target specific users for message delivery. The WebSocket provider listens for new message events and broadcasts them to the appropriate recipients, enabling instant message delivery without the need for polling. This architecture ensures that users receive messages in real-time, enhancing the chat experience with immediate feedback and reducing latency.

```mermaid
sequenceDiagram
participant Frontend
participant Backend
participant WebSocketProvider
Frontend->>Backend : HTTP Request (Login)
Backend->>WebSocketProvider : Establish Connection
WebSocketProvider->>Backend : Store Connection (User ID)
Frontend->>Backend : Send Message (tRPC)
Backend->>Database : Save Message
Backend->>WebSocketProvider : Notify Recipient
WebSocketProvider->>Frontend : Emit Message Event
Frontend->>Frontend : Update UI with New Message
```

**Diagram sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [message.ts](file://server/src/services/message.ts#L1-L263)

**Section sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [message.ts](file://server/src/services/message.ts#L1-L263)

## Message Persistence
Message persistence is handled by the Prisma ORM, which provides a type-safe interface for database operations. The Message model is defined in the Prisma schema with fields for content, sender and receiver IDs, creation timestamp, and read status. Indexes are created on sender and receiver IDs to optimize query performance for retrieving conversation histories. The MessageService class uses Prisma's query methods to create, read, and update messages, ensuring data consistency and integrity. Messages are stored in a MySQL database, providing durability and scalability for the messaging system.

```mermaid
flowchart TD
A[Frontend] --> |tRPC Request| B[MessageService]
B --> C[Prisma Client]
C --> D[MySQL Database]
D --> C
C --> B
B --> |Response| A
subgraph "Database Operations"
C --> E[Create Message]
C --> F[Find Messages]
C --> G[Update Message]
end
```

**Diagram sources**
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [schema.prisma](file://server/prisma/schema.prisma#L200-L220)

**Section sources**
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [schema.prisma](file://server/prisma/schema.prisma#L200-L220)

## Read Status Tracking
Read status tracking is implemented through a boolean field in the Message model that indicates whether a message has been viewed by the recipient. The `setAsRead` method updates this field when a user opens a conversation, providing visual feedback to the sender about message delivery and read status. The service also provides an `unreadCount` method that returns the number of unread messages for a user, enabling the frontend to display notification badges. This feature enhances user experience by providing clear indicators of message status and helping users manage their conversations effectively.

```mermaid
classDiagram
class Message {
+int id
+DateTime createdAt
+string content
+string senderId
+string receiverId
+boolean read
}
class MessageService {
+static setAsRead(userId : string, messageId : number) Promise~void~
+static unreadCount(userId : string) Promise~number~
}
MessageService --> Message : "updates"
MessageService --> Message : "queries"
```

**Diagram sources**
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [schema.prisma](file://server/prisma/schema.prisma#L200-L220)

**Section sources**
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [schema.prisma](file://server/prisma/schema.prisma#L200-L220)

## Frontend Integration
The frontend integration of the Message Service is achieved through tRPC queries and mutations that connect React components to backend endpoints. The `useChat` hook encapsulates the logic for fetching messages, sending new messages, and managing chat state, providing a clean interface for UI components. The `MainChat` component uses this hook to display conversation histories and handle user input, while the `ChatPartners` component shows a list of conversation partners with unread message counts. This architecture separates concerns between data fetching, state management, and UI rendering, making the codebase maintainable and scalable.

```mermaid
flowchart LR
A[ChatPage] --> B[ChatPartners]
A --> C[MainChat]
B --> D[useChat]
C --> D
D --> E[tRPC Queries]
E --> F[MessageService]
subgraph "State Management"
D --> G[useState]
D --> H[useRef]
end
```

**Diagram sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L30)
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx#L1-L132)
- [ChatPage.tsx](file://frontend/src/pages/ChatPage.tsx#L1-L39)

**Section sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L30)
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx#L1-L132)
- [ChatPage.tsx](file://frontend/src/pages/ChatPage.tsx#L1-L39)

## Common Issues and Solutions
The messaging system addresses several common challenges in distributed systems, particularly around message ordering and consistency. One key issue is ensuring that messages are displayed in the correct chronological order, even when network latency causes out-of-order delivery. This is solved by relying on server-generated timestamps rather than client timestamps, ensuring a consistent ordering across all clients. Another challenge is handling concurrent updates to message read status, which is mitigated by using database transactions and optimistic locking. The system also addresses the problem of message duplication by using unique identifiers and idempotent operations.

```mermaid
flowchart TD
A[Client 1] --> |Send Message| B[Server]
C[Client 2] --> |Send Message| B
B --> D[Database]
D --> E[Order by createdAt]
E --> F[Client 1]
E --> G[Client 2]
subgraph "Consistency Challenges"
H[Network Latency] --> I[Out-of-Order Delivery]
J[Clock Skew] --> K[Incorrect Timestamps]
L[Concurrent Updates] --> M[Data Corruption]
end
subgraph "Solutions"
I --> N[Server-Generated Timestamps]
K --> N
M --> O[Database Transactions]
end
```

**Diagram sources**
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [schema.prisma](file://server/prisma/schema.prisma#L200-L220)

**Section sources**
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [schema.prisma](file://server/prisma/schema.prisma#L200-L220)

## Conclusion
The Message Service in the takeNpass backend provides a comprehensive solution for real-time chat functionality, message persistence, and read status tracking. By leveraging Prisma ORM for database operations, WebSocket provider for real-time communication, and tRPC for type-safe API endpoints, the service delivers a robust and scalable messaging system. The domain model captures the essential attributes of chat messages and their relationships, while the service interfaces provide a clean and consistent API for frontend integration. The implementation addresses common distributed system challenges, ensuring message ordering, consistency, and reliability. This documentation serves as a comprehensive guide for developers working with the messaging system, providing both high-level overviews and detailed technical insights.