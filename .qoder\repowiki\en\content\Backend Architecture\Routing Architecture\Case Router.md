# Case Router

<cite>
**Referenced Files in This Document**   
- [case.ts](file://server/src/router/routers/case.ts)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts)
- [case.ts](file://server/src/services/case.ts)
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx)
- [CasePage.tsx](file://frontend/src/pages/CasePage.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [tRPC Procedures Overview](#trpc-procedures-overview)
3. [Input Validation with Zod](#input-validation-with-zod)
4. [Frontend Integration](#frontend-integration)
5. [Case Service and Business Logic](#case-service-and-business-logic)
6. [Authentication and Authorization](#authentication-and-authorization)
7. [Query Procedures and Filtering](#query-procedures-and-filtering)
8. [Error Handling Patterns](#error-handling-patterns)
9. [Conclusion](#conclusion)

## Introduction
The Case Router provides a comprehensive API for managing delivery cases in the takeNpass application. Built on tRPC, it enables secure and type-safe interactions between the frontend and backend for creating, retrieving, updating, and deleting cases. The router serves as an interface between the frontend components and the underlying CaseService, which handles all business logic related to case management. This document details the implementation of the Case Router, including its procedures, validation schemas, integration with frontend components, and the delegation of business logic to the CaseService.

**Section sources**
- [case.ts](file://server/src/router/routers/case.ts)

## tRPC Procedures Overview
The Case Router exposes several tRPC procedures that enable full CRUD operations on cases. The `updateOrCreate` mutation handles both creation and updating of cases, accepting a payload that includes route details, pricing, and status information. This single endpoint simplifies the API surface by combining two related operations. The router also provides query procedures for retrieving case data: `list` for public case searches, `myList` for user-specific cases (created, assigned, favorites, or subscriptions), and `id` for retrieving a specific case by identifier. Additional mutation procedures include `assignClient` for expressing interest in a case and `toFavofite` for managing case favorites. All authenticated procedures use the `authedProcedure` wrapper, ensuring that only authenticated users can perform these operations.

```mermaid
sequenceDiagram
participant Frontend
participant TRPCRouter
participant CaseService
participant Database
Frontend->>TRPCRouter : updateOrCreate(input)
TRPCRouter->>TRPCRouter : validate input with Zod
TRPCRouter->>TRPCRouter : check user authentication
TRPCRouter->>CaseService : create/update(payload)
CaseService->>Database : Prisma operations
Database-->>CaseService : case data
CaseService-->>TRPCRouter : formatted case
TRPCRouter-->>Frontend : success response
```

**Diagram sources**
- [case.ts](file://server/src/router/routers/case.ts)
- [case.ts](file://server/src/services/case.ts)

**Section sources**
- [case.ts](file://server/src/router/routers/case.ts)

## Input Validation with Zod
The Case Router employs Zod for robust input validation, ensuring data integrity across the application. The `CaseCreateSchema` defines the structure for case creation, including optional description, baggage array, price, request status, expiration date, and route points (from, to, and middlepoints). Each waypoint contains geographical metadata, coordinates, date, and comment. The `CaseListParamsSchema` governs search parameters, allowing filtering by geographical coordinates, pagination (page and limit), search radius, date ranges, request status, and language preferences. These schemas are imported from the `CaseShemas.ts` file and applied directly to the tRPC procedure inputs, providing compile-time type safety and runtime validation. The validation ensures that all incoming data conforms to expected formats before being processed by the CaseService.

```mermaid
classDiagram
class CaseCreateSchema {
+description : string?
+baggage : string[]
+price : number?
+isRequest : boolean?
+expire_at : Date?
+from : WayPointSchema
+to : WayPointSchema
+middlepoints : WayPointSchema[]
}
class WayPointSchema {
+id : number?
+geometa : GeoMetaSchema
+date : Date?
+lat : number|string
+lon : number|string
+comment : string?
}
class GeoMetaSchema {
+address : object
+lat : number|string
+lon : number|string
+display_name : string?
+type : string?
}
class CaseListParamsSchema {
+from : LatLonSchema?
+to : LatLonSchema?
+page : number?
+limit : number?
+radius : number?
+dates : Date[2]?
+isRequest : boolean?
+expire_at : Date?
+pointsGeoreverse : boolean?
+lang : string?
}
CaseCreateSchema --> WayPointSchema : "contains"
WayPointSchema --> GeoMetaSchema : "references"
CaseListParamsSchema --> LatLonSchema : "references"
```

**Diagram sources**
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts)

**Section sources**
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts)

## Frontend Integration
Frontend components interact with the Case Router through tRPC hooks, enabling seamless data flow between the UI and backend. The `CreateCasePage.tsx` component uses the `updateOrCreate` mutation to create or update cases, collecting route information, pricing, and descriptions through an interactive form with a map interface. Users can define origin, destination, and intermediate waypoints, with real-time validation and feedback. The `CasePage.tsx` component utilizes the `id` query to retrieve specific case details and display comprehensive information including route visualization, participant management, and author details. Both components leverage React Query for state management, providing loading states, error handling, and automatic refetching. The integration demonstrates a clean separation of concerns, with the frontend handling user interaction and presentation while delegating data operations to the tRPC procedures.

```mermaid
flowchart TD
A[CreateCasePage] --> B[Form Input]
B --> C{Is Editing?}
C --> |Yes| D[Fetch Existing Case]
C --> |No| E[Initialize New Case]
D --> F[Populate Form]
E --> F
F --> G[User Edits Fields]
G --> H[Map Interaction]
H --> I[Update Waypoints]
I --> J[Submit Form]
J --> K[tRPC updateOrCreate]
K --> L[Handle Response]
L --> M{Success?}
M --> |Yes| N[Navigate to Case]
M --> |No| O[Show Error Toast]
P[CasePage] --> Q[tRPC case.id Query]
Q --> R{Loading?}
R --> |Yes| S[Show Spinner]
R --> |No| T[Display Case Data]
T --> U[Render Route Map]
T --> V[Show Participant List]
T --> W[Display Author Info]
U --> X[Interactive Map]
V --> Y[User Cards]
W --> Z[Contact Options]
```

**Diagram sources**
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx)
- [CasePage.tsx](file://frontend/src/pages/CasePage.tsx)

**Section sources**
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx)
- [CasePage.tsx](file://frontend/src/pages/CasePage.tsx)

## Case Service and Business Logic
The CaseService class encapsulates all business logic for case management, acting as an intermediary between the Case Router and the database. It handles case creation with proper normalization of baggage data and waypoint processing, including the creation of associated from, to, and middlepoint records in the database. For updates, the service implements a sophisticated merge strategy that identifies which middlepoints to create, update, or delete based on their IDs. The service also manages relationships with other entities, such as favorites and client assignments. When a new case is created, the service automatically checks for matching subscription requests and sends notifications to potential subscribers. The service includes utility methods for formatting case data for API responses, ensuring consistent output across different procedures. This separation of concerns allows the router to focus on request handling and validation while the service manages complex business rules and data operations.

```mermaid
classDiagram
class CaseService {
+static db : PrismaClient
+static faviriteDB : PrismaClient
+static userDB : PrismaClient
+static notificationService : NotificationService
+static onServerInit() : Promise~number~
+static findSubscribesAfterCreate(createdCase) : Promise~object~
+static toFavorite(params) : Promise~object~
+static id(id) : Promise~Case~
+static myList(type, userId) : Promise~Case[]~
+static list(params) : Promise~object~
+static makeLatLonNearbyPoints(config) : LatLonType
+static update(payload) : Promise~object~
+static create(payload) : Promise~object~
+static assignClient(caseId, client) : Promise~object~
+static formatCaseForApi(casE) : object
}
class CaseRouter {
+updateOrCreate(input)
+list(input)
+myList(input)
+id(input)
+assignClient(input)
+toFavofite(input)
}
CaseRouter --> CaseService : "delegates business logic"
CaseService --> Prisma : "database operations"
CaseService --> NotificationService : "sends notifications"
CaseService --> GeoService : "geocoding operations"
```

**Diagram sources**
- [case.ts](file://server/src/services/case.ts)

**Section sources**
- [case.ts](file://server/src/services/case.ts)

## Authentication and Authorization
The Case Router implements robust authentication and authorization mechanisms to protect user data and ensure proper access control. All sensitive operations require authentication through the `authedProcedure` wrapper, which verifies the user's session before proceeding. The router imports `checkIsAuthed` from the authentication module to validate user sessions and extract user information from the context. Authorization checks are performed within the CaseService, particularly in the update operation, where the service verifies that the requesting user is the author of the case being modified. This prevents unauthorized modifications to cases owned by other users. The `assignClient` and `toFavofite` procedures also validate that the authenticated user is operating on their own account, ensuring data privacy and integrity. These security measures create a secure environment where users can only modify their own data or interact with cases according to the application's business rules.

**Section sources**
- [case.ts](file://server/src/router/routers/case.ts)
- [case.ts](file://server/src/services/case.ts)

## Query Procedures and Filtering
The Case Router provides powerful query procedures for searching and filtering cases based on various criteria. The `list` procedure accepts parameters for origin and destination coordinates, enabling spatial searches within a specified radius. The implementation calculates latitude and longitude boundaries using the Haversine approximation, allowing efficient database queries for cases near specific geographical points. The procedure supports pagination through page and limit parameters, with safeguards to prevent excessive data retrieval (capping limit at 200 and radius at 300). Date filtering is available through the dates parameter, which accepts a two-element array defining a date range. The `myList` procedure offers user-specific filtering, allowing users to retrieve cases they've created, cases they're assigned to, their favorite cases, or their subscription requests. Results include metadata such as total count and pagination information, enabling efficient client-side rendering and navigation through large datasets.

```mermaid
flowchart TD
A[Client Request] --> B{Query Type}
B --> |list| C[Parse Parameters]
C --> D[Validate Radius and Limit]
D --> E[Calculate Geographical Boundaries]
E --> F[Build Prisma Query]
F --> G[Execute Database Query]
G --> H[Format Results with Metadata]
H --> I[Return Paginated Response]
B --> |myList| J[Validate User Authentication]
J --> K[Map Type to Query Filter]
K --> L[Build User-Specific Query]
L --> M[Execute Database Query]
M --> N[Format Cases for API]
N --> O[Return Filtered Cases]
```

**Diagram sources**
- [case.ts](file://server/src/router/routers/case.ts)
- [case.ts](file://server/src/services/case.ts)

**Section sources**
- [case.ts](file://server/src/router/routers/case.ts)
- [case.ts](file://server/src/services/case.ts)

## Error Handling Patterns
The Case Router and CaseService implement comprehensive error handling to ensure robust operation and meaningful feedback to users. The service layer throws descriptive errors for authorization failures, such as when a user attempts to modify a case they don't own. These errors are propagated through the tRPC layer to the frontend, where they can be displayed to users. The router leverages tRPC's built-in error handling to return appropriate HTTP status codes and error shapes. Input validation errors from Zod are automatically converted to structured error responses, highlighting specific validation failures. The frontend components use React Query's error handling capabilities to display user-friendly error messages through toast notifications. Rate limiting is implemented using a RateLimitedQueue to prevent abuse, with failed requests due to rate limiting being handled gracefully. This multi-layered approach to error handling ensures that both developers and end users receive clear information about issues, facilitating debugging and improving the overall user experience.

**Section sources**
- [case.ts](file://server/src/router/routers/case.ts)
- [case.ts](file://server/src/services/case.ts)
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx)

## Conclusion
The Case Router provides a robust and secure API for managing delivery cases in the takeNpass application. By leveraging tRPC and Zod, it ensures type safety and data integrity throughout the system. The clear separation between the router (handling request/response cycles) and the CaseService (managing business logic) promotes maintainability and testability. The comprehensive set of procedures supports all necessary operations for case management, from creation and modification to searching and user interactions. Integration with frontend components is seamless, thanks to the strongly-typed tRPC interface. Security is prioritized through authentication checks and authorization validation, protecting user data and preventing unauthorized access. The implementation demonstrates best practices in API design, with thoughtful consideration of performance (pagination, rate limiting), usability (comprehensive filtering), and reliability (robust error handling).