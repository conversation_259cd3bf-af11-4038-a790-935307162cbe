# Case Service

<cite>
**Referenced Files in This Document**   
- [case.ts](file://server/src/services/case.ts)
- [CreateCasePayload.ts](file://server/src/types/CreateCasePayload.ts)
- [UpdateCasePayload.ts](file://server/src/types/UpdateCasePayload.ts)
- [CaseWithWay.ts](file://server/src/types/CaseWithWay.ts)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts)
- [CaseStatusSchema.ts](file://server/prisma/generated/zod/inputTypeSchemas/CaseStatusSchema.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Domain Model](#domain-model)
3. [Case Creation](#case-creation)
4. [Case Search and Listing](#case-search-and-listing)
5. [Case Update and Management](#case-update-and-management)
6. [Favorites and User Interactions](#favorites-and-user-interactions)
7. [Status Transitions and Lifecycle](#status-transitions-and-lifecycle)
8. [Integration with Prisma and tRPC](#integration-with-prisma-and-trpc)
9. [Concurrency and Data Integrity](#concurrency-and-data-integrity)
10. [Error Handling and Validation](#error-handling-and-validation)
11. [Conclusion](#conclusion)

## Introduction
The Case Service in the takeNpass backend is responsible for managing travel cases, which represent transportation requests or offers between geographic waypoints. It supports core operations including case creation, search, update, assignment, and user interactions such as favoriting. The service integrates with Prisma ORM for database operations, GeoService for geolocation, and NotificationService for real-time updates. This document provides a comprehensive overview of the implementation, focusing on business logic, data flow, and integration points.

## Domain Model
The domain model revolves around the `Case` entity and its associated waypoints: `WayFrom`, `WayTo`, and `WayMiddle`. A case represents a travel request or offer with metadata such as description, baggage, price, and status. Waypoints define the geographic and temporal aspects of the journey, including latitude, longitude, date, and optional comments. Cases are linked to users as authors or clients and can be favorited by users.

```mermaid
erDiagram
CASE {
string id PK
datetime createdAt
string description
string status
string authorId FK
string baggage
float price
boolean isRequest
datetime expire_at
}
WAYPOINT {
int id PK
string geometa
datetime date
float lat
float lon
string comment
string caseId FK
}
USER {
string id PK
string email
string name
}
FAVORITE {
int id PK
string caseId FK
string userId FK
}
CASE ||--o{ WAYPOINT : "has"
CASE ||--|| USER : "author"
CASE }o--o{ USER : "clients"
CASE ||--o{ FAVORITE : "favorites"
```

**Diagram sources**
- [case.ts](file://server/src/services/case.ts#L1-L635)
- [CaseWithWay.ts](file://server/src/types/CaseWithWay.ts#L1-L23)

**Section sources**
- [case.ts](file://server/src/services/case.ts#L1-L635)
- [CaseWithWay.ts](file://server/src/types/CaseWithWay.ts#L1-L23)

## Case Creation
Case creation is handled by the `create` method in `CaseService`, which accepts a `CreateCasePayload` containing the case data and author ID. The payload includes waypoints (`from`, `to`, `middlepoints`) and metadata. Before persistence, baggage is normalized into a comma-separated string. Waypoints are mapped to ensure proper typing of `geometa` (as `Prisma.InputJsonValue`) and numeric conversion of `lat`/`lon`. The service also triggers notifications to subscribers if the case is not a request.

```mermaid
sequenceDiagram
participant Client
participant CaseService
participant Prisma
participant NotificationService
Client->>CaseService : create(CreateCasePayload)
CaseService->>CaseService : normalizeBaggage()
CaseService->>CaseService : mapWaypoint()
CaseService->>Prisma : create(case with waypoints)
Prisma-->>CaseService : created case
alt !isRequest
CaseService->>CaseService : findSubscribesAfterCreate()
CaseService->>NotificationService : createNotification(NEW_CASES_ON_SUBSCRIBE)
end
CaseService-->>Client : formatted case
```

**Diagram sources**
- [case.ts](file://server/src/services/case.ts#L450-L480)
- [CreateCasePayload.ts](file://server/src/types/CreateCasePayload.ts#L1-L6)

**Section sources**
- [case.ts](file://server/src/services/case.ts#L450-L480)
- [CreateCasePayload.ts](file://server/src/types/CreateCasePayload.ts#L1-L6)

## Case Search and Listing
The `list` method enables searching cases based on geographic proximity, date ranges, and other filters. It uses latitude/longitude bounds derived from a radius around `from` and `to` points. The service constructs a Prisma `CaseWhereInput` with spatial and temporal conditions, supporting flexible querying. Geolocation metadata can be reverse-resolved for display. The response includes pagination metadata and formatted cases.

```mermaid
flowchart TD
Start([list]) --> ValidateInput["Validate input parameters"]
ValidateInput --> Normalize["Normalize radius, limit"]
Normalize --> ReverseGeo["Reverse geocode points if requested"]
ReverseGeo --> BuildQuery["Build Prisma WHERE clause"]
BuildQuery --> SpatialFilter["Apply spatial filter using makeLatLonNearbyPoints"]
BuildQuery --> TemporalFilter["Apply date filter"]
BuildQuery --> ExecuteQuery["Execute findMany with pagination"]
ExecuteQuery --> Format["Map results with formatCaseForApi"]
Format --> Return["Return {cases, total, pagination}"]
```

**Diagram sources**
- [case.ts](file://server/src/services/case.ts#L180-L280)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L50-L71)

**Section sources**
- [case.ts](file://server/src/services/case.ts#L180-L280)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L50-L71)

## Case Update and Management
The `update` method allows case authors to modify case details. It validates ownership by comparing `authorId` with the case's `authorId`. The update process handles waypoints with create, update, and delete operations using Prisma's nested mutations. Middlepoints are synchronized by ID, with missing IDs deleted. Baggage is normalized, and waypoints are type-cast. The service ensures data consistency through transactional updates.

```mermaid
sequenceDiagram
participant Client
participant CaseService
participant Prisma
Client->>CaseService : update(UpdateCasePayload)
CaseService->>Prisma : findUniqueOrThrow(id)
Prisma-->>CaseService : case
CaseService->>CaseService : validate authorization
CaseService->>CaseService : normalizeBaggage(), mapWaypoint()
CaseService->>Prisma : update(case with nested operations)
Prisma-->>CaseService : updated case
CaseService-->>Client : {id}
```

**Diagram sources**
- [case.ts](file://server/src/services/case.ts#L400-L450)
- [UpdateCasePayload.ts](file://server/src/types/UpdateCasePayload.ts#L1-L6)

**Section sources**
- [case.ts](file://server/src/services/case.ts#L400-L450)
- [UpdateCasePayload.ts](file://server/src/types/UpdateCasePayload.ts#L1-L6)

## Favorites and User Interactions
Users can toggle a case as a favorite using the `toFavorite` method. The service checks for an existing favorite record and either deletes it (unfavorite) or creates a new one (favorite). This operation is idempotent and uses the `favorite` database model. The response indicates the new favorite state.

**Section sources**
- [case.ts](file://server/src/services/case.ts#L130-L150)

## Status Transitions and Lifecycle
Case status is managed through the `CaseStatus` enum, which includes values like `OPEN`, `CLOSED`, `ARCHIVED`, `DONE`, `CANCELED`, and `TEST`. The `onServerInit` method automatically closes cases where the destination date has passed. Status transitions are not exposed via public methods but are enforced through business logic during initialization and updates.

```mermaid
stateDiagram-v2
[*] --> OPEN
OPEN --> CLOSED : "expire_at passed"
CLOSED --> ARCHIVED : "manual/archive"
OPEN --> DONE : "completed"
OPEN --> CANCELED : "canceled by user"
```

**Diagram sources**
- [case.ts](file://server/src/services/case.ts#L20-L50)
- [CaseStatusSchema.ts](file://server/prisma/generated/zod/inputTypeSchemas/CaseStatusSchema.ts#L1-L7)

**Section sources**
- [case.ts](file://server/src/services/case.ts#L20-L50)
- [CaseStatusSchema.ts](file://server/prisma/generated/zod/inputTypeSchemas/CaseStatusSchema.ts#L1-L7)

## Integration with Prisma and tRPC
The Case Service integrates with Prisma ORM through static references to `$prisma` models. It uses tRPC routers to expose methods as API endpoints. Input validation is performed using Zod schemas defined in `CaseShemas.ts`, ensuring type safety and data integrity. The service abstracts database operations while exposing a clean interface for controllers.

**Section sources**
- [case.ts](file://server/src/services/case.ts#L1-L635)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L1-L71)

## Concurrency and Data Integrity
The service handles concurrent modifications by relying on Prisma's transactional guarantees and optimistic locking. Case updates require ownership verification, preventing unauthorized changes. The use of unique constraints and foreign keys in the database schema ensures referential integrity. No explicit locking mechanisms are implemented, assuming typical web-scale concurrency.

**Section sources**
- [case.ts](file://server/src/services/case.ts#L400-L450)

## Error Handling and Validation
Validation is performed at multiple levels: Zod schemas validate input parameters, Prisma enforces database constraints, and service methods include business logic checks (e.g., ownership). Errors are thrown as JavaScript `Error` objects with descriptive messages. The service normalizes input types (e.g., string to number for coordinates) to prevent type mismatches.

**Section sources**
- [case.ts](file://server/src/services/case.ts#L400-L450)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L1-L71)

## Conclusion
The Case Service provides a robust foundation for managing travel cases in the takeNpass application. It effectively handles creation, search, update, and user interactions while maintaining data integrity through Prisma and validation. The design supports scalability and extensibility, with clear separation of concerns and well-defined interfaces. Future enhancements could include more sophisticated geospatial queries and real-time updates.