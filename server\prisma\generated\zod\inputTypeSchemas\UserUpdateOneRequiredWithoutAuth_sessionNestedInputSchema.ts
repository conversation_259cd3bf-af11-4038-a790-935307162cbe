import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserCreateWithoutAuth_sessionInputSchema } from './UserCreateWithoutAuth_sessionInputSchema';
import { UserUncheckedCreateWithoutAuth_sessionInputSchema } from './UserUncheckedCreateWithoutAuth_sessionInputSchema';
import { UserCreateOrConnectWithoutAuth_sessionInputSchema } from './UserCreateOrConnectWithoutAuth_sessionInputSchema';
import { UserUpsertWithoutAuth_sessionInputSchema } from './UserUpsertWithoutAuth_sessionInputSchema';
import { UserWhereUniqueInputSchema } from './UserWhereUniqueInputSchema';
import { UserUpdateToOneWithWhereWithoutAuth_sessionInputSchema } from './UserUpdateToOneWithWhereWithoutAuth_sessionInputSchema';
import { UserUpdateWithoutAuth_sessionInputSchema } from './UserUpdateWithoutAuth_sessionInputSchema';
import { UserUncheckedUpdateWithoutAuth_sessionInputSchema } from './UserUncheckedUpdateWithoutAuth_sessionInputSchema';

export const UserUpdateOneRequiredWithoutAuth_sessionNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutAuth_sessionNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutAuth_sessionInputSchema),z.lazy(() => UserUncheckedCreateWithoutAuth_sessionInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutAuth_sessionInputSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutAuth_sessionInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => UserUpdateToOneWithWhereWithoutAuth_sessionInputSchema),z.lazy(() => UserUpdateWithoutAuth_sessionInputSchema),z.lazy(() => UserUncheckedUpdateWithoutAuth_sessionInputSchema) ]).optional(),
}).strict();

export default UserUpdateOneRequiredWithoutAuth_sessionNestedInputSchema;
