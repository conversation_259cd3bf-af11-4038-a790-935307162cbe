import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayToOrderByRelevanceFieldEnumSchema } from './WayToOrderByRelevanceFieldEnumSchema';
import { SortOrderSchema } from './SortOrderSchema';

export const WayToOrderByRelevanceInputSchema: z.ZodType<Prisma.WayToOrderByRelevanceInput> = z.object({
  fields: z.union([ z.lazy(() => WayToOrderByRelevanceFieldEnumSchema),z.lazy(() => WayToOrderByRelevanceFieldEnumSchema).array() ]),
  sort: z.lazy(() => SortOrderSchema),
  search: z.string()
}).strict();

export default WayToOrderByRelevanceInputSchema;
