# Technology Stack

<cite>
**Referenced Files in This Document**  
- [package.json](file://frontend/package.json)
- [package.json](file://server/package.json)
- [vite.config.ts](file://frontend/vite.config.ts)
- [tailwind.config.ts](file://frontend/tailwind.config.ts)
- [server.ts](file://server/src/server.ts)
- [better-auth.ts](file://server/src/providers/better-auth.ts)
</cite>

## Table of Contents
1. [Frontend Stack](#frontend-stack)
2. [Backend Stack](#backend-stack)
3. [End-to-End Type Safety with tRPC and Zod](#end-to-end-type-safety-with-trpc-and-zod)
4. [UI Framework and Styling](#ui-framework-and-styling)
5. [Testing Strategy](#testing-strategy)
6. [Real-Time Communication](#real-time-communication)
7. [Authentication System](#authentication-system)
8. [Version Compatibility and Dependency Management](#version-compatibility-and-dependency-management)

## Frontend Stack

The frontend of takeNpass is built using **React 18** with **TypeScript**, leveraging **Vite** as the build tool. This combination was selected to ensure a fast, type-safe, and developer-friendly environment.

React 18 provides modern features such as concurrent rendering and automatic batching, improving performance and responsiveness. TypeScript enhances code quality by enabling static type checking, reducing runtime errors, and improving developer experience through better autocompletion and refactoring support.

Vite was chosen over traditional bundlers like Webpack due to its superior development server performance. It leverages native ES modules and esbuild for pre-bundling dependencies, resulting in **instant server start** and **hot module replacement (HMR)** with near-instantaneous updates. This significantly improves developer productivity during active development.

The integration of Vite with React is seamless through the `@vitejs/plugin-react` plugin, which supports React Fast Refresh for state-preserving component updates.

```mermaid
flowchart TD
A["Vite Dev Server"] --> B["ES Modules"]
B --> C["React Fast Refresh"]
C --> D["Instant HMR"]
E["TypeScript"] --> F["Static Type Checking"]
F --> G["Early Error Detection"]
G --> H["Improved Code Quality"]
I["React 18"] --> J["Concurrent Rendering"]
J --> K["Better UX"]
```

**Diagram sources**
- [vite.config.ts](file://frontend/vite.config.ts#L1-L39)
- [package.json](file://frontend/package.json#L1-L77)

**Section sources**
- [package.json](file://frontend/package.json#L1-L77)
- [vite.config.ts](file://frontend/vite.config.ts#L1-L39)

## Backend Stack

The backend is powered by **Fastify**, a high-performance web framework for Node.js known for its low overhead and excellent JSON serialization speed. Fastify was selected over Express due to its schema-based routing, built-in logging, and strong typing support, which align well with the project's emphasis on performance and type safety.

For database access, **Prisma ORM** is used to provide a type-safe and intuitive interface to the database. Prisma generates TypeScript types from the database schema, ensuring that database queries are checked at compile time and reducing the risk of runtime errors.

The server is structured using modular routing and dependency injection principles, with services encapsulating business logic and providers managing external integrations such as authentication and database connections.

```mermaid
graph TB
A["Fastify Server"] --> B["Request Handling"]
B --> C["tRPC Router"]
C --> D["Service Layer"]
D --> E["Prisma Client"]
E --> F["MySQL Database"]
G["TypeScript"] --> H["Compile-Time Safety"]
```

**Diagram sources**
- [server.ts](file://server/src/server.ts#L1-L253)
- [package.json](file://server/package.json#L1-L56)

**Section sources**
- [server.ts](file://server/src/server.ts#L1-L253)
- [package.json](file://server/package.json#L1-L56)

## End-to-End Type Safety with tRPC and Zod

takeNpass implements **tRPC** to achieve end-to-end type safety between the frontend and backend. tRPC allows API endpoints to be defined as TypeScript functions on the server, with their types automatically inferred and made available on the client without the need for code generation or manual type definitions.

This eliminates the need for REST API documentation and reduces the risk of mismatched request/response types. The integration with React Query (`@tanstack/react-query`) enables efficient data fetching, caching, and synchronization on the frontend.

**Zod** is used for runtime validation of data, ensuring that inputs and outputs conform to expected schemas. Zod schemas are used in conjunction with Prisma to validate data before it reaches the database, and they are also integrated with tRPC to validate requests at the API boundary.

The combination of tRPC and Zod ensures that both compile-time and runtime type safety are enforced across the entire stack.

```mermaid
sequenceDiagram
participant Frontend
participant tRPC
participant Backend
participant Zod
participant Prisma
Frontend->>tRPC : Call endpoint with args
tRPC->>Zod : Validate input
Zod-->>tRPC : Validated data
tRPC->>Backend : Invoke function
Backend->>Prisma : Query database
Prisma-->>Backend : Data
Backend->>Zod : Validate output
Zod-->>tRPC : Validated response
tRPC-->>Frontend : Return result
```

**Diagram sources**
- [server.ts](file://server/src/server.ts#L1-L253)
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L33)

**Section sources**
- [server.ts](file://server/src/server.ts#L1-L253)
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L33)

## UI Framework and Styling

The UI is styled using **Tailwind CSS**, a utility-first CSS framework that enables rapid UI development with minimal custom CSS. Tailwind's atomic classes are used throughout the component library to ensure consistency and maintainability.

The project integrates **@heroui/react**, a component library that provides pre-styled, accessible React components. This reduces development time and ensures a consistent design language across the application. The Tailwind configuration is extended with `@heroui/react`'s theme plugin to support custom design tokens and responsive behavior.

Additional libraries such as **Framer Motion** and **React Leaflet** are used for animations and interactive maps, respectively, enhancing the user experience with smooth transitions and geographic visualizations.

```mermaid
classDiagram
class TailwindCSS {
+Utility Classes
+Responsive Design
+Dark Mode Support
}
class HerouiReact {
+Pre-built Components
+Accessibility
+Theme Integration
}
class FramerMotion {
+Animations
+Gestures
+Layout Effects
}
TailwindCSS --> HerouiReact : "styled with"
HerouiReact --> FramerMotion : "enhanced by"
```

**Diagram sources**
- [tailwind.config.ts](file://frontend/tailwind.config.ts#L1-L48)
- [package.json](file://frontend/package.json#L1-L77)

**Section sources**
- [tailwind.config.ts](file://frontend/tailwind.config.ts#L1-L48)
- [package.json](file://frontend/package.json#L1-L77)

## Testing Strategy

The testing stack includes **Vitest** for unit and integration testing on both frontend and backend, providing a fast and Jest-compatible environment. Vitest runs tests in the browser-like environment using Vite, enabling seamless testing of components and utilities.

For end-to-end (E2E) testing, **Playwright** (`@playwright/test`) is used to simulate real user interactions across multiple browsers. Playwright ensures that critical user flows such as authentication, case creation, and chat functionality work as expected in production-like environments.

Test files are colocated with their corresponding modules, and mocking is used extensively to isolate components and services during testing.

**Section sources**
- [package.json](file://frontend/package.json#L1-L77)
- [package.json](file://server/package.json#L1-L56)

## Real-Time Communication

Real-time features such as chat and notifications are implemented using **Socket.IO**, which provides bidirectional communication between the client and server. The frontend uses `socket.io-client` to connect to the backend's Socket.IO server, enabling instant message delivery and presence updates.

The backend integrates Socket.IO via the `fastify-socket.io` plugin, allowing seamless integration with Fastify's ecosystem. WebSocket connections are managed through a centralized `IO` service that handles connection lifecycle events and message broadcasting.

**Section sources**
- [server.ts](file://server/src/server.ts#L1-L253)
- [socket.ts](file://frontend/socket.ts#L1-L10)

## Authentication System

Authentication is handled by **better-auth**, a modern, type-safe authentication library that supports email/password login, session management, and secure cookie handling. better-auth is integrated with Prisma to persist user data and sessions in the database.

The authentication flow is exposed through REST-like endpoints under `/api/auth`, which are proxied through Fastify and handled by better-auth's request handler. Sessions are encrypted and stored in HTTP-only cookies, with automatic refresh and expiration policies configured for security.

The system supports multi-origin development environments through configurable trusted origins, enabling secure local development with multiple frontend ports.

```mermaid
sequenceDiagram
participant Client
participant Fastify
participant BetterAuth
participant Prisma
Client->>Fastify : POST /api/auth/sign-in
Fastify->>BetterAuth : Forward request
BetterAuth->>Prisma : Validate credentials
Prisma-->>BetterAuth : User data
BetterAuth->>BetterAuth : Create session
BetterAuth->>Fastify : Set encrypted cookie
Fastify-->>Client : 200 OK + session cookie
```

**Diagram sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L33)
- [server.ts](file://server/src/server.ts#L1-L253)

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L33)
- [server.ts](file://server/src/server.ts#L1-L253)

## Version Compatibility and Dependency Management

The project uses **npm** for dependency management, with `package-lock.json` ensuring deterministic installs. Both frontend and backend are aligned on key dependency versions:

- **tRPC**: v11.5.1 (shared across client and server)
- **Zod**: v4.1.5 (backend)
- **TypeScript**: v5.9.2 (both)
- **React**: v18.3.1 (frontend)
- **Fastify**: v5.6.0 (backend)
- **Prisma**: v6.15.0 (backend)

Dependencies are updated regularly, with breaking changes evaluated through automated testing and manual review. The use of TypeScript and tRPC minimizes integration risks during upgrades by catching type mismatches at compile time.

**Section sources**
- [package.json](file://frontend/package.json#L1-L77)
- [package.json](file://server/package.json#L1-L56)