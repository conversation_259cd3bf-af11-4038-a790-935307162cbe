import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserUpdateWithoutSendedRatingsInputSchema } from './UserUpdateWithoutSendedRatingsInputSchema';
import { UserUncheckedUpdateWithoutSendedRatingsInputSchema } from './UserUncheckedUpdateWithoutSendedRatingsInputSchema';
import { UserCreateWithoutSendedRatingsInputSchema } from './UserCreateWithoutSendedRatingsInputSchema';
import { UserUncheckedCreateWithoutSendedRatingsInputSchema } from './UserUncheckedCreateWithoutSendedRatingsInputSchema';
import { UserWhereInputSchema } from './UserWhereInputSchema';

export const UserUpsertWithoutSendedRatingsInputSchema: z.ZodType<Prisma.UserUpsertWithoutSendedRatingsInput> = z.object({
  update: z.union([ z.lazy(() => UserUpdateWithoutSendedRatingsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutSendedRatingsInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutSendedRatingsInputSchema),z.lazy(() => UserUncheckedCreateWithoutSendedRatingsInputSchema) ]),
  where: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export default UserUpsertWithoutSendedRatingsInputSchema;
