import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface GlassCardProps {
  children: ReactNode;
  className?: string;
  variant?: 'light' | 'dark' | 'primary' | 'default';
  blur?: 'sm' | 'md' | 'lg' | 'xl';
  opacity?: 'low' | 'medium' | 'high';
  border?: boolean;
  shadow?: boolean;
  hover?: boolean;
}

export const GlassCard = ({
  children,
  className,
  variant = 'default',
  blur = 'md',
  opacity = 'medium',
  border = true,
  shadow = true,
  hover = true,
  ...props
}: GlassCardProps) => {
  const baseClasses = "relative overflow-hidden transition-all duration-300";
  
  const variantClasses = {
    default: "bg-white/5 border-white/10",
    light: "bg-white/8 border-white/15",
    dark: "bg-black/10 border-white/8",
    primary: "bg-primary/8 border-primary/20"
  };
  
  const blurClasses = {
    sm: "backdrop-blur-sm",
    md: "backdrop-blur-md",
    lg: "backdrop-blur-lg",
    xl: "backdrop-blur-xl"
  };
  
  const opacityClasses = {
    low: "bg-opacity-30",
    medium: "bg-opacity-50",
    high: "bg-opacity-70"
  };
  
  const shadowClasses = shadow ? "shadow-lg shadow-black/10" : "";
  const borderClasses = border ? "border" : "";
  const hoverClasses = hover ? "hover:bg-opacity-60 hover:border-opacity-30 hover:shadow-xl" : "";
  
  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        blurClasses[blur],
        shadowClasses,
        borderClasses,
        hoverClasses,
        className
      )}
      style={{
        backdropFilter: `blur(${blur === 'sm' ? '8px' : blur === 'md' ? '16px' : blur === 'lg' ? '24px' : '32px'})`,
        WebkitBackdropFilter: `blur(${blur === 'sm' ? '8px' : blur === 'md' ? '16px' : blur === 'lg' ? '24px' : '32px'})`
      }}
      {...props}
    >
      {children}
    </div>
  );
};

// Preset components for common use cases
export const GlassCardLight = ({ children, className, ...props }: Omit<GlassCardProps, 'variant'>) => (
  <GlassCard variant="light" className={className} {...props}>
    {children}
  </GlassCard>
);

export const GlassCardDark = ({ children, className, ...props }: Omit<GlassCardProps, 'variant'>) => (
  <GlassCard variant="dark" className={className} {...props}>
    {children}
  </GlassCard>
);

export const GlassCardPrimary = ({ children, className, ...props }: Omit<GlassCardProps, 'variant'>) => (
  <GlassCard variant="primary" className={className} {...props}>
    {children}
  </GlassCard>
);
