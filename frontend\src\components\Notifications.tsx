import NotificationsIcon from '@/lib/svg/NotificationIcon'
import { socket } from '@/socket'
import { trpc } from '@/trpc'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON><PERSON> } from "@heroui/react"
import { useEffect, useState } from 'react'
import { SocketNotification, SocketNotificationType } from '../../../server/src/types/SocketNotificationType'
import { addToast, closeToast } from '@heroui/react'
import { motion } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import { Link as RouterLink } from '@tanstack/react-router'
import { useUserStore } from '@/store'
import { dispatch } from 'use-bus'
// import dayjs from 'dayjs'

// const disabledEvents: Array<keyof SocketNotificationType> = ['CANCEL_REQUEST','NEW_CASES_ON_SUBSCRIBE', 'NEW_MESSAGE', 'NEW_REQUEST', 'NEW_REVIEW']

// type RT = {
//   event: keyof SocketNotificationType,
//   route: string
// }

const container = {
  hidden: { opacity: 1, scale: 0 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      delayChildren: 0.2,
      staggerChildren: 0.1
    }
  }
}

const item = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1
  }
}

interface Props {}
export const Notifications = ({}: Props) => {
  const { data: userData } = useUserStore()

  const [ncount, setNcount] = useState<number | string>(0)
  const [showSidebar, setShowSidebar] = useState(false)
  const [nTab, setNTab] = useState('unread')
  const { t } = useTranslation()

  const { isFetching, isError, data, error, refetch } = trpc.notification.list.useQuery(undefined, {
    enabled: !!userData?.id
  })

  const { mutateAsync: markAsRead, status: markAsRead_status } = trpc.notification.markAsRead.useMutation({
    onSuccess: () => refetch()
  })
  const { mutateAsync: markAllAsRead, status: markAllAsRead_status } = trpc.notification.markAllAsRead.useMutation({
    onSuccess: () => refetch()
  })

  // useEffect(() => {
  //   refetch()
  // }, [markAsRead_status, markAllAsRead_status])

  useEffect(() => {
    if (data?.length) {
      const unreadLen = data.filter((i) => i.data.read == false).length
      setNcount(unreadLen > 100 ? '99+' : unreadLen)
    }
  }, [data])

  const NewReviewTemplate = (notification: SocketNotification) => {
    return (
      <div className='p-1'>
        <div>
          <div className='flex justify-between text-sm text-default-500'>
            <div>
              <div className='text-xs text-default-400'>#{notification.data.id}</div>
            </div>
            <div>{notification.data.createdAt.toLocaleString()}</div>
          </div>
          <div className='ml-3 flex flex-col justify-end'>
            {/* <p className='text-sm font-medium text-gray-900'>{notification.data?.sender?.username}</p> */}
            <div className='mt-1 text-sm'>{notification.message}</div>
            <div className='flex justify-start'>
              <div onClick={() => setShowSidebar(false)}>
                <RouterLink className='text-primary-400 font-semibold text-sm my-2' to='/reviews'>
                  {t('Go to')}
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const NewMessageTemplate = (notification: SocketNotification) => {
    return (
      <div className='p-1'>
        <div>
          <div className='flex justify-between text-sm text-default-500'>
            <div>
              <div className='text-xs text-default-400'>#{notification.data.id}</div>
            </div>
            <div>{notification.data.createdAt.toLocaleString()}</div>
          </div>
          <div className='ml-3 flex flex-col justify-end'>
            {/* <p className='text-sm font-medium text-gray-900'>{notification.data?.sender?.username}</p> */}
            <div className='mt-1 text-sm'>{notification.message}</div>
            <div className='flex justify-start'>
              <div onClick={() => setShowSidebar(false)}>
                <RouterLink className='text-primary-400 font-semibold text-sm my-2' search={{ partnerId: notification.data.senderId }} to='/messages'>
                  {t('Go to')}
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const NewRequestTemplate = (notification: SocketNotification) => {
    console.log('🚀 ~ file: Notifications.tsx:86 ~ NewRequestTemplate ~ notification:', notification)
    return (
      <div className='flex-1 p-1'>
        <div>
          <div className='shrink-0 pt-0.5'>
            {/* <div className='text-default-600 font-semibold'>{notification.data.id}: </div> */}
            {/* <Avatar src={notification?.data.sender?.avatar?.base64string} /> */}
          </div>
          <div className='flex justify-between text-sm text-default-500'>
            <div>
              <div className='text-xs text-default-400'>#{notification.data.id}</div>
            </div>
            <div>{notification.data.createdAt.toLocaleString()}</div>
          </div>
          <div className='ml-3 flex-1 '>
            {/* <p className='text-sm font-medium text-gray-900'>{notification.data?.sender?.username}</p> */}
            <p className='mt-1 text-sm'>{notification.message}</p>
          </div>
        </div>
      </div>
    )
  }

  const NewCasesOnRequestTemplate = (notification: SocketNotification) => {
    return (
      <div className='p-1'>
        <div>
          <div className='flex justify-between text-sm text-default-500'>
            <div>
              <div className='text-xs text-default-400'>#{notification.data.id}</div>
            </div>
            <div>{notification.data.createdAt.toLocaleString()}</div>
          </div>
          <div className='ml-3 flex flex-col justify-end'>
            {/* <p className='text-sm font-medium text-gray-900'>{notification.data?.sender?.username}</p> */}
            <div className='mt-1 text-sm'>{notification.message}</div>
            <div className='flex justify-start'>
              <div onClick={() => setShowSidebar(false)}>
                <RouterLink className='text-primary-400 font-semibold text-sm my-2' to='/route/$caseId' params={{ caseId: notification.data.caseId }}>
                  {t('Go to')}
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // TODO: useMemo / useCallback
  const templates: { [K in keyof Partial<SocketNotificationType>]: (notification: SocketNotification) => JSX.Element } = {
    'NEW_REQUEST': NewRequestTemplate,
    'CANCEL_REQUEST': NewRequestTemplate,
    'NEW_CASES_ON_SUBSCRIBE': NewCasesOnRequestTemplate,
    'NEW_REVIEW': NewReviewTemplate,
    'NEW_MESSAGE': NewMessageTemplate
  }

  function newToast(notification: SocketNotification) {
    let key: string | number | undefined
    key = addToast({
      description: templates[notification.type]?.(notification) || notification.message,
      hideCloseButton: true,
      timeout: 8000,
      classNames: {
        base: 'max-w-md w-full bg-default-100 shadow-lg text-default-700 rounded-lg pointer-events-auto flex items-center ring-1 ring-black ring-opacity-5'
      },
      endContent: (
        <Button onClick={() => key && closeToast(key)} size='sm' isIconOnly color='primary' variant='flat' aria-label='Close'>
          <b>X</b>
        </Button>
      )
    })
  }

  function socketCB(sdata: SocketNotification) {
    refetch()

    if (sdata.type == 'NEW_MESSAGE') {
      console.log('start NEW_MESSAGE dispatch!')

      dispatch({
        type: 'NEW_MESSAGE'
        // payload: sdata
      })
    }

    if (sdata.type == 'NEW_MESSAGE' && window.location.pathname == '/messages') {
      return false
    }

    newToast(sdata)
  }
  useEffect(() => {
    if (userData?.id) {
      console.log('socket xnotif init')

      socket.on('xnotification', (sdata: SocketNotification) => {
        socketCB(sdata)
      })

      return () => {
        socket.off('xnotification')
      }
    }
  }, [userData])

  // useEffect(() => {
  //   console.log('notifications: ', data)
  // }, [data])

  return (
    <>
      {userData?.id && (
        <div>
          <Badge
            // da
            isInvisible={!ncount}
            variant='shadow'
            shape='rectangle'
            onClick={() => setShowSidebar(true)}
            content={ncount}
            size='md'
            classNames={{
              base: 'mt-1'
            }}
            color='danger'
          >
            <Button className='w-6 h-6' isLoading={isFetching} onClick={() => setShowSidebar(true)} radius='full' isIconOnly variant='bordered'>
              {!isFetching && <NotificationsIcon className='w-6 h-6' />}
            </Button>
          </Badge>

          <Drawer isOpen={showSidebar} onOpenChange={setShowSidebar} placement='right' size='sm'>
            <DrawerContent className='bg-default-50 dark:bg-zinc-950 w-80'>
              {(onClose) => (
                <>
                  <DrawerHeader className='hidden' />
                  <DrawerBody className='p-2 pt-0'>
                    {showSidebar && (
                      <Tabs selectedKey={nTab} onSelectionChange={setNTab} aria-label={t('notifications')}>
                        <Tab key='unread' title={t('Unread')}>
                          {nTab == 'unread' && (
                            <div>
                              <div className='flex justify-end mt-3'>
                                <Button
                                  onClick={() => markAllAsRead()}
                                  radius='sm'
                                  variant='light'
                                  color='primary'
                                >
                                  {t('Mark all as read')}
                                </Button>
                              </div>
                              <motion.ul className='space-y-5 mt-3' variants={container} initial='hidden' animate='visible'>
                                {data
                                  ?.filter((i) => i.data.read == false)
                                  .map((notification) => (
                                    <motion.li key={notification.data.id} className='item' variants={item}>
                                      <div className='bg-default-100 p-1 py-2 rounded-md'>
                                        {templates[notification.type]?.(notification)}
                                        <div className='flex justify-end'>
                                          <Button onClick={() => markAsRead(notification.data.id)} radius='sm' variant='flat' size='sm'>
                                            {t('Mark as read')}
                                          </Button>
                                        </div>
                                      </div>
                                    </motion.li>
                                  ))}
                              </motion.ul>
                            </div>
                          )}
                        </Tab>
                        <Tab key='read' title={t('Read')}>
                          {nTab == 'read' && (
                            <ul className='space-y-5'>
                              {data
                                ?.filter((i) => i.data.read)
                                .map((notification) => (
                                  <li key={notification.data.id} className='item'>
                                    <div className='bg-default-100 p-1 py-2 rounded-md'>{templates[notification.type]?.(notification)}</div>
                                  </li>
                                ))}
                            </ul>
                          )}
                        </Tab>
                      </Tabs>
                    )}
                  </DrawerBody>
                </>
              )}
            </DrawerContent>
          </Drawer>
        </div>
      )}
    </>
  )
}
