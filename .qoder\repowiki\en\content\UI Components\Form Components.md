# Form Components

<cite>
**Referenced Files in This Document**   
- [DatePicker.tsx](file://frontend/src/components/DatePicker.tsx)
- [TimePicker.tsx](file://frontend/src/components/TimePicker.tsx)
- [PhoneInput.tsx](file://frontend/src/components/PhoneInput.tsx)
- [SortSelect.tsx](file://frontend/src/components/SortSelect.tsx)
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx)
- [Login.tsx](file://frontend/src/components/Login.tsx)
- [RatingForm.tsx](file://frontend/src/components/RatingForm.tsx)
- [RatingsPage.tsx](file://frontend/src/pages/RatingsPage.tsx)
- [RegPage.tsx](file://frontend/src/pages/RegPage.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Input Components](#core-input-components)
   - [DatePicker](#datepicker)
   - [TimePicker](#timepicker)
   - [PhoneInput](#phoneinput)
   - [SortSelect](#sortselect)
3. [Form Composition and State Management](#form-composition-and-state-management)
   - [RegForm](#regform)
   - [Login Component](#login-component)
4. [RatingForm and User Feedback](#ratingform-and-user-feedback)
5. [Form Composition Patterns](#form-composition-patterns)
6. [Accessibility and Responsiveness](#accessibility-and-responsiveness)
7. [Global State and Error Handling](#global-state-and-error-handling)

## Introduction
This document provides comprehensive documentation for the form components in the takeNpass application. It details the implementation, props, validation patterns, and integration with form state for key input components including DatePicker, TimePicker, PhoneInput, and SortSelect. The document also covers the RegForm and Login components with their validation logic and error handling, the RatingForm component used for user feedback, and demonstrates form composition patterns from actual pages. Accessibility compliance, keyboard navigation, mobile responsiveness, and integration with global state management are also addressed.

## Core Input Components

### DatePicker
The DatePicker component (`CDatePicker`) provides a flexible date selection interface with support for both single date and date range selection. It wraps the `DatePicker` and `DateRangePicker` components from @heroui/react with custom styling and behavior.

**Props:**
- `calendarProps`: Props passed directly to the underlying DatePicker/DateRangePicker component
- `inputProps`: Props for customizing the input field appearance and behavior
- `popoverProps`: Configuration for the popover container
- `ariaLabel`: Accessibility label for screen readers

The component manages internal state for the selected date(s) and synchronizes it with the parent component through the `onChange` callback in `calendarProps`. It supports clearing values and automatically closes the popover after selection.

```mermaid
flowchart TD
Start([DatePicker Rendered]) --> CheckMode{"Selection Mode?"}
CheckMode --> |Single| SingleDate[Render DatePicker]
CheckMode --> |Range| RangeDate[Render DateRangePicker]
SingleDate --> HandleChange[onChange(value)]
RangeDate --> HandleRange[onChange(rangeValue)]
HandleChange --> UpdateParent[Update Parent State]
HandleRange --> UpdateParent
UpdateParent --> SyncInput[Sync Input Display]
```

**Diagram sources**
- [DatePicker.tsx](file://frontend/src/components/DatePicker.tsx#L1-L76)

**Section sources**
- [DatePicker.tsx](file://frontend/src/components/DatePicker.tsx#L1-L76)
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx#L274-L317)

### TimePicker
The TimePicker component implements a custom time selection interface using a dropdown with hour and minute selectors. It provides a user-friendly way to select time values with visual feedback.

**Implementation Details:**
- Uses `Dropdown` to contain the time selection interface
- Displays selected time in a readonly input field
- Contains two `Select` components for hours (00-23) and minutes (00-59)
- Manages state for selected hour and minute using `useState`
- Updates the display value whenever hour or minute selection changes

The component creates arrays of hour and minute options with padded two-digit values and uses `useEffect` to synchronize the combined time value when either selection changes.

```mermaid
flowchart TD
A[TimePicker Rendered] --> B[Display Current Value]
B --> C[Dropdown Trigger]
C --> D[Open Dropdown]
D --> E[Select Hour]
E --> F[Update Hour State]
F --> G[Update Display Value]
D --> H[Select Minute]
H --> I[Update Minute State]
I --> G
G --> J[Propagate Value Change]
```

**Diagram sources**
- [TimePicker.tsx](file://frontend/src/components/TimePicker.tsx#L3-L95)

**Section sources**
- [TimePicker.tsx](file://frontend/src/components/TimePicker.tsx#L3-L95)

### PhoneInput
The PhoneInput component provides an international phone number input with country selection and validation. It integrates `react-phone-number-input` with @heroui/react components for a consistent UI.

**Props:**
- `onValueChange`: Callback function triggered when phone number changes
- `value`: Current phone number value
- `errorMessage`: Error message to display when validation fails
- `formNoValidate`: Flag to disable browser validation
- `variant`: Input variant style (default: 'flat')
- `readonly`: Read-only state
- `size`: Input size (default: 'md')
- `label`: Custom label (default: 'Phone')

The component uses `ReactPhoneInput` as the base input with a custom `PhoneInputComponent` wrapper that applies @heroui styling. It maintains the current phone value in a `ref` and updates it through `onChangeHandler`, only propagating the value on blur.

```mermaid
flowchart TD
A[PhoneInput Rendered] --> B[Initialize Ref]
B --> C[Render ReactPhoneInput]
C --> D[User Types Number]
D --> E[onChangeHandler Updates Ref]
E --> F[User Blurs Input]
F --> G[onBlur Triggers onValueChange]
G --> H[Propagate Value to Parent]
```

**Diagram sources**
- [PhoneInput.tsx](file://frontend/src/components/PhoneInput.tsx#L20-L78)

**Section sources**
- [PhoneInput.tsx](file://frontend/src/components/PhoneInput.tsx#L20-L78)
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx#L116-L145)

### SortSelect
The SortSelect component provides a dropdown interface for selecting sorting options. It uses @heroui/react's Dropdown and Select components to create a clean, interactive sorting control.

**Props:**
- `onChange`: Callback function triggered when sort option changes

The component maintains selected keys state and uses `useMemo` to transform the selected key into a display value. Currently supports 'date' and 'price' sorting options, with the UI displaying a capitalized version of the selected option.

```mermaid
flowchart TD
A[SortSelect Rendered] --> B[Initialize SelectedKeys]
B --> C[Render Button with Value]
C --> D[User Clicks Button]
D --> E[Open Dropdown Menu]
E --> F[User Selects Option]
F --> G[Update SelectedKeys]
G --> H[Call onSelectionChange]
H --> I[Update Display Value]
```

**Diagram sources**
- [SortSelect.tsx](file://frontend/src/components/SortSelect.tsx#L7-L33)

**Section sources**
- [SortSelect.tsx](file://frontend/src/components/SortSelect.tsx#L7-L33)

## Form Composition and State Management

### RegForm
The RegForm component implements the user registration form with comprehensive validation and error handling. It manages form state for username, email, password, avatar, and phone number.

**State Management:**
- Individual state variables for each form field
- Errors object to track validation errors for each field
- Loading state to manage submission process

**Validation Logic:**
- Client-side validation through better-auth integration
- Error messages displayed below respective input fields
- Specific error handling for email and password validation failures
- Visual feedback through `isInvalid` prop on input components

**Integration:**
- Uses `authClient.signUp.email` for registration
- Stores successful registration data in `useUserStore`
- Navigates to home page upon successful registration
- Displays toast notifications for success and error states

```mermaid
sequenceDiagram
participant User
participant RegForm
participant AuthClient
participant Store
User->>RegForm : Fill registration form
RegForm->>RegForm : Update local state
User->>RegForm : Click Sign Up
RegForm->>RegForm : Set isLoading = true
RegForm->>AuthClient : signUp.email(credentials)
AuthClient-->>RegForm : Return result
alt Success
RegForm->>Store : setData(user)
RegForm->>RegForm : Show success toast
RegForm->>RegForm : Navigate to home
else Error
RegForm->>RegForm : Parse error messages
RegForm->>RegForm : Update errors state
RegForm->>RegForm : Show error toast
end
RegForm->>RegForm : Set isLoading = false
```

**Diagram sources**
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx#L38-L168)

**Section sources**
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx#L38-L168)
- [RegPage.tsx](file://frontend/src/pages/RegPage.tsx#L0-L10)

### Login Component
The Login component implements the user authentication interface with email and password fields. It follows a similar pattern to RegForm but for user sign-in functionality.

**Key Features:**
- State management for login credentials
- Integration with better-auth client for authentication
- Global store integration for user data persistence
- Toast notifications for feedback
- Loading state during authentication process

The component uses `authClient.signIn.email` to authenticate users and updates the global user store upon successful login, then navigates to the home page.

```mermaid
sequenceDiagram
participant User
participant Login
participant AuthClient
participant Store
User->>Login : Enter credentials
Login->>Login : Update state
User->>Login : Click Sign In
Login->>Login : Set isLoading = true
Login->>AuthClient : signIn.email(credentials)
AuthClient-->>Login : Return result
alt Success
Login->>Store : setData(user)
Login->>Login : Show success toast
Login->>Login : Navigate to home
else Error
Login->>Login : Show error toast
end
Login->>Login : Set isLoading = false
```

**Diagram sources**
- [Login.tsx](file://frontend/src/components/Login.tsx#L0-L95)

**Section sources**
- [Login.tsx](file://frontend/src/components/Login.tsx#L0-L95)

## RatingForm and User Feedback
The RatingForm component enables users to provide feedback and ratings for other users within a case context. It integrates with the application's API to submit ratings and comments.

**Props:**
- `c`: Case object for context
- `targetUser`: User receiving the rating
- `initValue`: Initial rating and comment values (optional)

**State Management:**
- `rating`: Current star rating (0-5)
- `comment`: Text comment
- Integration with `trpc.users.setRating.useMutation` for API calls

**Implementation:**
- Uses `UserRating` component for star rating selection
- Textarea for comment input
- Submit button that triggers rating submission
- Support for both creating new ratings and updating existing ones

The component includes `useEffect` hooks to initialize values from `initValue` and to display success notifications after submission.

```mermaid
flowchart TD
A[RatingForm Rendered] --> B[Check initValue]
B --> C[Initialize rating and comment]
C --> D[Render UserRating Component]
D --> E[User Selects Stars]
E --> F[Update rating state]
D --> G[User Types Comment]
G --> H[Update comment state]
H --> I[Click Confirm/Update]
I --> J[Call mutateAsync]
J --> K[API Request]
K --> L[Show Success Toast]
```

**Diagram sources**
- [RatingForm.tsx](file://frontend/src/components/RatingForm.tsx#L0-L65)

**Section sources**
- [RatingForm.tsx](file://frontend/src/components/RatingForm.tsx#L0-L65)
- [RatingsPage.tsx](file://frontend/src/pages/RatingsPage.tsx#L0-L12)

## Form Composition Patterns
The application demonstrates several form composition patterns through its page components:

1. **Standalone Form Pages**: Pages like `RegPage` and `LoginPage` contain single, focused forms wrapped in minimal layout.

2. **Profile-Integrated Forms**: Components like `ProfileSettings` integrate forms within profile containers, combining form elements with other UI components.

3. **Contextual Forms**: The `RatingForm` appears within the context of a specific case and user, demonstrating how forms can be embedded in complex UIs.

4. **Reusable Form Components**: Input components like `DatePicker`, `PhoneInput`, and `SortSelect` are designed as reusable building blocks that can be composed into various forms.

These patterns show a consistent approach to form design while allowing flexibility for different use cases.

## Accessibility and Responsiveness
The form components prioritize accessibility and mobile responsiveness:

**Accessibility Features:**
- Proper ARIA labels and roles
- Keyboard navigation support
- Focus management
- Screen reader compatibility
- Semantic HTML structure

**Responsive Design:**
- Mobile-first approach
- Flexible layouts using Tailwind CSS
- Appropriate touch targets
- Adaptive component sizing
- Graceful degradation on smaller screens

The components use @heroui/react's built-in accessibility features and follow best practices for interactive elements, ensuring they are usable by all users regardless of device or ability.

## Global State and Error Handling
The application implements a robust state management and error handling system:

**State Management:**
- Uses `useUserStore` for global user data
- Integrates with `zustand` for state persistence
- Centralized authentication state
- Synchronization between components

**Error Handling:**
- Form-level validation with immediate feedback
- API error parsing and display
- Toast notifications for user feedback
- Console logging for debugging
- Graceful error recovery

The error handling pattern consistently uses the `errors` state object to track field-specific validation issues and displays them through the `errorMessage` prop on input components, providing clear feedback to users.