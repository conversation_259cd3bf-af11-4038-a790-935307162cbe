import { useUserStore } from '@/store'
import { trpc } from '@/trpc'
import { Badge, Listbox, ListboxItem } from "@heroui/react"
import { Link, useRouter } from '@tanstack/react-router'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { addToast } from '@heroui/react'
import { authClient } from '@/lib/auth'

export const UserMenu = () => {
  const { t } = useTranslation()
  const userStore = useUserStore()
  const router = useRouter()

  const { data: messageCount_data, isFetching: messageCount_isFetching } = trpc.message.unreadCount.useQuery()
  const [isLoading, setIsLoading] = useState(false)

  async function logout() {
    try {
      setIsLoading(true)

      // Используем better-auth клиент для выхода
      const result = await authClient.signOut()

      if (result.data) {
        userStore.setData(null)
        addToast({ color: 'success', title: t('Logged out successfully') })
        router.navigate({ to: '/' })
      } else if (result.error) {
        addToast({ color: 'danger', title: result.error.message || t('Logout failed') })
      }
    } catch (error) {
      console.error('Logout error:', error)
      addToast({ color: 'danger', title: t('Something went wrong, please try again later') })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Listbox
      aria-label='Listbox Variants'
      // color='primary'
      variant='flat'
      classNames={{
        // list: 'space-y-5'
      }}
    >
      <ListboxItem
        color='primary'
        classNames={{
          title: 'text-lg',
        }}
        key='create'
      >
        <Link to='/create'> {t('Create')} + </Link>
      </ListboxItem>
      <ListboxItem classNames={{
        title: 'overflow-visible!'
      }} key='messages'>
        <Badge isInvisible={!messageCount_data} content={messageCount_data} size='md' variant='shadow' shape='rectangle' color='danger'>
          <Link to='/messages'> {t('Messages')} </Link>
        </Badge>
      </ListboxItem>
      <ListboxItem key='profile'>
        <Link to='/profile'> {t('Profile')} </Link>
      </ListboxItem>
      <ListboxItem key='settings'>
        <Link to='/settings'> {t('Settings')} </Link>
      </ListboxItem>
      <ListboxItem key='reviews'>
        <Link to='/reviews'> {t('Reviews')} </Link>
      </ListboxItem>
      <ListboxItem key='favorites'>
        <Link to='/favorites'> {t('Favorites')} </Link>
      </ListboxItem>

      <ListboxItem onClick={logout} key='logout' color='danger'>
        {t('Log Out')}
      </ListboxItem>
    </Listbox>
  )
}
