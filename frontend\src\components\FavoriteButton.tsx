import { EmptyHeartIcon } from '@/lib/svg/EmptyHeartIcon'
import FillHeartIcon from '@/lib/svg/FillHeartIcon'
import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { useUserStore } from '@/store'
import { trpc } from '@/trpc'
import { Spinner } from "@heroui/react"

import { CaseWithPartialRelations } from '../../../server/prisma/generated/zod/modelSchema'

interface Props {
  onChange?: (value: boolean) => void
  c: CaseWithPartialRelations
}

export const FavoriteButton = ({ onChange, c }: Props) => {
  
  const { data: userData } = useUserStore()
  const [isFavorite, setIsFavorite] = useState<boolean>(!!c?.favorites?.find((x) => x.userId == userData?.id))

  const { isError, error, isSuccess, isPending: isLoading, mutateAsync } = trpc.case.toFavofite.useMutation()

  // useEffect(() => {
  //   isError && toast.error('Something went wrong, try again later')
  //   isSuccess && toast(isFavorite ? 'added to favorites' : 'removed from favorites')
  // }, [isError, isSuccess])

  function clickHandler() {
    if (!c) throw new Error('c is undefined')

    setIsFavorite((value) => {
      onChange?.(!value)
      mutateAsync(c.id)
      return !value
    })
  }

  return (
    <>
      {isLoading ? (
        <Spinner size='sm' color='danger'></Spinner>
      ) : (
        <div className='cursor-pointer' onClick={clickHandler}>
          {isFavorite ? (
            <motion.div initial={{ scale: 1 }} whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
              <FillHeartIcon className='cursor-pointer w-6 text-danger-400 h-6' />
            </motion.div>
          ) : (
            <motion.div initial={{ scale: 1 }} whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
              <EmptyHeartIcon className='cursor-pointer text-danger-400 w-6 h-6' />
            </motion.div>
          )}
        </div>
      )}
    </>
  )
}
