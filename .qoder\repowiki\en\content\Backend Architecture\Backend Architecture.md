# Backend Architecture

<cite>
**Referenced Files in This Document**   
- [server.ts](file://server/src/server.ts)
- [config.ts](file://server/src/config.ts)
- [better-auth.ts](file://server/src/providers/better-auth.ts)
- [prisma.ts](file://server/src/providers/prisma.ts)
- [ws.ts](file://server/src/providers/ws.ts)
- [case.ts](file://server/src/services/case.ts)
- [geo.ts](file://server/src/services/geo.ts)
- [router/index.ts](file://server/src/router/index.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Server Initialization and Plugin Registration](#server-initialization-and-plugin-registration)
4. [Module System and Code Organization](#module-system-and-code-organization)
5. [Authentication and Session Management](#authentication-and-session-management)
6. [WebSocket and Real-Time Communication](#websocket-and-real-time-communication)
7. [Service Layer and Business Logic](#service-layer-and-business-logic)
8. [API Structure and Routing](#api-structure-and-routing)
9. [Error Handling and Logging](#error-handling-and-logging)
10. [Process Management and Scheduling](#process-management-and-scheduling)
11. [Internationalization and Localization](#internationalization-and-localization)
12. [Infrastructure and Scalability](#infrastructure-and-scalability)
13. [Security Measures](#security-measures)
14. [Deployment Topology](#deployment-topology)
15. [System Context Diagram](#system-context-diagram)

## Introduction
The backend component of takeNpass is built using Fastify with TypeScript to deliver high-performance APIs. The architecture follows a modular design pattern with clear separation of concerns between providers, services, routers, and utilities. The system leverages tRPC for type-safe API endpoints, Prisma for database access, and Better Auth for authentication. This document provides a comprehensive overview of the backend architecture, focusing on server initialization, module organization, real-time communication, and deployment considerations.

## Project Structure
The backend is organized into a well-structured directory hierarchy that promotes maintainability and scalability. The core components are grouped by responsibility, with clear boundaries between different layers of the application.

```mermaid
graph TD
A[server/] --> B[src/]
A --> C[prisma/]
A --> D[test/]
B --> E[providers/]
B --> F[router/]
B --> G[services/]
B --> H[shemas/]
B --> I[types/]
B --> J[utils/]
B --> K[config.ts]
B --> L[server.ts]
E --> M[auth.ts]
E --> N[better-auth.ts]
E --> O[prisma.ts]
E --> P[ws.ts]
F --> Q[routers/]
F --> R[context.ts]
F --> S[trpc.ts]
G --> T[case.ts]
G --> U[geo.ts]
G --> V[message.ts]
G --> W[notification.ts]
G --> X[user.ts]
```

**Diagram sources**
- [server/src](file://server/src)

**Section sources**
- [server/src](file://server/src)

## Server Initialization and Plugin Registration
The server initialization process in server.ts follows a structured approach to register plugins, configure middleware, and set up the application environment. The createServer function accepts configuration options and returns a Fastify instance with start and stop lifecycle methods.

The server registers several key plugins:
- fastifyCors for cross-origin resource sharing
- fastifyIO for WebSocket integration
- fastifyTRPCPlugin for tRPC endpoint handling
- fastifyCookie for cookie-based session management
- i18nextMiddleware for internationalization

```mermaid
sequenceDiagram
participant Server as Fastify Server
participant Plugin as Plugin Registration
participant Start as Start Process
Start->>Server : createServer(opts)
Server->>Plugin : regPlugins()
Plugin->>Plugin : register CORS
Plugin->>Plugin : register Socket.IO
Plugin->>Plugin : register i18next
Plugin->>Plugin : register tRPC
Plugin->>Plugin : register Cookie
Plugin-->>Server : Plugins registered
Server->>Start : await server.listen()
Start-->>Server : Server listening
```

**Diagram sources**
- [server.ts](file://server/src/server.ts#L253)

**Section sources**
- [server.ts](file://server/src/server.ts#L253)

## Module System and Code Organization
The backend employs a modular architecture with clearly defined responsibilities. The code organization follows these principles:

- **Providers**: Handle external integrations and global services (database, auth, WebSocket)
- **Router**: Manages API routing through tRPC with type-safe endpoints
- **Services**: Contain business logic and interact with providers
- **Schemas**: Define data validation rules
- **Types**: Provide TypeScript interfaces for data structures
- **Utils**: Offer reusable helper functions

This separation ensures that business logic remains decoupled from transport mechanisms and external dependencies, promoting testability and maintainability.

**Section sources**
- [server/src](file://server/src)

## Authentication and Session Management
Authentication is implemented using Better Auth, which provides a robust session management system. The configuration includes:

- Email/password authentication
- 7-day session expiration
- Daily session refresh
- Cookie-based session storage
- CSRF protection through signed cookies

The authentication routes are proxied through Fastify to Better Auth's handler, allowing seamless integration with the existing middleware chain. Session validation occurs during WebSocket connection establishment, ensuring only authenticated users can establish real-time connections.

```mermaid
classDiagram
class betterAuth {
+baseURL : string
+secret : string
+database : PrismaAdapter
+session : SessionConfig
+emailAndPassword : EmailConfig
}
class SessionConfig {
+expiresIn : number
+updateAge : number
+cookieCache : CookieCacheConfig
}
class PrismaAdapter {
+prismaClient : PrismaClient
+provider : string
}
betterAuth --> SessionConfig : "has"
betterAuth --> PrismaAdapter : "uses"
```

**Diagram sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L34)
- [auth.ts](file://server/src/providers/auth.ts#L7)

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L34)
- [auth.ts](file://server/src/providers/auth.ts#L7)

## WebSocket and Real-Time Communication
Real-time communication is handled through Socket.IO integrated with Fastify via fastify-socket.io. The WebSocket provider (ws.ts) manages client connections and maintains a registry of connected users.

Key features include:
- Session-based user identification
- User-to-socket mapping
- Connection lifecycle management
- Secure message broadcasting
- Automatic client cleanup on disconnect

The IO class provides static methods for sending messages to specific users, enabling targeted notifications and real-time updates without requiring clients to poll for changes.

**Section sources**
- [ws.ts](file://server/src/providers/ws.ts#L51)

## Service Layer and Business Logic
The service layer contains the core business logic of the application. Each service class follows a static pattern with methods that operate on data models.

Key services include:
- **CaseService**: Manages case creation, assignment, and lifecycle
- **GeoService**: Handles geolocation lookups and reverse geocoding
- **NotificationService**: Manages user notifications
- **UserService**: Handles user-related operations
- **MessageService**: Manages messaging functionality

The CaseService, for example, implements complex business rules for case management, including automatic closure of expired cases and notification of subscribers when new cases are created.

```mermaid
classDiagram
class CaseService {
+onServerInit() : Promise~UpdateManyResult~
+findSubscribesAfterCreate(createdCase) : Promise~SubscribersResult~
+toFavorite(params) : Promise~FavoriteResult~
+id(id : string) : Promise~Case~
+myList(type : CasesType, userId : string) : Promise~Case[]~
+list(params : CaseListParams) : Promise~CaseListResult~
+update(payload : UpdateCasePayload) : Promise~UpdateResult~
+create(payload : CreateCasePayload) : Promise~Case~
+assignClient(caseId : string, client : User) : Promise~AssignmentResult~
-formatCaseForApi(case : Case) : FormattedCase
-makeLatLonNearbyPoints(params) : LatLonType
}
class GeoService {
+findLocation(query : string, lang : string) : Promise~GeoMeta[]~
+reverse(params : ReverseParams) : Promise~GeoMeta | GeoMeta[]~
-enabledServiceList : AvailableGeoService[]
-services : GeoServiceMap
}
CaseService --> GeoService : "uses"
CaseService --> NotificationService : "uses"
CaseService --> UserService : "uses"
```

**Diagram sources**
- [case.ts](file://server/src/services/case.ts#L636)
- [geo.ts](file://server/src/services/geo.ts#L219)

**Section sources**
- [case.ts](file://server/src/services/case.ts#L636)
- [geo.ts](file://server/src/services/geo.ts#L219)

## API Structure and Routing
The API structure is built on tRPC with Fastify integration, providing type-safe endpoints. The routing system uses a modular approach with separate routers for different domain entities.

The main API endpoints include:
- Users: User management and authentication
- Geo: Geolocation and reverse geocoding
- Case: Case creation, listing, and management
- Notification: User notifications
- Message: Messaging functionality

All tRPC routes are mounted under the /trpc prefix, enabling consistent URL patterns and easy versioning. The context is properly typed and injected into all route handlers, ensuring type safety throughout the API layer.

**Section sources**
- [router/index.ts](file://server/src/router/index.ts#L21)

## Error Handling and Logging
The backend implements comprehensive error handling and logging mechanisms. Fastify's built-in logger is used for production logging, with different verbosity levels based on the environment.

Error handling includes:
- Global error boundaries
- Structured error responses
- Detailed logging of exceptions
- Graceful shutdown on critical errors
- Input validation errors with descriptive messages

The server catches unhandled exceptions during startup and logs them appropriately before exiting, preventing silent failures in production environments.

**Section sources**
- [server.ts](file://server/src/server.ts#L253)

## Process Management and Scheduling
The backend includes a process scheduling mechanism for running periodic tasks. The runServicesAtMidnight function ensures that certain services are executed daily at midnight.

Currently, the CaseService.onServerInit method is scheduled to run daily, which:
- Identifies cases that have passed their expiration date
- Automatically closes expired cases
- Logs the operation for audit purposes

The scheduling logic includes instance awareness through NODE_APP_INSTANCE, ensuring that scheduled tasks run only on designated instances in clustered environments.

**Section sources**
- [server.ts](file://server/src/server.ts#L253)

## Internationalization and Localization
Internationalization is implemented using i18next with a custom database-backed translation system. The backend loads translations from the database at startup and makes them available through middleware.

Key features include:
- Database-stored translations
- Dynamic language switching
- Middleware integration
- Support for multiple languages (en, ru, es)

The loadLocalesFromDatabase function queries the translations table and structures the data in a format compatible with i18next, allowing for dynamic content updates without requiring application restarts.

**Section sources**
- [server.ts](file://server/src/server.ts#L253)

## Infrastructure and Scalability
The backend is designed with scalability in mind, supporting both vertical and horizontal scaling strategies. The architecture allows for:

- Stateless application instances
- Shared database backend
- Distributed caching potential
- Load-balanced deployment
- Independent scaling of components

The use of tRPC enables efficient serialization and deserialization of data, reducing network overhead. The modular design allows for future microservices decomposition if needed.

**Section sources**
- [server.ts](file://server/src/server.ts#L253)

## Security Measures
The backend implements multiple security measures to protect user data and prevent common vulnerabilities:

- HTTPS enforcement through reverse proxy
- CORS policy configuration
- CSRF protection via signed cookies
- Input validation through type checking
- Rate limiting potential through middleware
- Secure session management
- Environment variable protection
- Database query parameterization

The authentication system uses modern security practices including secure cookie settings and proper session expiration.

**Section sources**
- [server.ts](file://server/src/server.ts#L253)
- [better-auth.ts](file://server/src/providers/better-auth.ts#L34)

## Deployment Topology
The recommended deployment topology includes:

- Reverse proxy (Nginx or similar) for SSL termination
- Multiple application instances behind a load balancer
- Separate database server
- Redis for potential caching and session storage
- Monitoring and logging infrastructure
- CI/CD pipeline for automated deployments

The application can be deployed in containerized environments (Docker) or on traditional server infrastructure. The configuration is environment-aware through environment variables.

**Section sources**
- [server.ts](file://server/src/server.ts#L253)
- [config.ts](file://server/src/config.ts#L30)

## System Context Diagram
The following diagram illustrates the overall system context, showing the relationships between the backend components, external services, and the frontend application.

```mermaid
graph TD
A[Frontend] --> |HTTP/HTTPS| B[Backend API]
A --> |WebSocket| C[Real-Time Service]
B --> D[Database]
B --> E[Authentication Service]
B --> F[Geolocation Services]
C --> G[Session Store]
B --> H[Translation Database]
F --> I[Geoapify]
F --> J[LocationIQ]
E --> K[User Database]
B --> L[Logging Service]
subgraph "Backend"
B
C
H
end
subgraph "External Services"
I
J
K
L
end
```

**Diagram sources**
- [server.ts](file://server/src/server.ts#L253)
- [config.ts](file://server/src/config.ts#L30)
- [better-auth.ts](file://server/src/providers/better-auth.ts#L34)
- [geo.ts](file://server/src/services/geo.ts#L219)

**Section sources**
- [server.ts](file://server/src/server.ts#L253)
- [config.ts](file://server/src/config.ts#L30)
- [better-auth.ts](file://server/src/providers/better-auth.ts#L34)
- [geo.ts](file://server/src/services/geo.ts#L219)