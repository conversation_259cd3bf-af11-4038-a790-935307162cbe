# Message Router

<cite>
**Referenced Files in This Document**  
- [message.ts](file://server/src/router/routers/message.ts)
- [message.ts](file://server/src/services/message.ts)
- [ChatPage.tsx](file://frontend/src/pages/ChatPage.tsx)
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts)
- [chat.ts](file://frontend/src/types/chat.ts)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [tRPC Message Procedures](#trpc-message-procedures)
3. [Input Validation and Security](#input-validation-and-security)
4. [Message Threading and User Relationships](#message-threading-and-user-relationships)
5. [Real-Time Delivery Status via WebSocket](#real-time-delivery-status-via-websocket)
6. [Frontend Component Interaction](#frontend-component-interaction)
7. [Message History and Pagination](#message-history-and-pagination)
8. [Read Status Management](#read-status-management)
9. [Attachment Handling](#attachment-handling)
10. [Security Considerations](#security-considerations)

## Introduction
The Message Router implementation in the takeNpass application enables secure, real-time messaging between users. Built on tRPC, it provides a type-safe interface for sending, retrieving, and managing chat messages. The system supports message threading by user relationships, integrates with WebSocket for delivery status updates, and ensures message privacy through authentication and access control. This document details the architecture, procedures, and frontend integration of the messaging system.

## tRPC Message Procedures
The message router exposes several tRPC procedures for core messaging functionality:

- **send**: Creates a new message from authenticated user to a specified recipient.
- **partners**: Retrieves a list of users the current user has exchanged messages with.
- **unreadCount**: Returns the count of unread messages for the current user.
- **chats**: Fetches all message threads grouped by conversation partner.
- **messagesByPartner**: Retrieves all messages between the current user and a specific partner.
- **setAsRead**: Marks a specific message as read.
- **getPartnerById**: Fetches user details of a message partner.

These procedures are protected by `authedProcedure`, ensuring only authenticated users can access them.

**Section sources**
- [message.ts](file://server/src/router/routers/message.ts#L1-L70)

## Input Validation and Security
Input validation is implemented using Zod schemas to ensure data integrity and prevent injection attacks:

- **SendInputShema** validates that `receiverId` is a string and `content` is a non-empty string.
- Content length is implicitly limited to 254 characters via frontend `maxLength` and likely enforced at the database level.
- All procedures use `checkIsAuthed(ctx)` to verify user authentication before processing requests.
- Message creation includes server-side enforcement of `senderId` from session data, preventing spoofing.

**Section sources**
- [message.ts](file://server/src/router/routers/message.ts#L3-L10)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx#L85)

## Message Threading and User Relationships
Messages are threaded by user pairs, forming bidirectional conversations. The `messagesByPartner` service method queries messages where either the current user or partner is sender or receiver, ordered by creation time. The `chats` method groups messages by partner, calculating unread counts per conversation. The `partners` method identifies all unique message participants, filtering out the current user.

```mermaid
flowchart TD
A["User A sends message to User B"] --> B["Message stored with senderId=A, receiverId=B"]
B --> C["Conversation thread formed between A and B"]
C --> D["Messages ordered by createdAt ascending"]
D --> E["Unread count calculated for incoming messages"]
```

**Diagram sources**
- [message.ts](file://server/src/services/message.ts#L100-L130)
- [message.ts](file://server/src/services/message.ts#L132-L180)

**Section sources**
- [message.ts](file://server/src/services/message.ts#L100-L180)

## Real-Time Delivery Status via WebSocket
While direct WebSocket integration for message delivery is not evident in current code, delivery status is reflected in the UI. The `MessageItem` component displays "Delivered" (single check) when a message is sent, and "Read" (double check) when marked as read. The `useMessageRead` hook uses Intersection Observer to automatically mark messages as read when they become visible in the viewport, triggering the `setAsRead` tRPC mutation.

```mermaid
sequenceDiagram
participant Client as "Frontend"
participant Hook as "useMessageRead"
participant TRPC as "tRPC setAsRead"
participant Server as "MessageService"
Client->>Hook : MessageItem becomes visible
Hook->>Hook : isVisible = true
Hook->>TRPC : mutateAsync({ messageId })
TRPC->>Server : setAsRead(userId, messageId)
Server->>Server : Update message.read = true
Server-->>TRPC : Success
TRPC-->>Hook : onSuccess
Hook-->>Client : Update UI to show "Read"
```

**Diagram sources**
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx#L25-L45)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts#L20-L45)

**Section sources**
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx#L25-L45)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts#L1-L66)

## Frontend Component Interaction
Key frontend components interact with the message router as follows:

- **ChatPage.tsx**: Orchestrates the chat interface, managing active user state and rendering `ChatPartners` and `MainChat` components.
- **MessageItem.tsx**: Renders individual messages, showing sender avatar, content, timestamp, and read status. Uses `useMessageRead` to handle read receipts.
- **ChatInput.tsx**: Provides message composition interface with send button and Enter-to-send functionality. Limits input to 254 characters.

The `ChatPage` initializes with an optional `partnerId` from URL search params, enabling deep linking to specific conversations.

**Section sources**
- [ChatPage.tsx](file://frontend/src/pages/ChatPage.tsx#L1-L41)
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx#L1-L112)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx#L1-L91)

## Message History and Pagination
The current implementation retrieves all messages in a conversation without pagination. The `messagesByPartner` service method returns all messages between two users, ordered by creation time. While this works for low-volume conversations, it may impact performance for long-running chats. No explicit pagination parameters (skip/take) are present in the current API, suggesting either full-load strategy or potential future enhancement.

**Section sources**
- [message.ts](file://server/src/services/message.ts#L100-L115)

## Read Status Management
Read status is managed through a combination of frontend observation and backend updates:

- Messages have a `read: boolean | null` field indicating delivery and read status.
- The `useMessageRead` hook monitors message visibility using Intersection Observer.
- When a non-owned message becomes visible, it triggers `setAsRead` mutation.
- The `MessageService.setAsRead` method updates the database record.
- UI reflects status with single check (delivered) and double check (read) indicators.

This system ensures read receipts are accurate while minimizing unnecessary server calls.

```mermaid
classDiagram
class ChatMessage {
+id : number
+createdAt : Date
+content : string
+senderId : string
+receiverId : string
+read : boolean | null
+sender : ChatUser
+receiver : ChatUser
}
class useMessageRead {
+markMessageAsRead(message, isVisible)
+processedMessages : Set<number>
}
class MessageService {
+setAsRead(userId, messageId)
+unreadCount(userId)
}
ChatMessage --> useMessageRead : "triggers"
useMessageRead --> MessageService : "calls setAsRead"
MessageService --> ChatMessage : "updates read status"
```

**Diagram sources**
- [chat.ts](file://frontend/src/types/chat.ts#L3-L15)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts#L1-L66)
- [message.ts](file://server/src/services/message.ts#L3-L15)

**Section sources**
- [chat.ts](file://frontend/src/types/chat.ts#L3-L15)
- [useMessageRead.ts](file://frontend/src/hooks/useMessageRead.ts#L1-L66)
- [message.ts](file://server/src/services/message.ts#L3-L15)

## Attachment Handling
The current implementation does not support message attachments. The `content` field is a plain string, and no file upload endpoints or attachment models are present in the analyzed code. Message composition is limited to text input with a 254-character limit. Future implementation of attachments would require new endpoints for file upload, storage, and message referencing.

**Section sources**
- [chat.ts](file://frontend/src/types/chat.ts#L3-L15)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx#L85)

## Security Considerations
The messaging system incorporates several security measures:

- **Authentication**: All procedures require authenticated sessions via `authedProcedure`.
- **Authorization**: Users can only send messages as themselves (senderId from session) and read messages they are recipients of.
- **Input Validation**: Zod schema enforces non-empty content and proper receiver ID.
- **Rate Limiting**: While not explicitly shown, the `rateLimit.ts` utility suggests potential rate limiting capabilities.
- **Privacy**: Message content is only accessible to sender and receiver; no public endpoints expose message data.
- **Read Receipts**: Automatic read marking only occurs for visible messages, preventing false receipts.

The system avoids exposing internal IDs in error messages and uses parameterized database queries through Prisma, mitigating injection risks.

**Section sources**
- [message.ts](file://server/src/router/routers/message.ts#L3-L10)
- [message.ts](file://server/src/services/message.ts#L3-L264)
- [utils/rateLimit.ts](file://server/src/utils/rateLimit.ts)