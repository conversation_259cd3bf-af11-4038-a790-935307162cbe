import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserCreateWithoutNotificationSettingsInputSchema } from './UserCreateWithoutNotificationSettingsInputSchema';
import { UserUncheckedCreateWithoutNotificationSettingsInputSchema } from './UserUncheckedCreateWithoutNotificationSettingsInputSchema';
import { UserCreateOrConnectWithoutNotificationSettingsInputSchema } from './UserCreateOrConnectWithoutNotificationSettingsInputSchema';
import { UserUpsertWithoutNotificationSettingsInputSchema } from './UserUpsertWithoutNotificationSettingsInputSchema';
import { UserWhereUniqueInputSchema } from './UserWhereUniqueInputSchema';
import { UserUpdateToOneWithWhereWithoutNotificationSettingsInputSchema } from './UserUpdateToOneWithWhereWithoutNotificationSettingsInputSchema';
import { UserUpdateWithoutNotificationSettingsInputSchema } from './UserUpdateWithoutNotificationSettingsInputSchema';
import { UserUncheckedUpdateWithoutNotificationSettingsInputSchema } from './UserUncheckedUpdateWithoutNotificationSettingsInputSchema';

export const UserUpdateOneRequiredWithoutNotificationSettingsNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutNotificationSettingsNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutNotificationSettingsInputSchema),z.lazy(() => UserUncheckedCreateWithoutNotificationSettingsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutNotificationSettingsInputSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutNotificationSettingsInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => UserUpdateToOneWithWhereWithoutNotificationSettingsInputSchema),z.lazy(() => UserUpdateWithoutNotificationSettingsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutNotificationSettingsInputSchema) ]).optional(),
}).strict();

export default UserUpdateOneRequiredWithoutNotificationSettingsNestedInputSchema;
