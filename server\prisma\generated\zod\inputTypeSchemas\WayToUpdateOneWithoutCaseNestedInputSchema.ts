import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayToCreateWithoutCaseInputSchema } from './WayToCreateWithoutCaseInputSchema';
import { WayToUncheckedCreateWithoutCaseInputSchema } from './WayToUncheckedCreateWithoutCaseInputSchema';
import { WayToCreateOrConnectWithoutCaseInputSchema } from './WayToCreateOrConnectWithoutCaseInputSchema';
import { WayToUpsertWithoutCaseInputSchema } from './WayToUpsertWithoutCaseInputSchema';
import { WayToWhereInputSchema } from './WayToWhereInputSchema';
import { WayToWhereUniqueInputSchema } from './WayToWhereUniqueInputSchema';
import { WayToUpdateToOneWithWhereWithoutCaseInputSchema } from './WayToUpdateToOneWithWhereWithoutCaseInputSchema';
import { WayToUpdateWithoutCaseInputSchema } from './WayToUpdateWithoutCaseInputSchema';
import { WayToUncheckedUpdateWithoutCaseInputSchema } from './WayToUncheckedUpdateWithoutCaseInputSchema';

export const WayToUpdateOneWithoutCaseNestedInputSchema: z.ZodType<Prisma.WayToUpdateOneWithoutCaseNestedInput> = z.object({
  create: z.union([ z.lazy(() => WayToCreateWithoutCaseInputSchema),z.lazy(() => WayToUncheckedCreateWithoutCaseInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => WayToCreateOrConnectWithoutCaseInputSchema).optional(),
  upsert: z.lazy(() => WayToUpsertWithoutCaseInputSchema).optional(),
  disconnect: z.union([ z.boolean(),z.lazy(() => WayToWhereInputSchema) ]).optional(),
  delete: z.union([ z.boolean(),z.lazy(() => WayToWhereInputSchema) ]).optional(),
  connect: z.lazy(() => WayToWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => WayToUpdateToOneWithWhereWithoutCaseInputSchema),z.lazy(() => WayToUpdateWithoutCaseInputSchema),z.lazy(() => WayToUncheckedUpdateWithoutCaseInputSchema) ]).optional(),
}).strict();

export default WayToUpdateOneWithoutCaseNestedInputSchema;
