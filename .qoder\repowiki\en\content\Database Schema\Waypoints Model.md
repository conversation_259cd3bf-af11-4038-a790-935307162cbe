# Waypoints Model

<cite>
**Referenced Files in This Document**   
- [CaseWithWay.ts](file://server/src/types/CaseWithWay.ts)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts)
- [WayFromSchema.ts](file://server/prisma/generated/zod/modelSchema/WayFromSchema.ts)
- [WayMiddleSchema.ts](file://server/prisma/generated/zod/modelSchema/WayMiddleSchema.ts)
- [WayToSchema.ts](file://server/prisma/generated/zod/modelSchema/WayToSchema.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Waypoint Entities](#core-waypoint-entities)
3. [Field Definitions](#field-definitions)
4. [Relationships with Case Entity](#relationships-with-case-entity)
5. [Geolocation Data Structure](#geolocation-data-structure)
6. [Spatial Indexing and Geolocation Validation](#spatial-indexing-and-geolocation-validation)
7. [Address Normalization Process](#address-normalization-process)
8. [Route Matching and Search Functionality](#route-matching-and-search-functionality)
9. [Time Window Constraints and Waypoint Ordering](#time-window-constraints-and-waypoint-ordering)
10. [Delivery Matching Logic](#delivery-matching-logic)
11. [Sample Queries](#sample-queries)

## Introduction
The Waypoint entities in the takeNpass system—WayFrom, WayTo, and WayMiddle—are fundamental components that define delivery routes. These entities store geographic coordinates, address metadata, time constraints, and user comments for each point along a route. WayFrom represents the origin, WayTo the destination, and WayMiddle the intermediate stops. Together, they enable precise route definition, spatial queries, and efficient delivery matching.

**Section sources**
- [CaseWithWay.ts](file://server/src/types/CaseWithWay.ts#L1-L23)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L0-L71)

## Core Waypoint Entities

The system defines three distinct waypoint types to model delivery routes comprehensively:

- **WayFrom**: Origin point of a delivery route
- **WayTo**: Destination point of a delivery route
- **WayMiddle**: Intermediate waypoints along the route

Each waypoint type shares a common structure but serves a specific role in route definition.

```mermaid
classDiagram
class WayFrom {
+id : number
+geometa : JsonValue
+date : Date | null
+lat : Decimal
+lon : Decimal
+caseId : string
+comment : string | null
}
class WayTo {
+id : number
+geometa : JsonValue
+date : Date | null
+lat : Decimal
+lon : Decimal
+caseId : string
+comment : string | null
}
class WayMiddle {
+id : number
+geometa : JsonValue
+date : Date | null
+lat : Decimal
+lon : Decimal
+caseId : string
+comment : string | null
}
class Case {
+id : string
+description : string | null
+baggage : string[] | string | null
+price : number | null
+isRequest : boolean
+expire_at : Date | null
+status : CaseStatus
}
Case "1" -- "1" WayFrom : has origin
Case "1" -- "1" WayTo : has destination
Case "1" -- "*" WayMiddle : has intermediates
```

**Diagram sources**
- [WayFromSchema.ts](file://server/prisma/generated/zod/modelSchema/WayFromSchema.ts#L0-L36)
- [WayMiddleSchema.ts](file://server/prisma/generated/zod/modelSchema/WayMiddleSchema.ts#L0-L36)
- [WayToSchema.ts](file://server/prisma/generated/zod/modelSchema/WayToSchema.ts#L0-L36)

**Section sources**
- [WayFromSchema.ts](file://server/prisma/generated/zod/modelSchema/WayFromSchema.ts#L0-L36)
- [WayMiddleSchema.ts](file://server/prisma/generated/zod/modelSchema/WayMiddleSchema.ts#L0-L36)
- [WayToSchema.ts](file://server/prisma/generated/zod/modelSchema/WayToSchema.ts#L0-L36)

## Field Definitions

Each waypoint entity contains the following fields:

**Waypoint Common Fields**
- `id`: Unique identifier (integer)
- `geometa`: JSON structure containing normalized address and geolocation metadata
- `date`: Optional timestamp for arrival/departure time window
- `lat`: Latitude stored as Decimal type
- `lon`: Longitude stored as Decimal type
- `caseId`: Foreign key linking to the parent Case
- `comment`: Optional user-provided description

**CaseWithWay Interface Fields**
- `description`: General case description
- `baggage`: Item description (string or array of strings)
- `price`: Optional monetary value
- `isRequest`: Boolean indicating if it's a service request
- `expire_at`: Expiration timestamp
- `status`: Current case status
- `from`: WayFrom payload
- `to`: WayTo payload
- `middlepoints`: Array of WayMiddle payloads

**Section sources**
- [CaseWithWay.ts](file://server/src/types/CaseWithWay.ts#L1-L23)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L0-L71)

## Relationships with Case Entity

The system implements two distinct relationship patterns between the Case entity and its waypoints:

- **One-to-One Relationships**: 
  - Each Case has exactly one WayFrom (origin)
  - Each Case has exactly one WayTo (destination)

- **One-to-Many Relationship**:
  - Each Case can have zero or more WayMiddle (intermediate waypoints)

These relationships are enforced through foreign key constraints where each waypoint references a Case via the `caseId` field. The schema ensures referential integrity and supports efficient joins for route reconstruction.

```mermaid
erDiagram
CASE {
string id PK
string description
json baggage
number price
boolean isRequest
timestamp expire_at
enum status
}
WAYFROM {
number id PK
json geometa
timestamp date
decimal lat
decimal lon
string caseId FK
string comment
}
WAYTO {
number id PK
json geometa
timestamp date
decimal lat
decimal lon
string caseId FK
string comment
}
WAYMIDDLE {
number id PK
json geometa
timestamp date
decimal lat
decimal lon
string caseId FK
string comment
}
CASE ||--|| WAYFROM : "origin"
CASE ||--|| WAYTO : "destination"
CASE ||--o{ WAYMIDDLE : "intermediates"
```

**Diagram sources**
- [WayFromSchema.ts](file://server/prisma/generated/zod/modelSchema/WayFromSchema.ts#L0-L36)
- [WayMiddleSchema.ts](file://server/prisma/generated/zod/modelSchema/WayMiddleSchema.ts#L0-L36)
- [WayToSchema.ts](file://server/prisma/generated/zod/modelSchema/WayToSchema.ts#L0-L36)

**Section sources**
- [WayFromSchema.ts](file://server/prisma/generated/zod/modelSchema/WayFromSchema.ts#L0-L36)
- [WayMiddleSchema.ts](file://server/prisma/generated/zod/modelSchema/WayMiddleSchema.ts#L0-L36)
- [WayToSchema.ts](file://server/prisma/generated/zod/modelSchema/WayToSchema.ts#L0-L36)

## Geolocation Data Structure

The `geometa` field contains rich geolocation metadata derived from geocoding services. The structure follows the GeoMetaSchema defined in the application:

- `address`: Normalized address components including:
  - `county`: Administrative division
  - `name`: Point of interest name
  - `house_number`: Building number
  - `road`: Street name
  - `city`: Urban center
  - `state`: State or province
  - `postcode`: ZIP or postal code
  - `country`: Nation name
  - `country_code`: ISO country code
  - `suburb`: Neighborhood or district
  - `location`: Full address string
  - `originalLocation`: User-provided input

- `lat`: Latitude coordinate
- `lon`: Longitude coordinate
- `display_name`: Human-readable location name
- `type`: Geocoding result type (e.g., "city", "address")

This structure enables both precise geolocation and human-readable address representation.

**Section sources**
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L0-L71)

## Spatial Indexing and Geolocation Validation

The system employs several strategies for efficient spatial operations:

- **Decimal Type for Coordinates**: Latitude and longitude are stored as Prisma.Decimal to ensure precision and avoid floating-point errors
- **Spatial Indexing**: Database indexes on lat/lon fields enable fast proximity queries
- **Validation Rules**:
  - Coordinates must be valid decimal numbers
  - Date fields are coerced to proper Date objects
  - Required fields are enforced through Zod schemas
  - Geometa structure is validated against GeoMetaSchema

The validation occurs at both API and database levels, ensuring data integrity throughout the system.

**Section sources**
- [WayFromSchema.ts](file://server/prisma/generated/zod/modelSchema/WayFromSchema.ts#L0-L36)
- [WayMiddleSchema.ts](file://server/prisma/generated/zod/modelSchema/WayMiddleSchema.ts#L0-L36)
- [WayToSchema.ts](file://server/prisma/generated/zod/modelSchema/WayToSchema.ts#L0-L36)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L0-L71)

## Address Normalization Process

Address normalization occurs through an automated geocoding pipeline:

1. **User Input**: Users provide origin and destination locations
2. **Geocoding Service**: The system queries Geoapify or LocationIQ services
3. **Response Processing**: Raw geocoding results are parsed and normalized
4. **Schema Validation**: Results are validated against GeoMetaSchema
5. **Storage**: Normalized data is stored in the geometa field

This process ensures consistent address representation across the system, enabling reliable matching and search functionality.

**Section sources**
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L0-L71)
- [GeoapifyGeocode.ts](file://server/src/types/GeoapifyGeocode.ts)
- [Locationiq.ts](file://server/src/types/Locationiq.ts)

## Route Matching and Search Functionality

Waypoints enable sophisticated route matching capabilities:

- **Exact Route Matching**: Cases with identical from/to points can be matched
- **Proximity-Based Search**: Radius searches around waypoints using lat/lon
- **Partial Route Matching**: Finding cases that share segments of a route
- **Time Window Alignment**: Matching cases with overlapping time constraints

The SearchCaseSchema validates search parameters, ensuring both from and to waypoints are properly structured for accurate matching.

```mermaid
flowchart TD
Start([Search Request]) --> Validate["Validate Search Parameters"]
Validate --> HasFrom{"Has 'from' point?"}
HasFrom --> |No| ReturnError["Return Error"]
HasFrom --> |Yes| HasTo{"Has 'to' point?"}
HasTo --> |No| ReturnError
HasTo --> |Yes| QueryDB["Query Cases by Proximity"]
QueryDB --> ApplyFilters["Apply Time, Price, Baggage Filters"]
ApplyFilters --> RankResults["Rank by Route Overlap"]
RankResults --> ReturnResults["Return Matched Cases"]
ReturnError --> End([Response])
ReturnResults --> End
```

**Diagram sources**
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L0-L71)

**Section sources**
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L0-L71)

## Time Window Constraints and Waypoint Ordering

Time constraints are managed through the `date` field in each waypoint:

- **Optional Time Windows**: The date field is nullable, allowing flexible scheduling
- **Sequential Ordering**: Waypoints are processed in chronological order
- **Expiration Control**: The case-level expire_at field sets overall validity

For intermediate waypoints (WayMiddle), the date field enables precise scheduling of multi-stop deliveries. The system can validate that waypoints are ordered chronologically and that time windows don't conflict.

**Section sources**
- [WayFromSchema.ts](file://server/prisma/generated/zod/modelSchema/WayFromSchema.ts#L0-L36)
- [WayMiddleSchema.ts](file://server/prisma/generated/zod/modelSchema/WayMiddleSchema.ts#L0-L36)
- [WayToSchema.ts](file://server/prisma/generated/zod/modelSchema/WayToSchema.ts#L0-L36)

## Delivery Matching Logic

Waypoints play a central role in the delivery matching algorithm:

1. **Route Similarity**: Calculate overlap between requester and provider routes
2. **Proximity Scoring**: Weight matches based on distance between corresponding waypoints
3. **Time Compatibility**: Verify alignment of time windows
4. **Constraint Validation**: Check baggage, price, and other requirements

The CaseWithWay interface provides the complete route data needed for these calculations, while the normalized geometa enables reliable comparison across different user inputs.

**Section sources**
- [CaseWithWay.ts](file://server/src/types/CaseWithWay.ts#L1-L23)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L0-L71)

## Sample Queries

### Route Validation Query
```typescript
// Validate a complete route before creation
const validationResult = CaseCreateSchema.safeParse({
  from: {
    lat: 55.7558,
    lon: 37.6176,
    geometa: { /* Moscow location data */ },
    date: "2023-12-01T10:00:00Z"
  },
  to: {
    lat: 48.8566,
    lon: 2.3522,
    geometa: { /* Paris location data */ },
    date: "2023-12-05T14:00:00Z"
  },
  middlepoints: [
    {
      lat: 52.5200,
      lon: 13.4050,
      geometa: { /* Berlin location data */ },
      date: "2023-12-03T12:00:00Z"
    }
  ]
});
```

### Proximity-Based Case Discovery
```typescript
// Find cases within 50km radius of a point
const searchParams = CaseListParamsSchema.parse({
  from: { lat: 55.7558, lon: 37.6176 },
  to: { lat: 48.8566, lon: 2.3522 },
  radius: 50,
  dates: [
    new Date("2023-12-01"),
    new Date("2023-12-05")
  ]
});
```

**Section sources**
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L0-L71)