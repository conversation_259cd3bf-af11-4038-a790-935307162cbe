# Notification Service

<cite>
**Referenced Files in This Document**   
- [notification.ts](file://server/src/services/notification.ts)
- [notification.router.ts](file://server/src/router/routers/notification.ts)
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts)
- [ws.ts](file://server/src/providers/ws.ts)
- [schema.prisma](file://server/prisma/schema.prisma)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx)
- [socket.ts](file://frontend/src/socket.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Domain Model and Data Structure](#domain-model-and-data-structure)
3. [Notification Types and Event Triggers](#notification-types-and-event-triggers)
4. [Service Implementation and Business Logic](#service-implementation-and-business-logic)
5. [WebSocket Integration and Real-Time Delivery](#websocket-integration-and-real-time-delivery)
6. [Frontend Notification Handling](#frontend-notification-handling)
7. [User Preferences and Notification Settings](#user-preferences-and-notification-settings)
8. [API Endpoints and Service Interfaces](#api-endpoints-and-service-interfaces)
9. [Common Issues and Solutions](#common-issues-and-solutions)
10. [Conclusion](#conclusion)

## Introduction

The Notification Service in the takeNpass backend is responsible for managing user notifications across various events such as new requests, message arrivals, case updates, and reviews. It supports real-time delivery via WebSocket, persistence through Prisma ORM, and localization using i18next. The service ensures users are promptly informed of relevant activities while allowing them to manage their notification preferences.

This document provides a comprehensive overview of the notification system's architecture, implementation details, and integration points with both frontend and backend components.

## Domain Model and Data Structure

The core domain model for notifications is defined in the Prisma schema and includes key entities such as `Notification`, `User`, and `NotificationSettings`. These models establish relationships for sender-receiver dynamics, read status tracking, and user-specific delivery preferences.

```mermaid
erDiagram
USER {
string id PK
string email UK
string username
string language
boolean confirmed
}
NOTIFICATION {
int id PK
datetime createdAt
boolean read
string translationKey
string userId FK
string senderId FK
string caseId FK
}
NOTIFICATIONSETTINGS {
string id PK
string userId FK
string method
boolean active
}
USER ||--o{ NOTIFICATION : "receives"
USER ||--o{ NOTIFICATION : "sends"
USER ||--o{ NOTIFICATIONSETTINGS : "has"
```

**Diagram sources**
- [schema.prisma](file://server/prisma/schema.prisma#L245-L280)

**Section sources**
- [schema.prisma](file://server/prisma/schema.prisma#L245-L280)

## Notification Types and Event Triggers

The system supports several predefined notification types, each tied to specific user actions or system events. These types are strongly typed using TypeScript to ensure consistency across the stack.

| Notification Type | Trigger Event | Associated Data |
|-------------------|-------------|-----------------|
| `NEW_REQUEST` | New trip request received | user, sender, case |
| `CANCEL_REQUEST` | Request cancellation | user, sender, case |
| `NEW_CASES_ON_SUBSCRIBE` | New cases matching subscription | user, case |
| `NEW_REVIEW` | New rating received | user, sender, case (optional) |
| `NEW_MESSAGE` | New message received | user, sender, content |

These types are defined in the `SocketNotificationType` interface and used throughout the system for type-safe event handling.

**Section sources**
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts#L2-L48)

## Service Implementation and Business Logic

The `NotificationService` class encapsulates all business logic related to notification creation, retrieval, and management. It leverages Prisma for database operations and integrates with the WebSocket provider for real-time delivery.

Key methods include:
- `createNotification`: Creates a new notification and emits it via WebSocket
- `getNotificationsForUser`: Retrieves paginated notifications with localization
- `markNotificationAsRead`: Updates read status for a specific notification
- `markAllNotificationAsRead`: Marks all notifications as read for a user
- `getUnreadNotificationsForUser`: Returns only unread notifications
- `deleteNotification`: Removes a notification from the system

The service uses partial user field selection to minimize data transfer and includes i18n message generation at creation time.

```mermaid
flowchart TD
Start([Create Notification]) --> ValidateInput["Validate User Data"]
ValidateInput --> CreateDB["Create in Database"]
CreateDB --> GenerateMessage["Generate i18n Message"]
GenerateMessage --> EmitSocket["Emit via WebSocket"]
EmitSocket --> End([Notification Delivered])
style Start fill:#4CAF50,stroke:#388E3C
style End fill:#4CAF50,stroke:#388E3C
```

**Diagram sources**
- [notification.ts](file://server/src/services/notification.ts#L25-L197)

**Section sources**
- [notification.ts](file://server/src/services/notification.ts#L25-L197)

## WebSocket Integration and Real-Time Delivery

Real-time notification delivery is achieved through Socket.IO integration. The `IO` class manages active user connections and enables targeted message emission based on user ID.

When a notification is created:
1. The service checks if the recipient has an active WebSocket connection
2. If connected, it formats a `SocketNotification` object with localized message
3. The notification is emitted directly to the user's socket under the `xnotification` event

The connection management includes automatic session validation using authentication cookies and maintains a map of user IDs to socket instances for efficient routing.

**Section sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [notification.ts](file://server/src/services/notification.ts#L75-L85)

## Frontend Notification Handling

The frontend component `Notifications.tsx` provides a user interface for viewing and managing notifications. It integrates with TRPC for API calls and Socket.IO for real-time updates.

Key features:
- Real-time toast notifications for incoming events
- Sidebar drawer with tabbed view (unread/read)
- Batch and individual mark-as-read functionality
- Dynamic template rendering based on notification type
- Badge counter for unread notifications

The component uses React Query for data fetching, Framer Motion for animations, and i18next for localization. It also dispatches custom events (e.g., `NEW_MESSAGE`) to update other parts of the application.

```mermaid
sequenceDiagram
participant Frontend
participant WebSocket
participant Backend
Backend->>WebSocket : emit('xnotification', data)
WebSocket->>Frontend : socket.on('xnotification')
Frontend->>Frontend : refetch notifications
Frontend->>Frontend : show toast notification
Frontend->>Frontend : update unread badge
```

**Diagram sources**
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L1-L323)
- [socket.ts](file://frontend/src/socket.ts#L1-L10)

**Section sources**
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L1-L323)
- [socket.ts](file://frontend/src/socket.ts#L1-L10)

## User Preferences and Notification Settings

Users can configure their notification preferences through the `NotificationSettings` model, which supports multiple delivery methods:
- EMAIL
- SMS
- TELEGRAM
- WHATSAPP

Each setting is associated with a user and can be individually activated or deactivated. Although the current implementation focuses on in-app notifications via WebSocket, the schema is designed to support multi-channel delivery.

The settings are stored per-user and can be extended to include channel-specific configurations such as email frequency, SMS limits, or webhook URLs for external services.

**Section sources**
- [schema.prisma](file://server/prisma/schema.prisma#L50-L65)

## API Endpoints and Service Interfaces

The notification service exposes a TRPC router with the following endpoints:

### GET `/notification/list`
- **Purpose**: Retrieve user's notification list
- **Authentication**: Required (`authedProcedure`)
- **Parameters**: None (uses session user ID)
- **Returns**: Array of `SocketNotification` objects
- **Includes**: Case, sender, and user data with localized messages

### POST `/notification/markAsRead`
- **Purpose**: Mark a single notification as read
- **Input**: Notification ID (number)
- **Returns**: Updated read status (boolean)
- **Behavior**: Triggers refetch on success

### POST `/notification/markAllAsRead`
- **Purpose**: Mark all notifications as read
- **Input**: None (uses session user ID)
- **Returns**: Count of updated notifications
- **Behavior**: Updates all unread notifications for the user

```mermaid
classDiagram
class NotificationService {
+createNotification(type, data)
+getNotificationsForUser(userId)
+markAllNotificationAsRead(userId)
+markNotificationAsRead(id, userId)
+getUnreadNotificationsForUser(userId)
+deleteNotification(id)
}
class NotificationRouter {
-list()
-markAsRead(input : number)
-markAllAsRead()
}
NotificationRouter --> NotificationService : "delegates"
```

**Diagram sources**
- [notification.router.ts](file://server/src/router/routers/notification.ts#L1-L26)
- [notification.ts](file://server/src/services/notification.ts#L25-L197)

**Section sources**
- [notification.router.ts](file://server/src/router/routers/notification.ts#L1-L26)

## Common Issues and Solutions

### Notification Deduplication
**Issue**: Potential duplicate notifications on rapid-fire events  
**Solution**: The system relies on database-level uniqueness constraints and idempotent operations. Future enhancements could include Redis-based deduplication using event hashes.

### Delivery Guarantees
**Issue**: Notifications may be lost if user is offline  
**Solution**: Persistent storage ensures notifications survive disconnections. Upon reconnection, clients fetch the full list. For critical alerts, future implementation could add retry mechanisms and fallback channels.

### Performance Considerations
- **Database Indexing**: All foreign keys are indexed for fast lookups
- **Field Selection**: Partial user fields reduce payload size
- **Caching**: Not currently implemented but could be added using Redis
- **Pagination**: Limited to 50 recent notifications to prevent overload

### Localization Challenges
- Messages are generated at notification creation time using recipient's language setting
- Uses i18next with dynamic key resolution
- Translation keys must exist in corresponding language files

**Section sources**
- [notification.ts](file://server/src/services/notification.ts#L25-L197)
- [schema.prisma](file://server/prisma/schema.prisma#L245-L280)

## Conclusion

The Notification Service in takeNpass provides a robust foundation for real-time user engagement with support for multiple event types, localization, and persistent storage. Its integration with WebSocket enables instant delivery while the Prisma-based data model ensures reliability and scalability.

Key strengths include type safety across the stack, clean separation of concerns, and extensibility for multi-channel delivery. Future improvements could focus on delivery guarantees for offline users, enhanced preference management, and analytics for notification effectiveness.

The service successfully balances immediate user feedback with persistent record-keeping, making it a critical component of the takeNpass user experience.