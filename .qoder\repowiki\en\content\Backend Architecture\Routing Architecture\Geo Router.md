# Geo Router

<cite>
**Referenced Files in This Document**   
- [geo.ts](file://server/src/router/routers/geo.ts)
- [geo.ts](file://server/src/services/geo.ts)
- [GeoMeta.ts](file://server/src/types/GeoMeta.ts)
- [MainMap.tsx](file://frontend/src/components/map/MainMap.tsx)
- [config.ts](file://server/src/config.ts)
- [trpc.ts](file://frontend/src/trpc.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [tRPC Procedures for Geolocation](#trpc-procedures-for-geolocation)
3. [External Service Integration](#external-service-integration)
4. [Map Component Usage](#map-component-usage)
5. [Input Validation and Error Handling](#input-validation-and-error-handling)
6. [Caching and Rate Limiting](#caching-and-rate-limiting)
7. [Data Transformation](#data-transformation)
8. [Conclusion](#conclusion)

## Introduction
The Geo Router implementation provides geolocation functionality for the application through tRPC endpoints. It supports address autocompletion, reverse geocoding, and location validation by integrating with external geolocation services. The system is designed to be resilient through fallback mechanisms and includes comprehensive error handling for external API failures.

**Section sources**
- [geo.ts](file://server/src/router/routers/geo.ts#L1-L41)
- [geo.ts](file://server/src/services/geo.ts#L1-L219)

## tRPC Procedures for Geolocation

The Geo Router exposes four main tRPC procedures for geolocation operations:

1. **findLocation**: Query procedure for address autocompletion based on a search query
2. **lazyFindLocation**: Mutation procedure for address autocompletion (used for non-idempotent operations)
3. **reverse**: Query procedure for reverse geocoding (coordinates to address)
4. **lazyReverse**: Mutation procedure for reverse geocoding

These procedures are rate-limited using a `RateLimitedQueue` with a concurrency of 2 and a 1-second delay between requests to prevent overwhelming external APIs.

```mermaid
sequenceDiagram
participant Frontend
participant tRPC
participant GeoService
participant ExternalAPI
Frontend->>tRPC : findLocation(query, lang)
tRPC->>GeoService : queue.add(findLocation)
GeoService->>ExternalAPI : Request to active service
ExternalAPI-->>GeoService : Raw geolocation data
GeoService-->>tRPC : Transformed GeoMeta[]
tRPC-->>Frontend : Autocomplete results
```

**Diagram sources**
- [geo.ts](file://server/src/router/routers/geo.ts#L1-L41)
- [geo.ts](file://server/src/services/geo.ts#L203-L218)

**Section sources**
- [geo.ts](file://server/src/router/routers/geo.ts#L1-L41)

## External Service Integration

The GeoService integrates with two external geolocation providers: Geoapify and LocationIQ. The system is configured to use both services with a fallback mechanism.

### Service Configuration
The active services are defined in the configuration:

```mermaid
classDiagram
class GeoConfig {
+activeGeoServices : AvailableGeoService[]
}
class AvailableGeoService {
+LocationIQ
+Geoapify
}
GeoConfig --> AvailableGeoService : "contains"
```

**Diagram sources**
- [config.ts](file://server/src/config.ts#L20-L30)

The integration follows a failover pattern where the system attempts to use each enabled service in sequence until a successful response is received.

### Geoapify Integration
- Uses environment variables `GEOPIFY_API_KEY` and `GEOPIFY_API_URL`
- Provides endpoints for search, autocomplete, and reverse geocoding
- Handles responses in Geoapify-specific formats

### LocationIQ Integration
- Uses environment variables `LOCATIONIQ_API_KEY` and `LOCATIONIQ_API_URL`
- Provides autocomplete and reverse geocoding endpoints
- Configured with parameters like `accept-language`, `dedupe`, and `normalizecity`

```mermaid
flowchart TD
A[GeoService Request] --> B{Try LocationIQ?}
B --> |Yes| C[Call LocationIQ API]
C --> D{Success?}
D --> |No| E[Try Geoapify]
E --> F[Call Geoapify API]
F --> G{Success?}
G --> |No| H[Return empty result]
G --> |Yes| I[Return transformed data]
D --> |Yes| I
```

**Diagram sources**
- [geo.ts](file://server/src/services/geo.ts#L1-L219)

**Section sources**
- [geo.ts](file://server/src/services/geo.ts#L1-L219)
- [config.ts](file://server/src/config.ts#L20-L30)

## Map Component Usage

The `MainMap.tsx` component utilizes the Geo Router endpoints for interactive map functionality.

### Search Functionality
The component implements debounced search with a 1-second delay using `useDebounce`:

```mermaid
sequenceDiagram
participant User
participant MainMap
participant tRPC
participant GeoRouter
User->>MainMap : Type in search input
MainMap->>MainMap : Debounce 1000ms
MainMap->>tRPC : lazyFindLocation(query, lang)
tRPC->>GeoRouter : Execute findLocation
GeoRouter-->>tRPC : GeoMeta[] results
tRPC-->>MainMap : Return results
MainMap->>User : Display location suggestions
```

**Diagram sources**
- [MainMap.tsx](file://frontend/src/components/map/MainMap.tsx#L1-L399)

### Reverse Geocoding on Click
When users click on the map, the component performs reverse geocoding:

```mermaid
sequenceDiagram
participant User
participant MainMap
participant tRPC
participant GeoRouter
User->>MainMap : Click on map
MainMap->>MainMap : Capture lat/lng
MainMap->>tRPC : lazyReverse(lat, lng, lang)
tRPC->>GeoRouter : Execute reverse
GeoRouter-->>tRPC : GeoMeta result
tRPC-->>MainMap : Return location data
MainMap->>User : Place marker and display info
```

**Diagram sources**
- [MainMap.tsx](file://frontend/src/components/map/MainMap.tsx#L1-L399)

The component also supports "Find me" functionality that uses browser geolocation to determine the user's position and reverse geocode it.

**Section sources**
- [MainMap.tsx](file://frontend/src/components/map/MainMap.tsx#L1-L399)

## Input Validation and Error Handling

### Input Validation
The tRPC procedures implement input validation using Zod schemas:

- **findLocation**: Requires a query string (minimum 1 character) and optional language parameter
- **reverse**: Requires latitude and longitude (string or number) and optional language parameter

The validation ensures that only properly formatted requests are processed.

### Error Handling
The system implements comprehensive error handling at multiple levels:

1. **External API Failures**: Each service wrapper catches errors and logs them without breaking the chain
2. **Fallback Mechanism**: If one service fails, the system automatically tries the next enabled service
3. **Graceful Degradation**: Returns empty arrays or undefined values rather than throwing exceptions
4. **Client-Side Error States**: The React components handle loading, error, and success states appropriately

```mermaid
flowchart TD
A[Service Request] --> B{API Call Success?}
B --> |Yes| C[Return Data]
B --> |No| D[Log Error]
D --> E{More Services Available?}
E --> |Yes| F[Try Next Service]
E --> |No| G[Return Default]
F --> A
```

**Diagram sources**
- [geo.ts](file://server/src/services/geo.ts#L203-L218)

**Section sources**
- [geo.ts](file://server/src/router/routers/geo.ts#L1-L41)
- [geo.ts](file://server/src/services/geo.ts#L1-L219)

## Caching and Rate Limiting

### Rate Limiting
The system implements rate limiting using a `RateLimitedQueue` with the following configuration:
- **Concurrency**: 2 simultaneous requests
- **Delay**: 1000ms between requests
- **Error Handling**: Errors are ignored to prevent cascading failures

This prevents the application from exceeding API rate limits on external services.

### Client-Side Caching
While there is no explicit server-side caching, the client-side implementation includes several caching mechanisms:
- **Debounced Search**: Prevents excessive requests during typing
- **Result Grouping**: Locations are grouped by state/country to improve display performance
- **State Management**: Results are stored in React component state to avoid re-fetching

The system could be enhanced with Redis or in-memory caching to further improve performance and reduce external API calls.

**Section sources**
- [geo.ts](file://server/src/router/routers/geo.ts#L1-L41)
- [geo.ts](file://server/src/services/geo.ts#L1-L219)
- [MainMap.tsx](file://frontend/src/components/map/MainMap.tsx#L1-L399)

## Data Transformation

The system transforms external API responses into a unified internal `GeoMeta` interface.

### GeoMeta Interface
The standardized data structure includes:

```typescript
interface GeoMeta {
  address: {
    country: string
    country_code: string
    county: any
    name: string
    city?: string
    state: string
    postcode?: number | string
    location: string
    originalLocation?: string
  }
  lat: number | string
  lon: number | string
  display_name?: string
  type?: string
}
```

### Transformation Process
Each external service has its own transformation method:

- **Geoapify**: Maps `features.properties` to `GeoMeta` fields, formats the display name using `replaceSpecialCharacters`
- **LocationIQ**: Maps OSM data to `GeoMeta` fields, handles array or single responses uniformly

The transformation ensures consistent data regardless of the underlying service, allowing the frontend to consume geolocation data without knowing the source.

```mermaid
classDiagram
class GeoMeta {
+address : Address
+lat : number|string
+lon : number|string
+display_name : string
+type : string
}
class Address {
+country : string
+country_code : string
+city : string
+state : string
+location : string
+originalLocation : string
}
class GeoapifyResponse {
+features : Feature[]
}
class Feature {
+properties : Properties
}
class Properties {
+formatted : string
+lat : number
+lon : number
+country : string
+country_code : string
}
class LocationiqResponse {
+display_name : string
+lat : string
+lon : string
+address : AddressComponent
}
GeoapifyResponse --> GeoMeta : "transformRes"
LocationiqResponse --> GeoMeta : "transformRes"
GeoMeta --> Address : "contains"
```

**Diagram sources**
- [GeoMeta.ts](file://server/src/types/GeoMeta.ts#L1-L22)
- [geo.ts](file://server/src/services/geo.ts#L1-L219)

**Section sources**
- [GeoMeta.ts](file://server/src/types/GeoMeta.ts#L1-L22)
- [geo.ts](file://server/src/services/geo.ts#L1-L219)

## Conclusion
The Geo Router implementation provides a robust geolocation system that integrates multiple external services with proper error handling and rate limiting. The tRPC procedures expose essential geolocation functionality to the frontend, where the MainMap component utilizes these endpoints for interactive map experiences. The data transformation layer ensures consistency across different API providers, while the failover mechanism improves reliability. Future enhancements could include explicit caching, more sophisticated debouncing, and additional geolocation providers for improved coverage and reliability.