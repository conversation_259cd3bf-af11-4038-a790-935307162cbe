import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { SortOrderSchema } from './SortOrderSchema';

export const WayFromMinOrderByAggregateInputSchema: z.ZodType<Prisma.WayFromMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  date: z.lazy(() => SortOrderSchema).optional(),
  lat: z.lazy(() => SortOrderSchema).optional(),
  lon: z.lazy(() => SortOrderSchema).optional(),
  caseId: z.lazy(() => SortOrderSchema).optional(),
  comment: z.lazy(() => SortOrderSchema).optional()
}).strict();

export default WayFromMinOrderByAggregateInputSchema;
