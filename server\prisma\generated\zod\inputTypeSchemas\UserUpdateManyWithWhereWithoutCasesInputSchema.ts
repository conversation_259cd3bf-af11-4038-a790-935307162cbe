import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserScalarWhereInputSchema } from './UserScalarWhereInputSchema';
import { UserUpdateManyMutationInputSchema } from './UserUpdateManyMutationInputSchema';
import { UserUncheckedUpdateManyWithoutCasesInputSchema } from './UserUncheckedUpdateManyWithoutCasesInputSchema';

export const UserUpdateManyWithWhereWithoutCasesInputSchema: z.ZodType<Prisma.UserUpdateManyWithWhereWithoutCasesInput> = z.object({
  where: z.lazy(() => UserScalarWhereInputSchema),
  data: z.union([ z.lazy(() => UserUpdateManyMutationInputSchema),z.lazy(() => UserUncheckedUpdateManyWithoutCasesInputSchema) ]),
}).strict();

export default UserUpdateManyWithWhereWithoutCasesInputSchema;
