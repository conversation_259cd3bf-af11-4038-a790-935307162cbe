# Notification API

<cite>
**Referenced Files in This Document**  
- [notification.ts](file://server/src/router/routers/notification.ts)
- [notification.tsx](file://frontend/src/components/Notifications.tsx)
- [notification.ts](file://server/src/services/notification.ts)
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts)
- [socket.ts](file://frontend/src/socket.ts)
- [trpc.ts](file://frontend/src/trpc.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Notification Endpoints](#notification-endpoints)
3. [Zod Validation Schemas](#zod-validation-schemas)
4. [tRPC Procedures](#trpc-procedures)
5. [Dual Notification System](#dual-notification-system)
6. [Integration with Notifications.tsx](#integration-with-notificationstsx)
7. [Client-Side Invocation Patterns](#client-side-invocation-patterns)
8. [Notification Lifecycle](#notification-lifecycle)
9. [Error Codes](#error-codes)
10. [Conclusion](#conclusion)

## Introduction
The Notification API in takeNpass provides a comprehensive system for managing user notifications through both pull-based retrieval via tRPC and push-based delivery via WebSocket. This document details the procedures for retrieving, marking as read, and managing notification settings. It also explains the integration between backend services and the frontend UI component, `Notifications.tsx`, and outlines the full lifecycle of notifications from generation to user interaction.

## Notification Endpoints

The notification system exposes three primary endpoints via tRPC:

- **list**: Retrieves all notifications for the authenticated user.
- **markAsRead**: Marks a specific notification as read.
- **markAllAsRead**: Marks all unread notifications as read.

These endpoints are secured using `authedProcedure`, ensuring only authenticated users can access their notifications.

**Section sources**
- [notification.ts](file://server/src/router/routers/notification.ts#L5-L26)

## Zod Validation Schemas

The only input validation in the current notification API is applied to the `markAsRead` mutation, which uses Zod to validate that the input is a number representing the notification ID:

```ts
z.number()
```

No explicit schema exists for filtering or settings updates, as the current implementation does not support updating notification preferences via API. All notifications are generated based on system events (e.g., new message, case request), and filtering is handled client-side.

**Section sources**
- [notification.ts](file://server/src/router/routers/notification.ts#L16)

## tRPC Procedures

The following tRPC procedures are defined in the `notificationRouter`:

### getNotifications (list)
- **Type**: Query
- **Authentication**: Required (`authedProcedure`)
- **Function**: Calls `NotificationService.getNotificationsForUser()` to retrieve up to 50 most recent notifications, ordered by creation date (descending).
- **Response**: Array of `SocketNotification` objects.

### markNotificationAsRead
- **Type**: Mutation
- **Authentication**: Required
- **Input**: Notification ID (number)
- **Function**: Calls `NotificationService.markNotificationAsRead()` to update the `read` status of a single notification.
- **Response**: Boolean indicating success.

### markAllAsRead
- **Type**: Mutation
- **Authentication**: Required
- **Function**: Invokes `NotificationService.markAllNotificationAsRead()` to set all unread notifications to read.
- **Response**: Number of notifications updated.

**Section sources**
- [notification.ts](file://server/src/router/routers/notification.ts#L5-L26)

## Dual Notification System

takeNpass implements a hybrid notification delivery model combining:

### Pull-Based Retrieval (tRPC)
- Notifications are fetched on demand using tRPC queries.
- The `list` query is used when the component mounts or when actions like "Mark as read" occur.
- Enables reliable state synchronization and offline support.

### Push-Based Delivery (WebSocket)
- Real-time notifications are pushed via WebSocket using the event `'xnotification'`.
- Server emits notifications immediately after creation via `IO.getUserSocket().emit()`.
- Client listens for `'xnotification'` events and triggers UI updates and toast notifications.

This dual approach ensures users receive immediate feedback while maintaining data consistency across sessions.

```mermaid
sequenceDiagram
participant Server
participant WebSocket
participant Client
participant UI
Server->>WebSocket : emit('xnotification', data)
WebSocket->>Client : Receive notification
Client->>UI : Show toast & refetch list
UI->>Server : tRPC list query
Server-->>UI : Return updated notifications
```

**Diagram sources**
- [notification.ts](file://server/src/router/routers/notification.ts#L5-L26)
- [notification.tsx](file://frontend/src/components/Notifications.tsx#L100-L130)
- [notification.ts](file://server/src/services/notification.ts#L40-L60)

## Integration with Notifications.tsx

The `Notifications.tsx` component serves as the primary UI for interacting with the notification system. Key integration points include:

- **tRPC Hooks**: Uses `trpc.notification.list.useQuery()` to fetch notifications and `useMutation()` for mark-as-read actions.
- **WebSocket Listener**: Registers a listener on `'xnotification'` to receive real-time updates.
- **Toast Notifications**: Displays ephemeral toasts for incoming notifications using `addToast()`.
- **Tabbed Interface**: Allows filtering between "Unread" and "Read" notifications.
- **Routing Integration**: Provides navigation links within notification templates (e.g., to messages, cases, reviews).

Each notification type has a dedicated template function (e.g., `NewMessageTemplate`, `NewReviewTemplate`) that renders localized content and action buttons.

**Section sources**
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L1-L324)

## Client-Side Invocation Patterns

### Polling (Pull)
Used for initial load and after mutations:
```ts
const { data, refetch } = trpc.notification.list.useQuery()
```
- Enabled conditionally based on user authentication.
- Automatically re-fetches after `markAsRead` or `markAllAsRead` mutations via `onSuccess: refetch`.

### Real-Time (Push)
Implemented via WebSocket:
```ts
socket.on('xnotification', (sdata) => {
  refetch()
  newToast(sdata)
})
```
- Triggers both a UI update (`refetch`) and a toast notification.
- Prevents duplicate toasts in certain contexts (e.g., already viewing messages).

**Section sources**
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L50-L130)

## Notification Lifecycle

1. **Generation**
   - Triggered by system events (e.g., `NEW_MESSAGE`, `NEW_REQUEST`).
   - `NotificationService.createNotification()` is called with `type` and `data`.
   - Persists notification in the database.

2. **Delivery**
   - Server retrieves user’s WebSocket connection via `IO.getUserSocket(userId)`.
   - Emits `'xnotification'` event with fully formatted `SocketNotification`.

3. **Client Reception**
   - Frontend receives event and calls `socketCB()`.
   - `refetch()` updates the notification list.
   - `newToast()` displays a temporary notification.

4. **User Interaction**
   - User views notifications in the drawer.
   - Can mark individual or all notifications as read.
   - Clicking "Go to" navigates to relevant page.

5. **Cleanup**
   - No automatic deletion; old notifications remain unless explicitly deleted via `deleteNotification()`.

```mermaid
flowchart TD
A[Event Occurs<br/>(e.g., New Message)] --> B[createNotification<br/>(type, data)]
B --> C[Save to DB]
C --> D[Emit via WebSocket]
D --> E[Client Receives<br/>xnotification Event]
E --> F[Show Toast + Refetch]
F --> G[User Views Notifications]
G --> H{Action?}
H --> |Mark Read| I[markAsRead Mutation]
H --> |Navigate| J[RouterLink]
I --> C1[Update DB: read=true]
C1 --> K[Refetch List]
```

**Diagram sources**
- [notification.ts](file://server/src/services/notification.ts#L20-L80)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L100-L130)

## Error Codes

While explicit HTTP error codes are not returned by the tRPC procedures, the following error conditions may occur:

- **Invalid Notification ID**
  - Occurs when calling `markAsRead` with a non-existent or invalid ID.
  - Result: `TRPCError` with code `NOT_FOUND` or `BAD_REQUEST`.
  - Source: `NotificationService.markNotificationAsRead()`.

- **Unauthorized Access**
  - Triggered when unauthenticated users attempt to access notifications.
  - Result: `TRPCError` with code `UNAUTHORIZED`.
  - Source: `authedProcedure` guard.

- **Settings Validation Failure**
  - Not applicable in current implementation, as no settings update endpoint exists.
  - Future implementations should use Zod for schema validation and return `BAD_REQUEST` on failure.

**Section sources**
- [notification.ts](file://server/src/router/routers/notification.ts#L5-L26)
- [notification.ts](file://server/src/services/notification.ts#L150-L170)

## Conclusion

The Notification API in takeNpass effectively combines tRPC-based querying with real-time WebSocket delivery to provide a responsive and reliable user experience. The system supports core operations such as listing, reading, and clearing notifications, with extensibility for future features like customizable notification settings. Tight integration with the `Notifications.tsx` component ensures seamless presentation and interaction, while the use of i18next enables full localization of notification content.