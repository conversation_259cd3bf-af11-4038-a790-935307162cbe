# User Management API

<cite>
**Referenced Files in This Document**  
- [user.ts](file://server/src/router/routers/user.ts)
- [user.ts](file://server/src/services/user.ts)
- [ProfilePage.tsx](file://frontend/src/pages/ProfilePage.tsx)
- [SettingsPage.tsx](file://frontend/src/pages/SettingsPage.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core User Management Procedures](#core-user-management-procedures)
3. [Zod Validation Schemas](#zod-validation-schemas)
4. [User Relationships: Favorites and Ratings](#user-relationships-favorites-and-ratings)
5. [Integration with Cases, Messages, and Notifications](#integration-with-cases-messages-and-notifications)
6. [Privacy and Access Control](#privacy-and-access-control)
7. [Client-Side Usage Examples](#client-side-usage-examples)
8. [Data Synchronization](#data-synchronization)
9. [Error Handling and Codes](#error-handling-and-codes)

## Introduction
The User Management API in takeNpass provides a secure and scalable interface for managing user profiles, preferences, and social interactions. Built on tRPC and Zod, it enables type-safe operations for retrieving and updating user data, managing ratings, and maintaining privacy across the platform. This document details the core procedures, validation rules, access controls, and integration points that govern user data.

## Core User Management Procedures

The API exposes several tRPC procedures for user data operations, categorized by access level and functionality.

### Public Procedures
- `profileInfo`: Retrieves public profile information of any user by ID.
- `login`: Authenticates a user with email and password, returning session data.
- `validate`: Validates the current session and returns user data if authenticated.
- `createUser`: Registers a new user with validated credentials.

### Authenticated Procedures
- `myProfileInfo`: Retrieves the authenticated user’s full profile.
- `updateProfile`: Updates the authenticated user’s profile data.
- `updateLanguage`: Changes the user’s language preference.
- `updateAvatar`: Updates the user’s profile picture via base64 string.
- `logout`: Ends the current session.

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L20-L265)

## Zod Validation Schemas

The API uses Zod for runtime type validation of input data. The following schemas define acceptable formats for user data.

### updateUserSchema
Validates profile update requests:
```ts
{
  username?: string (min: 4, max: 30)
  email?: valid email format
  password?: string (min: 4, max: 30)
  phone: string (7–15 digits, validated via libphonenumber-js)
  about_me?: string (max: 100 chars)
}
```

### createUserSchema
Ensures new user data meets requirements:
```ts
{
  username: string (min: 4, max: 30)
  email: valid email
  password: string (min: 4, max: 30)
  avatar?: base64 string (max: 3000 chars)
  phone: valid phone number
}
```

### setUserRatingSchema
Validates rating submissions:
```ts
{
  rating: integer (1–5)
  comment: string or null
  userId: string (target user ID)
  caseId: string (associated case ID)
}
```

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L10-L45)

## User Relationships: Favorites and Ratings

Users can rate each other based on shared cases. Ratings are tied to specific cases and require confirmation before becoming public.

### Key Procedures
- `setRating`: Submits or updates a rating for another user after a case interaction.
- `myRatings`: Retrieves all confirmed ratings received by the authenticated user.
- `sendedRatings`: Lists ratings the user has sent, optionally including case details.

Ratings are stored in a junction table with composite primary key (`caseId_userId_senderId`) to prevent duplicates.

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L50-L90)
- [user.ts](file://server/src/services/user.ts#L40-L100)

## Integration with Cases, Messages, and Notifications

User data is tightly integrated with other entities in the system.

### Case Integration
- Profile statistics include count of completed cases.
- Ratings are linked to specific cases, enabling context-aware feedback.
- `getMyCases` retrieves cases authored by the user.

### Messaging
- User profiles are displayed in chat interfaces via `ProfileInfo`.
- Messages are associated with user IDs for sender/receiver tracking.

### Notifications
- When a user receives a rating, a `NEW_REVIEW` notification is generated.
- Notifications include sender profile data for display.

```mermaid
flowchart TD
A[User Rates Another] --> B[setRating Procedure]
B --> C[Store Rating]
C --> D[Create Notification]
D --> E[Push to Recipient]
```

**Diagram sources**
- [user.ts](file://server/src/services/user.ts#L70-L85)
- [notification.ts](file://server/src/services/notification.ts)

**Section sources**
- [user.ts](file://server/src/services/user.ts#L70-L85)

## Privacy and Access Control

Access to user data is governed by strict rules to protect privacy.

### Visibility Rules
- Public: `username`, `avatar`, `language`, `rating count`, `completed cases count`.
- Private: `email`, `phone`, `about_me`, `full rating history`.
- Authenticated-Only: Full profile via `myProfileInfo`.

### Access Control
- Only authenticated users can update their own profile.
- Users cannot view others’ private fields.
- Session validation occurs via `ctx.user` injection in tRPC context.

```mermaid
graph TD
A[Client Request] --> B{Is Authenticated?}
B --> |No| C[Public Data Only]
B --> |Yes| D{Own Profile?}
D --> |Yes| E[Full Data + Edit Access]
D --> |No| F[Public Data Only]
```

**Diagram sources**
- [user.ts](file://server/src/router/routers/user.ts#L20-L265)

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L20-L265)

## Client-Side Usage Examples

### ProfilePage.tsx
Displays a user’s public profile. Accepts `userId` via query parameter.

```tsx
const { data } = trpc.users.profileInfo.useQuery(searchParams?.userId)
```

Renders `ProfileInfo` component with user data. Shows error if user not found.

**Section sources**
- [ProfilePage.tsx](file://frontend/src/pages/ProfilePage.tsx#L1-L37)

### SettingsPage.tsx
Accessed only by authenticated users. Renders `ProfileSettings` component for editing profile, language, and avatar.

```tsx
{sessionUser?.id && <ProfileSettings />}
```

**Section sources**
- [SettingsPage.tsx](file://frontend/src/pages/SettingsPage.tsx#L1-L25)

## Data Synchronization

Profile updates are synchronized across related entities.

- Language changes are reflected immediately in UI via i18n.
- Avatar updates propagate to all components using `ProfileContainer`.
- Rating submissions trigger real-time notifications.

The `update` method in `UserService` ensures atomic updates and strips non-digit characters from phone numbers before storage.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L200-L230)

## Error Handling and Codes

The API returns standardized tRPC error codes for client handling.

| Code | Scenario | Example |
|------|--------|--------|
| `UNAUTHORIZED` | Invalid session or login | Invalid credentials |
| `FORBIDDEN` | Action not allowed | Logout without active session |
| `NOT_FOUND` | User not found | Invalid email on login |
| `CONFLICT` | Duplicate data | Email already registered |
| `BAD_REQUEST` | Invalid input | Phone number format error |
| `INTERNAL_SERVER_ERROR` | Unexpected failure | Database connection lost |

Error logging is implemented server-side for debugging.

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L120-L265)