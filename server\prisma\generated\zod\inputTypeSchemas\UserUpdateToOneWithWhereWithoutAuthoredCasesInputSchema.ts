import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserWhereInputSchema } from './UserWhereInputSchema';
import { UserUpdateWithoutAuthoredCasesInputSchema } from './UserUpdateWithoutAuthoredCasesInputSchema';
import { UserUncheckedUpdateWithoutAuthoredCasesInputSchema } from './UserUncheckedUpdateWithoutAuthoredCasesInputSchema';

export const UserUpdateToOneWithWhereWithoutAuthoredCasesInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutAuthoredCasesInput> = z.object({
  where: z.lazy(() => UserWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => UserUpdateWithoutAuthoredCasesInputSchema),z.lazy(() => UserUncheckedUpdateWithoutAuthoredCasesInputSchema) ]),
}).strict();

export default UserUpdateToOneWithWhereWithoutAuthoredCasesInputSchema;
