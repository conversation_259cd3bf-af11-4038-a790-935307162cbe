import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayFromCreateWithoutCaseInputSchema } from './WayFromCreateWithoutCaseInputSchema';
import { WayFromUncheckedCreateWithoutCaseInputSchema } from './WayFromUncheckedCreateWithoutCaseInputSchema';
import { WayFromCreateOrConnectWithoutCaseInputSchema } from './WayFromCreateOrConnectWithoutCaseInputSchema';
import { WayFromWhereUniqueInputSchema } from './WayFromWhereUniqueInputSchema';

export const WayFromCreateNestedOneWithoutCaseInputSchema: z.ZodType<Prisma.WayFromCreateNestedOneWithoutCaseInput> = z.object({
  create: z.union([ z.lazy(() => WayFromCreateWithoutCaseInputSchema),z.lazy(() => WayFromUncheckedCreateWithoutCaseInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => WayFromCreateOrConnectWithoutCaseInputSchema).optional(),
  connect: z.lazy(() => WayFromWhereUniqueInputSchema).optional()
}).strict();

export default WayFromCreateNestedOneWithoutCaseInputSchema;
