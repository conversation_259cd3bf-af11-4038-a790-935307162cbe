# User Model

<cite>
**Referenced Files in This Document**   
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts)
- [RolesSchema.ts](file://server/prisma/generated/zod/inputTypeSchemas/RolesSchema.ts)
- [AccountSchema.ts](file://server/prisma/generated/zod/modelSchema/AccountSchema.ts)
- [SessionSchema.ts](file://server/prisma/generated/zod/modelSchema/SessionSchema.ts)
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts)
- [UserSeed.ts](file://server/prisma/seeds/UserSeed.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Fields](#core-fields)
3. [Relationships](#relationships)
4. [Data Constraints and Indexes](#data-constraints-and-indexes)
5. [Validation Rules](#validation-rules)
6. [User Lifecycle and Privacy](#user-lifecycle-and-privacy)
7. [Sample Queries](#sample-queries)
8. [Entity Relationship Diagram](#entity-relationship-diagram)

## Introduction
The User model in the takeNpass application serves as the central identity entity, representing individuals who interact with the platform. It encapsulates personal information, authentication state, role-based access control, and relationships to various domain entities such as cases, messages, and ratings. This document details the structure, constraints, relationships, and usage patterns of the User entity.

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L1-L230)

## Core Fields
The User model contains the following core attributes:

| Field | Type | Nullable | Description |
|-------|------|----------|-------------|
| id | string (UUID) | No | Unique identifier for the user |
| email | string | No | Primary login identifier; must be unique |
| name | string | Yes | Full name of the user |
| role | enum | No | Access level: GUEST, REG, MODER, ADMIN, SU |
| username | string | Yes | Display name for the user |
| image | string | Yes | URL to profile picture |
| about_me | string | Yes | User's self-description |
| phone | string | Yes | Contact phone number |
| language | string | No | Preferred language code (e.g., "en", "es") |
| createdAt | date | No | Timestamp of account creation |
| updatedAt | date | No | Timestamp of last update |
| emailVerified | boolean | No | Indicates if email has been verified |
| blocked | boolean | No | Account lock status |
| confirmed | boolean | No | Account activation status |

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L15-L35)

## Relationships
The User entity maintains multiple relationships with other domain models:

### Account (Authentication)
Each user can have multiple authentication accounts (e.g., social logins). This relationship is defined through the `accounts` field.

[SPEC SYMBOL](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L55-L56)

### Session (Active Logins)
The `auth_session` relationship tracks active user sessions, enabling stateful authentication and session management.

[SPEC SYMBOL](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L62-L63)

### Case (Author and Client)
Users can create cases (`authoredCases`) and participate in cases as clients (`cases`). The Case model references the user via `authorId` and `clients` relationships.

[SPEC SYMBOL](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L53-L54)  
[SPEC SYMBOL](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L10-L11)

### Message (Sent and Received)
Users can send (`sentMessages`) and receive (`receivedMessages`) messages within the application.

[SPEC SYMBOL](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L57-L58)

### Notification
Users receive notifications (`notifications`) and may generate them (`sendedNotifications`).

[SPEC SYMBOL](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L59-L60)

### Favorite
The `favorites` relationship allows users to bookmark cases of interest.

[SPEC SYMBOL](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L54)

### UserRating
Users can rate others (`sendedRatings`) and be rated (`ratings`).

[SPEC SYMBOL](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L65-L66)

### UserAvatar
An optional profile image relationship managed through the `avatar` field.

[SPEC SYMBOL](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L64)

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L53-L66)
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L45-L46)
- [AccountSchema.ts](file://server/prisma/generated/zod/modelSchema/AccountSchema.ts#L45-L46)
- [SessionSchema.ts](file://server/prisma/generated/zod/modelSchema/SessionSchema.ts#L45-L46)

## Data Constraints and Indexes
The User model enforces several data integrity constraints:

- **Primary Key**: `id` field (UUID) uniquely identifies each user
- **Unique Index**: `email` field has a unique constraint to prevent duplicate accounts
- **Foreign Keys**:
  - `accounts.userId` references `User.id`
  - `sessions.userId` references `User.id`
  - `cases.authorId` references `User.id`
  - `messages.senderId` and `messages.receiverId` reference `User.id`

These constraints are enforced at the database level through Prisma schema definitions.

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L15-L35)
- [AccountSchema.ts](file://server/prisma/generated/zod/modelSchema/AccountSchema.ts#L10-L12)
- [SessionSchema.ts](file://server/prisma/generated/zod/modelSchema/SessionSchema.ts#L10-L11)

## Validation Rules
Data validation is enforced through Zod schemas and Prisma constraints:

### Role Validation
The `role` field accepts only predefined values:
```typescript
RolesSchema = z.enum(['GUEST','REG','MODER','ADMIN','SU'])
```

[SPEC SYMBOL](file://server/prisma/generated/zod/inputTypeSchemas/RolesSchema.ts#L3)

### Field-Level Validation
- `email`: Must be a valid string (format validated at application level)
- `id`: Must be a valid UUID
- `createdAt`, `updatedAt`, `expiresAt`: Coerced to Date objects
- String fields with `.nullable()`: Can be null or omitted

Validation occurs both at API input level (via Zod) and database level (via Prisma).

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L15-L35)
- [RolesSchema.ts](file://server/prisma/generated/zod/inputTypeSchemas/RolesSchema.ts#L3)

## User Lifecycle and Privacy
The User model supports the following lifecycle states:

- **Creation**: Users are created with `confirmed: false` until email verification
- **Activation**: `confirmed` set to true after verification
- **Blocking**: `blocked: true` disables account access
- **Soft Delete Pattern**: Not explicitly implemented; deletion appears to be hard delete based on schema

### Privacy Considerations
- Personal data (email, phone) is stored encrypted at rest
- `about_me` and `name` are public profile fields
- `password` is stored in the Account model with hashing
- Data access follows role-based permissions (RBAC)

User data retention follows GDPR principles, with deletion procedures managed through service layer logic.

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L30-L34)
- [UserSeed.ts](file://server/prisma/seeds/UserSeed.ts#L5-L20)

## Sample Queries
### Get User Profile with Relations
```typescript
const userWithProfile = await prisma.user.findUnique({
  where: { id: userId },
  include: {
    avatar: true,
    authoredCases: true,
    favorites: true,
    sentMessages: { take: 10 },
    receivedMessages: { take: 10 },
    ratings: true,
    notificationSettings: true
  }
});
```

### Authentication Validation
```typescript
const userForAuth = await prisma.user.findFirst({
  where: {
    email: inputEmail,
    confirmed: true,
    blocked: false
  },
  include: {
    accounts: true,
    auth_session: true
  }
});
```

### User Search by Email
```typescript
const user = await prisma.user.findUnique({
  where: { email: '<EMAIL>' },
  select: {
    id: true,
    name: true,
    image: true,
    role: true,
    about_me: true
  }
});
```

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L15-L35)

## Entity Relationship Diagram

```mermaid
erDiagram
USER {
uuid id PK
string email UK
string name
string username
string image
string about_me
string phone
enum role
boolean blocked
boolean confirmed
boolean emailVerified
string language
timestamp createdAt
timestamp updatedAt
}
ACCOUNT {
uuid id PK
string accountId
string providerId
string providerAccountId
string password
string accessToken
string refreshToken
timestamp expiresAt
string tokenType
string scope
string idToken
string sessionState
timestamp createdAt
timestamp updatedAt
}
SESSION {
string id PK
timestamp expiresAt
string token
string ipAddress
string userAgent
timestamp createdAt
timestamp updatedAt
}
CASE {
uuid id PK
enum status
timestamp createdAt
string description
string authorId FK
string baggage
number price
boolean isRequest
timestamp expire_at
}
MESSAGE {
uuid id PK
string senderId FK
string receiverId FK
string content
boolean isRead
timestamp createdAt
timestamp updatedAt
}
NOTIFICATION {
uuid id PK
string recipientId FK
string senderId FK
string content
boolean isRead
timestamp createdAt
}
FAVORITE {
uuid id PK
string userId FK
string caseId FK
timestamp createdAt
}
USERRATING {
uuid id PK
string raterId FK
string ratedId FK
integer rating
string comment
timestamp createdAt
}
USERAVATAR {
uuid id PK
string userId FK
string imageUrl
timestamp createdAt
}
USER ||--o{ ACCOUNT : "has"
USER ||--o{ SESSION : "has"
USER ||--o{ CASE : "authors"
USER }o--o{ CASE : "participates as client"
USER ||--o{ MESSAGE : "sends"
USER ||--o{ MESSAGE : "receives"
USER ||--o{ NOTIFICATION : "receives"
USER ||--o{ NOTIFICATION : "sends"
USER ||--o{ FAVORITE : "marks"
USER ||--o{ USERRATING : "gives"
USER ||--o{ USERRATING : "receives"
USER ||--o{ USERAVATAR : "has"
```

**Diagram sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L15-L35)
- [AccountSchema.ts](file://server/prisma/generated/zod/modelSchema/AccountSchema.ts#L10-L25)
- [SessionSchema.ts](file://server/prisma/generated/zod/modelSchema/SessionSchema.ts#L10-L18)
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L10-L20)