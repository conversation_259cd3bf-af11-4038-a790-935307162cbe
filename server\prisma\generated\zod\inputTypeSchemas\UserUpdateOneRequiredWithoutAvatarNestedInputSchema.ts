import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserCreateWithoutAvatarInputSchema } from './UserCreateWithoutAvatarInputSchema';
import { UserUncheckedCreateWithoutAvatarInputSchema } from './UserUncheckedCreateWithoutAvatarInputSchema';
import { UserCreateOrConnectWithoutAvatarInputSchema } from './UserCreateOrConnectWithoutAvatarInputSchema';
import { UserUpsertWithoutAvatarInputSchema } from './UserUpsertWithoutAvatarInputSchema';
import { UserWhereUniqueInputSchema } from './UserWhereUniqueInputSchema';
import { UserUpdateToOneWithWhereWithoutAvatarInputSchema } from './UserUpdateToOneWithWhereWithoutAvatarInputSchema';
import { UserUpdateWithoutAvatarInputSchema } from './UserUpdateWithoutAvatarInputSchema';
import { UserUncheckedUpdateWithoutAvatarInputSchema } from './UserUncheckedUpdateWithoutAvatarInputSchema';

export const UserUpdateOneRequiredWithoutAvatarNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutAvatarNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutAvatarInputSchema),z.lazy(() => UserUncheckedCreateWithoutAvatarInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutAvatarInputSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutAvatarInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => UserUpdateToOneWithWhereWithoutAvatarInputSchema),z.lazy(() => UserUpdateWithoutAvatarInputSchema),z.lazy(() => UserUncheckedUpdateWithoutAvatarInputSchema) ]).optional(),
}).strict();

export default UserUpdateOneRequiredWithoutAvatarNestedInputSchema;
