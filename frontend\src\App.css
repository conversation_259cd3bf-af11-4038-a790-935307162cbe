@reference './index.css';
/* @import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:opsz,wght@6..12,300;6..12,400;6..12,500;6..12,600&family=Poppins:wght@300;400;600&display=swap'); */
body {
  /* font-family: 'Nunito Sans', sans-serif; */
  /* font-weight: 600; */
  /* font-family: 'Poppins', sans-serif; */
}



.PhoneInputCountryIcon {
  @apply w-6 h-6;
}
.PhoneInput {
  @apply flex items-center space-x-1;
}

.PhoneInputCountry {
  @apply flex flex-row-reverse space-x-1 items-center;
}

.leaflet-custom-container {
  @apply dark:brightness-75 dark:contrast-200 dark:hue-rotate-180 dark:invert dark:saturate-50;
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.slide-out-right {
  animation: slideOutRight 0.4s ease-in-out;
}
