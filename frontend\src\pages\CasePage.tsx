import { getShortPointName } from '@/lib/utils/getShortPointName'
import { Avatar, Button, Card, CardBody, CardHeader, Chip, Input, Spinner, Textarea, User, Tooltip, Divider } from "@heroui/react"
import { Link, useMatch, useRouter } from '@tanstack/react-router'
import { CasePartialWithRelations } from '../../../server/prisma/generated/zod/modelSchema'
import { Timeline } from '@/components/Timeline'
import { PointType, Way } from '@/types'
import { trpc } from '@/trpc'
import { useUserStore } from '@/store'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { UserRating } from '@/components/UserRating'
import { PhoneInput } from '@/components/PhoneInput'

import { parsePhoneNumber } from 'react-phone-number-input/input'
import { MainMap } from '@/components/map/MainMap'
import L, { Map as LMap } from 'leaflet'
import { <PERSON><PERSON>, Popup, useMap } from 'react-leaflet'
import { Polyline } from 'react-leaflet/Polyline'

import _fromIcon from '@/assets/from.png'
import { useTranslation } from 'react-i18next'
import { RatingForm } from '@/components/RatingForm'
import { pointColorByType } from '@/lib/utils/pointColorByType'
import IconMessage from '@/lib/svg/MessageIcon'
import { statusColorByName } from '@/lib/utils/statusColor'
import IconPencil from '@/lib/svg/IconPencil'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/ru'

dayjs.extend(relativeTime)
dayjs.locale('ru')

export const CasePage = () => {
  const { t } = useTranslation()

  const { params } = useMatch({ from: '/route/$caseId' })
  const { caseId } = params
  const { data: userData } = useUserStore()

  const { isFetching, data, isError, error, refetch } = trpc.case.id.useQuery(caseId)

  const { data: sendedRatings } = trpc.users.sendedRatings.useQuery({
    wherePayload: {
      caseId: data?.id,
      senderId: userData?.id
    }
  })

  const [isAssigned, setIsAssigned] = useState(false)
  const [isAuthor, setIsAuthor] = useState<boolean | undefined>()

  const [mapInstance, setMapInstance] = useState<LMap>()
  const [pointsArr, setPointsArr] = useState<any[]>([])

  const [showRatingForm, setShowRatingForm] = useState(false)
  // const caseMap = useMap()

  const {
    isPending: assign_isLoading,
    isSuccess: assign_isSuccess,
    isError: assign_isError,
    error: assign_error,
    data: assign_data,
    mutateAsync: assignClient
  } = trpc.case.assignClient.useMutation()

  useEffect(() => {
    setIsAssigned(!!data?.clients.find((x) => x.id === userData?.id))
    const validPoints = [data?.from, ...(data?.middlepoints ?? []), data?.to]
      .filter((point) => point && point.lat != null && point.lon != null)
      .map((point) => [Number(point.lat), Number(point.lon)])
    setPointsArr(validPoints)
    setIsAuthor(data?.authorId === userData?.id)
  }, [data])

  useEffect(() => {
    if (data?.status != 'OPEN' && isAssigned && !isAuthor) {
      setShowRatingForm(true)
    }
  }, [isAssigned, data])

  useEffect(() => {
    assign_data && setIsAssigned(assign_data?.isAssigned)
  }, [assign_data])

  const dotColorType = {
    from: 'primary',
    middlepoints: 'warning',
    to: 'success'
  }

  // Функция для форматирования цены
  const formatPrice = (price: number | null | undefined) => {
    if (!price || price === 0) return t('Free')
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0
    }).format(price)
  }

  // Функция для форматирования даты с относительным временем
  const formatDateWithRelative = (date: Date | null | undefined) => {
    if (!date) return null
    const now = dayjs()
    const targetDate = dayjs(date)
    const diffDays = targetDate.diff(now, 'day')

    if (diffDays === 0) return t('Today')
    if (diffDays === 1) return t('Tomorrow')
    if (diffDays === -1) return t('Yesterday')
    if (diffDays > 0 && diffDays <= 7) return t('In {{count}} days', { count: diffDays })
    if (diffDays < 0 && diffDays >= -7) return t('{{count}} days ago', { count: Math.abs(diffDays) })

    return targetDate.format('DD.MM.YYYY')
  }

  // Функция для получения типа заявки
  const getCaseTypeLabel = (isRequest: boolean) => {
    return isRequest ? t('Request for delivery') : t('Delivery offer')
  }

  // Функция для парсинга информации о багаже
  const parseBaggageInfo = (baggage: string | null | undefined) => {
    if (!baggage) return null
    try {
      return JSON.parse(baggage)
    } catch {
      return baggage
    }
  }

  // Функция для вычисления продолжительности поездки
  const getTripDuration = () => {
    if (!data?.from?.date || !data?.to?.date) return null

    const fromDate = dayjs(data.from.date)
    const toDate = dayjs(data.to.date)
    const duration = toDate.diff(fromDate)

    if (duration <= 0) return null

    const hours = Math.floor(duration / (1000 * 60 * 60))
    const days = Math.floor(hours / 24)
    const remainingHours = hours % 24

    if (days > 0) {
      return remainingHours > 0
        ? t('{{days}} days {{hours}} hours', { days, hours: remainingHours })
        : t('{{days}} days', { days })
    } else if (hours > 0) {
      return t('{{hours}} hours', { hours })
    } else {
      const minutes = Math.floor(duration / (1000 * 60))
      return t('{{minutes}} minutes', { minutes })
    }
  }

  // useEffect(() => {
  //   if (assign_isError) {
  //     toast.error(t('Server error'))
  //   }
  //   if (assign_isSuccess) {
  //     assign_data.isAssigned ? toast(t('Request sended')) : toast(t('Request cancelled'))
  //     refetch()
  //   }
  // }, [assign_isError, assign_isSuccess])

  async function assignClientHandler() {
    await assignClient(String(data?.id))
  }

  function clickPointHandler(point: Way): void {
    mapInstance?.flyTo([point.lat, point.lon], 15, {
      duration: 3
    })
  }

  // Компонент для отображения участников поездки
  const ParticipantsPanel = () => {
    if (!data?.clients || data.clients.length === 0) return null

    return (
      <div className='bg-default-100 p-2 rounded'>
          <h3 className="text-sm font-medium text-default-600">{t('Participants')} ({data.clients.length})</h3>
        <div>
          <div className="divide-y divide-default-200">
            {data.clients.map((client) => (
              <div key={client.id} className="flex items-center justify-between py-2 gap-2">
                <User
                  name={client.username || client.email}
                  description={
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                      <UserRating user={client as any} width={80} />
                      {isAuthor && client.phone && (
                        <Chip size="sm" variant="flat" color="primary">
                          {t('Contact available')}
                        </Chip>
                      )}
                    </div>
                  }
                  avatarProps={{
                    src: client.avatar?.base64string,
                    size: 'sm'
                  }}
                />
                {isAuthor && (
                  <div className="flex gap-2 self-end sm:self-auto">
                    {client.phone && (
                      <>
                        <Tooltip content={t('Call')}>
                          <Button
                            size="sm"
                            variant="flat"
                            color="primary"
                            as="a"
                            href={`tel:${client.phone}`}
                            isIconOnly
                            aria-label={t('Call') as string}
                          >
                            📞
                          </Button>
                        </Tooltip>
                      </>
                    )}
                    <Button
                      size="sm"
                      variant="flat"
                      isIconOnly
                      aria-label={t('Message') as string}
                    >
                      <Link to='/messages' search={{ partnerId: client.id }}>
                        <IconMessage className='w-4 h-4' />
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Компонент для отображения дополнительной информации о поездке
  const CaseInfoPanel = () => {
    if (!data) return null

    const baggageInfo = parseBaggageInfo(data.baggage)
    const participantsCount = data.clients?.length || 0

    return (
      <div className='bg-default-100 p-3 rounded-lg'>
        <div>
          <div className="flex justify-between items-center w-full">
            <h3 className="text-sm font-medium text-default-600">{t('Trip Information')}</h3>
            <div className="flex gap-2">
              <Tooltip content={data.isRequest ? t('Someone is looking for delivery') : t('Someone offers delivery service')}>
                <Chip
                  color={data.isRequest ? 'warning' : 'primary'}
                  variant="flat"
                  size="sm"
                >
                  {getCaseTypeLabel(data.isRequest)}
                </Chip>
              </Tooltip>
              {data.price && data.price > 0 && (
                <Tooltip content={t('Price for this trip')}>
                  <Chip color="success" variant="flat" size="sm">
                    {formatPrice(data.price)}
                  </Chip>
                </Tooltip>
              )}
            </div>
          </div>
        </div>
        <div className="pt-0">
          <div className="flex flex-wrap items-center gap-2 text-xs">
            <Tooltip content={data.isRequest ? t('Someone is looking for delivery') : t('Someone offers delivery service')}>
              <Chip color={data.isRequest ? 'warning' : 'primary'} variant="flat" size="sm">
                {getCaseTypeLabel(data.isRequest)}
              </Chip>
            </Tooltip>
            {data.price && data.price > 0 && (
              <Tooltip content={t('Price for this trip')}>
                <Chip color="success" variant="flat" size="sm">
                  {formatPrice(data.price)}
                </Chip>
              </Tooltip>
            )}
            <Tooltip content={t('Current trip status')}>
              <Chip size="sm" variant="flat" color={statusColorByName(data.status) as any}>
                {t(data.status)}
              </Chip>
            </Tooltip>
            <div className="text-default-500">
              {t('Created')}: <span className="font-semibold">{formatDateWithRelative(data.createdAt)}</span>
            </div>
            {data.expire_at && (
              <div className="text-default-500">
                {t('Expires')}: <span className="font-semibold">{formatDateWithRelative(data.expire_at)}</span>
              </div>
            )}
            {getTripDuration() && (
              <Chip size="sm" color="primary" variant="flat">
                {getTripDuration()}
              </Chip>
            )}
            {isAuthor && (
              <Chip size="sm" variant="flat">
                {t('Participants')}: {participantsCount}
              </Chip>
            )}
          </div>

          {baggageInfo && (
            <details className="mt-2">
              <summary className="cursor-pointer text-xs text-default-500">{t('Baggage Information')}</summary>
              <div className="mt-1 text-xs whitespace-pre-wrap break-words">
                {typeof baggageInfo === 'string' ? baggageInfo : JSON.stringify(baggageInfo, null, 2)}
              </div>
            </details>
          )}
        </div>
      </div>
    )
  }

  const PointBody = ({ point, type }: { point: Way; type: PointType }) => {
    return (
      <Card className='w-full' radius='sm' shadow='none'>
        <CardHeader className='flex flex-col sm:flex-row sm:justify-between gap-3'>
          <Tooltip content={t('Click to focus on map')}>
            <Chip classNames={{ content: 'text-left' }} color={dotColorType[type] as any} variant='dot'>
              <span className='text-base sm:text-lg text-left text-ellipsis'>{getShortPointName(point, type)}</span>{' '}
            </Chip>
          </Tooltip>
          <div className="flex flex-row sm:flex-col items-start sm:items-end gap-2 sm:gap-1">
            {point.date && (
              <>
                <Chip variant='solid' className='font-bold' size="sm">
                  <span className='font-semibold text-xs sm:text-sm'>{dayjs(point.date).format('DD.MM.YYYY')}</span>
                </Chip>
                <Chip variant='flat' color="default" size="sm">
                  <span className='text-xs'>{dayjs(point.date).format('HH:mm')}</span>
                </Chip>
                <div className="text-xs text-default-500 hidden sm:block">
                  {formatDateWithRelative(point.date)}
                </div>
              </>
            )}
          </div>
        </CardHeader>

        <CardBody>
          <div className='cursor-pointer space-y-4'>
            <div onClick={() => clickPointHandler(point)} className="cursor-pointer">
              {/* <div className='text-sm'>{point.geometa?.display_name}</div> */}
              <Textarea
                label={t('Full address')}
                size='lg'
                radius='sm'
                labelPlacement='outside'
                readOnly
                className='w-full shadow-none'
                classNames={{
                  input: 'w-full shadow-none',
                  inputWrapper: 'shadow-none'
                }}
                value={(point.geometa as any)?.display_name}
              />
              {/* <div className='text-default-500'>{[point.lat, point.lon].join(', ')}</div> */}
            </div>
            {point.comment && (
              <div>
                <Textarea
                  label={t('Note')}
                  size='md'
                  labelPlacement='outside'
                  readOnly
                  className='w-full'
                  classNames={{
                    input: 'w-full',
                    inputWrapper: 'shadow-none'
                  }}
                  value={point.comment}
                />
              </div>
            )}
          </div>
        </CardBody>
        {/* <CardFooter className=' justify-between'>
          <div className='text-sm'>{point.geometa?.display_name}</div>
        </CardFooter> */}
      </Card>
    )
  }

  const MapInstance = () => {
    const map = useMap()

    useEffect(() => {
      if (pointsArr.length > 0) {
        try {
          const bounds = new L.LatLngBounds(pointsArr)
          map.fitBounds(bounds)
        } catch (error) {
          console.warn('Error fitting bounds:', error)
        }
      }
    }, [pointsArr, map])

    useEffect(() => {
      setMapInstance(map)
    }, [map])

    return <></>
  }

  return (
    <div>
      <div>
        {data?.id && (
          <>
            <div className='grid lg:grid-cols-2 lg:gap-7'>
              <div className='mb-5'>
              <div className={`bg-default-100 dark:bg-default-50 p-4 rounded-lg mb-5`}>
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-4">
                  <div className="flex-1">
                    <User
                      name={data.author.username || t('Anonymous')}
                      description={
                        <div className="space-y-2">
                          <UserRating user={data.author} />
                          <div className="text-xs text-default-500">
                            {t('Member since')} {dayjs(data.author.createdAt).format('MMMM YYYY')}
                          </div>
                          {data.author.about_me && (
                            <div className="text-sm text-default-600 mt-2">
                              {data.author.about_me}
                            </div>
                          )}
                        </div>
                      }
                      avatarProps={{
                        src: data.author.avatar?.base64string,
                        size: 'lg'
                      }}
                    />
                  </div>

                  <div className="flex gap-2 self-end sm:self-auto">
                    {data.status == 'OPEN' && (
                      <Button
                        onPress={assignClientHandler}
                        isLoading={assign_isLoading}
                        color='primary'
                        radius='sm'
                        variant='flat'
                        size="sm"
                        className="w-full sm:w-auto"
                      >
                        {isAssigned ? t('Cancel request') : t('Send request')}
                      </Button>
                    )}

                    {isAssigned && !isAuthor && (
                      <Button isIconOnly variant="flat" size="sm">
                        <Link to='/messages' search={{ partnerId: data?.authorId }}>
                          <IconMessage className='w-5 h-5' />
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>

                {isAssigned && data.status == 'OPEN' && (
                  <div className="bg-default-200 dark:bg-default-100 p-3 rounded-lg">
                    <div className="text-sm font-medium mb-2">{t('Contact Information')}</div>
                    <div className="space-y-1">
                      {data?.author.phone && (
                        <div className='text-sm flex items-center gap-2'>
                          <span className="text-default-500">{t('Phone')}:</span>
                          <a href={'tel:' + data.author.phone} className="text-primary hover:underline">
                            {parsePhoneNumber(data.author.phone)?.format('NATIONAL')}
                          </a>
                        </div>
                      )}
                      <div className='text-sm flex items-center gap-2'>
                        <span className="text-default-500">{t('Email')}:</span>
                        <a href={'mailto:' + data?.author.email} className="text-primary hover:underline">
                          {data.author.email}
                        </a>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {showRatingForm && (
                <div className='mt-5 mb-10'>
                  <RatingForm initValue={sendedRatings?.[0] as any} c={data as any} targetUser={data.author as any} />
                </div>
              )}
              <div className='relative pl-3'>
                <div className='flex justify-between items-center mb-2'>
                  <div className='text-default-500 text-sm'>#{data.id.slice(0, 8)}</div>

                  <div className="flex items-center gap-2">
                    <Tooltip content={t('Current trip status')}>
                      <Chip variant='flat' className='font-bold' color={statusColorByName(data.status) as any}>
                        {data.status}
                      </Chip>
                    </Tooltip>

                    {isAuthor && (
                      <Tooltip content={t('Edit trip')}>
                        <Button variant='flat' isIconOnly size="sm">
                          <Link className='flex items-center' to='/edit' search={{ caseId: data?.id }}>
                            <IconPencil className='w-4 h-4' />
                          </Link>
                        </Button>
                      </Tooltip>
                    )}

                    <Tooltip content={t('Share trip')}>
                      <Button
                        variant='flat'
                        isIconOnly
                        size="sm"
                        onPress={() => {
                          if (navigator.share) {
                            navigator.share({
                              title: t('Trip from {{from}} to {{to}}', {
                                from: getShortPointName(data.from as any, 'from'),
                                to: getShortPointName(data.to as any, 'to')
                              }),
                              url: window.location.href
                            })
                          } else {
                            navigator.clipboard.writeText(window.location.href)
                            toast.success(t('Link copied to clipboard'))
                          }
                        }}
                      >
                        📤
                      </Button>
                    </Tooltip>
                  </div>
                </div>

                
                <div className='space-y-5'>
                  {data.from && (
                    <div>
                      <h2 className='font-semibold text-primary-500 mb-2'>{t('From')}</h2>
                      <PointBody point={data.from as any} type='from' />
                    </div>
                  )}
                  {data.middlepoints.map((point) => (
                    <div key={point.id}>
                      <h2 className='font-semibold text-warning-500 mb-2'>{t('Via')}</h2>
                      <PointBody point={point as any} type='middlepoints' />
                    </div>
                  ))}
                  {data.to && (
                    <div>
                      <h2 className='font-semibold text-secondary-500 mb-2'>{t('To')}</h2>
                      <PointBody point={data.to as any} type='to' />
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className='flex flex-col gap-5'>
                <CaseInfoPanel />
                {isAuthor && data.clients && data.clients.length > 0 && <ParticipantsPanel />}

              <div className=''>
                {data.description && (
                  <Textarea
                    label={t('Route description')}
                    size='md'
                    labelPlacement='outside'
                    readOnly
                    className='w-full'
                    classNames={{
                      input: 'w-full'
                    }}
                    value={data.description}
                  />
                )}
              </div>
              <Card
                classNames={{
                  body: 'p-0'
                }}
              >
                <CardBody>
                  <MainMap mapStyles={{ height: '430px' }} readOnly initBrowserGeocode={false}>
                    <MapInstance />
                    <Polyline positions={pointsArr} />
                    {data?.from?.lat != null && data?.from?.lon != null && (
                      <Marker
                        icon={L.divIcon({ className: `w-4 h-4 ${pointColorByType('from')} rounded-full` })}
                        position={[Number(data.from.lat), Number(data.from.lon)]}
                      >
                        <Popup>
                          <div>{(data.from?.geometa as any)?.display_name}</div>
                        </Popup>
                      </Marker>
                    )}
                    {data?.middlepoints?.filter(point => point.lat != null && point.lon != null).map((point) => (
                      <Marker
                        key={point.id}
                        icon={L.divIcon({ className: `w-4 h-4 ${pointColorByType('middlepoints')} rounded-full` })}
                        position={[Number(point.lat), Number(point.lon)]}
                      >
                        <Popup>
                          <div>{(point.geometa as any)?.display_name}</div>
                        </Popup>
                      </Marker>
                    ))}
                    {data?.to?.lat != null && data?.to?.lon != null && (
                      <Marker
                        icon={L.divIcon({ className: `w-4 h-4 ${pointColorByType('to')} rounded-full` })}
                        position={[Number(data.to.lat), Number(data.to.lon)]}
                      >
                        <Popup>{(data.to?.geometa as any)?.display_name}</Popup>
                      </Marker>
                    )}
                  </MainMap>
                </CardBody>
              </Card>
            </div>
          </div>
          </>
        )}
      </div>
      {isFetching && (
        <div className='h-screen flex flex-col items-center'>
          <Spinner />
        </div>
      )}
    </div>
  )
}
