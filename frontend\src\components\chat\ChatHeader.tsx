import { memo } from 'react'
import { Use<PERSON>, <PERSON><PERSON> } from '@heroui/react'
import { Link } from '@tanstack/react-router'
import { Chat<PERSON><PERSON><PERSON> } from '@/types/chat'

interface ChatHeaderProps {
  partner?: ChatPartner
  onBack?: () => void
  showBackButton?: boolean
}

const ChatHeader = memo(({ partner, onBack, showBackButton = false }: ChatHeaderProps) => {
  if (!partner) {
    return null
  }

  return (
    <div className="flex items-center justify-between border-b pb-3 border-default-300">
      <div className="flex items-center gap-3">
        {showBackButton && onBack && (
          <Button
            isIconOnly
            variant="light"
            size="sm"
            onPress={onBack}
            aria-label="Go back"
          >
            ←
          </Button>
        )}
        
        <Link to="/profile" search={{ userId: partner.id }}>
          <User
            className="text-ellipsis cursor-pointer hover:opacity-80 transition-opacity"
            classNames={{
              name: 'text-default-900 text-ellipsis text-base font-medium'
            }}
            name={partner.username}
            description={partner.email}
            avatarProps={{
              size: 'md',
              color: 'default',
              src: partner.avatar?.base64string,
              name: partner.username
            }}
          />
        </Link>
      </div>
      
      {/* Online status indicator - можно добавить позже */}
      {/* <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-success rounded-full"></div>
        <span className="text-xs text-default-500">Online</span>
      </div> */}
    </div>
  )
})

ChatHeader.displayName = 'ChatHeader'

export { ChatHeader }
