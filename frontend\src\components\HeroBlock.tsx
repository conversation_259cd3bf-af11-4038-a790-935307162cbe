import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, Image } from "@heroui/react"

export const HeroBlock = () => {
  return (
    <div className='flex justify-center flex-wrap lg:flex-nowrap items-center'>
      <div className='mb-7 text-xl md:text-2xl text-center font-semibold'>
        <span className='bg-linear-to-r font-bold from-primary-500 via-default-800 to-secondary-500 inline-block text-transparent bg-clip-text'>Travel and deliver</span>
        <span className='text-default-700'> packages: Fast global delivery through travelers.</span>
      </div>
      {/* <Image removeWrapper alt='Relaxing app background' className='hh-[400px] w-2/5 object-cover' src='/heroblock_1.png' /> */}
    </div>
  )
}
