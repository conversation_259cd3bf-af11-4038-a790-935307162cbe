# Real-time Communication

<cite>
**Referenced Files in This Document**   
- [ws.ts](file://server/src/providers/ws.ts)
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts)
- [socket.ts](file://frontend/src/socket.ts)
- [notification.ts](file://server/src/router/routers/notification.ts)
- [message.ts](file://server/src/services/message.ts)
- [notification.ts](file://server/src/services/notification.ts)
- [useChat.ts](file://frontend/src/hooks/useChat.ts)
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx)
- [ChatPartners.tsx](file://frontend/src/components/chat/ChatPartners.tsx)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx)
- [WebSocketStatus.tsx](file://frontend/src/components/WebSocketStatus.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [WebSocket Provider Implementation](#websocket-provider-implementation)
3. [Socket Notification Types and Payload Structure](#socket-notification-types-and-payload-structure)
4. [Message Broadcasting and Room-Based Communication](#message-broadcasting-and-room-based-communication)
5. [Event Emission and Client Disconnection Handling](#event-emission-and-client-disconnection-handling)
6. [Connection Scaling and Message Queuing](#connection-scaling-and-message-queuing)
7. [Fallback Strategies](#fallback-strategies)
8. [WebSocket Events and tRPC Notification Integration](#websocket-events-and-trpc-notification-integration)
9. [Frontend Integration and UI Components](#frontend-integration-and-ui-components)
10. [Conclusion](#conclusion)

## Introduction
This document provides comprehensive documentation for the WebSocket-based real-time communication system in the takeNpass application. The system enables instant messaging, notifications, and user presence tracking through a Socket.IO-powered backend and React frontend integration. It covers the architecture of the WebSocket provider, event types, message flow, and integration with tRPC for real-time data synchronization.

## WebSocket Provider Implementation

The WebSocket provider is implemented in `ws.ts` as a static class `IO` that manages Socket.IO connections and user sessions. It initializes the WebSocket server instance and handles authentication via session cookies, mapping connected users to their respective sockets.

Upon connection, the server reads the session cookie from the request headers and validates the session using the authentication provider. If valid, the user's ID is associated with their socket in a global `Map<string, Socket>`, enabling targeted message delivery. The provider exposes methods to retrieve a user's socket and send messages directly to them.

```mermaid
classDiagram
class IO {
+static wss : any
+static clients : Map<string, Socket>
+static init(serverInst : any)
+static getUserSocket(userId : string) : Socket
+static sendMessageToUser(userId : string, data : any) : void
}
class Socket {
+request : Request
+on(event : string, callback : Function)
+emit(event : string, data : any)
}
IO --> Socket : "manages"
IO --> Map : "uses"
```

**Diagram sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L51)

**Section sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L51)

## Socket Notification Types and Payload Structure

The `SocketNotificationType.ts` file defines a TypeScript type union that specifies the various notification events the system can emit. Each event type includes a structured payload with relevant user, case, and contextual data.

Key event types include:
- `NEW_REQUEST`: Triggered when a new request is sent, containing sender, recipient, and case details
- `CANCEL_REQUEST`: Emitted when a request is canceled
- `NEW_CASES_ON_SUBSCRIBE`: Notifies users of new cases matching their subscription
- `NEW_REVIEW`: Indicates a new review has been posted
- `NEW_MESSAGE`: Fired when a new message is received, including sender and content

The `SocketNotification` type standardizes the notification envelope, containing metadata such as ID, creation timestamp, read status, and a localized message string.

```mermaid
flowchart TD
A["SocketNotification"] --> B["data: Notification Metadata"]
A --> C["message: Localized String"]
A --> D["type: Event Type"]
B --> B1["id: number"]
B --> B2["createdAt: Date"]
B --> B3["userId: string"]
B --> B4["read: boolean | null"]
B --> B5["caseId: string | null"]
B --> B6["senderId: string | null"]
B --> B7["translationKey: string"]
B --> B8["user: Partial<User>"]
B --> B9["sender: Partial<User>"]
B --> B10["case: Partial<Case>"]
D --> D1["NEW_REQUEST"]
D --> D2["CANCEL_REQUEST"]
D --> D3["NEW_CASES_ON_SUBSCRIBE"]
D --> D4["NEW_REVIEW"]
D --> D5["NEW_MESSAGE"]
```

**Diagram sources**
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts#L1-L49)

**Section sources**
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts#L1-L49)

## Message Broadcasting and Room-Based Communication

The system implements direct user-to-user messaging rather than room-based broadcasting. Messages are sent point-to-point using the recipient's user ID as a key to locate their active socket connection. When a message is sent via the message service, it triggers a notification that is delivered exclusively to the recipient's connected socket.

The chat functionality maintains conversation state on the frontend using React hooks and tRPC queries. The `useChat` hook manages message state, input handling, and scroll behavior, while fetching messages through tRPC queries. Message delivery is confirmed through database persistence and read receipt updates.

```mermaid
sequenceDiagram
participant ClientA as "User A (Client)"
participant Frontend as "Frontend App"
participant TRPC as "tRPC API"
participant MessageService as "MessageService"
participant NotificationService as "NotificationService"
participant IO as "IO Provider"
participant ClientB as "User B (Client)"
ClientA->>Frontend : Send Message
Frontend->>TRPC : mutation.send(input)
TRPC->>MessageService : send({receiverId, senderId, content})
MessageService->>MessageService : Create message in DB
MessageService->>NotificationService : notificationAfterSend()
NotificationService->>NotificationService : Create notification record
NotificationService->>IO : getUserSocket(receiverId)
IO-->>NotificationService : Return socket
NotificationService->>ClientB : socket.emit('xnotification', data)
MessageService-->>TRPC : Return message ID
TRPC-->>Frontend : Mutation success
Frontend->>Frontend : Update UI optimistically
```

**Diagram sources**
- [message.ts](file://server/src/services/message.ts#L1-L264)
- [notification.ts](file://server/src/services/notification.ts#L1-L198)
- [ws.ts](file://server/src/providers/ws.ts#L1-L51)

**Section sources**
- [message.ts](file://server/src/services/message.ts#L1-L264)
- [notification.ts](file://server/src/services/notification.ts#L1-L198)

## Event Emission and Client Disconnection Handling

Events are emitted from the server using the `socket.emit()` method with the event name `'xnotification'` and a structured payload. The emission occurs after successful database operations in services like `NotificationService.createNotification()`.

Client disconnections are handled through Socket.IO's built-in `disconnect` event listener. When a user disconnects, their socket is automatically removed from the `clients` map in the `IO` class, ensuring that future messages are not routed to inactive connections. This cleanup prevents memory leaks and ensures accurate presence tracking.

```mermaid
flowchart LR
A["Client Disconnect"] --> B["Socket 'disconnect' event"]
B --> C["Remove user from IO.clients Map"]
C --> D["Cleanup complete"]
E["Service Operation Complete"] --> F["NotificationService.createNotification()"]
F --> G["IO.getUserSocket(userId)"]
G --> H{"Socket exists?"}
H --> |Yes| I["socket.emit('xnotification', data)"]
H --> |No| J["Notification stored in DB only"]
```

**Diagram sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L51)
- [notification.ts](file://server/src/services/notification.ts#L1-L198)

**Section sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L51)
- [notification.ts](file://server/src/services/notification.ts#L1-L198)

## Connection Scaling and Message Queuing

The current implementation uses an in-memory `Map` to store active socket connections, which limits horizontal scaling to a single server instance. For multi-instance deployments, a shared message broker (e.g., Redis) would be required to enable cross-server message routing.

Message queuing is not explicitly implemented; messages are processed synchronously. However, the system could be enhanced with a message queue (e.g., RabbitMQ, Kafka) to handle high-volume notification traffic, ensure delivery during server maintenance, and support offline message delivery.

The architecture currently assumes that all real-time events are critical and must be delivered immediately, without prioritization or batching mechanisms.

## Fallback Strategies

The system includes several fallback mechanisms:
- If a user is not connected, notifications are stored in the database and retrieved when the user next polls their notification list via tRPC
- Frontend components use tRPC queries as the source of truth, ensuring data consistency even if WebSocket messages are missed
- The `useChat` hook implements optimistic UI updates, providing responsiveness while falling back to server state on error
- Connection status is visually indicated through the `WebSocketStatus` component, showing connectivity state to users

No automatic reconnection logic is explicitly defined in the frontend, relying on Socket.IO's built-in reconnection capabilities.

**Section sources**
- [socket.ts](file://frontend/src/socket.ts#L1-L11)
- [WebSocketStatus.tsx](file://frontend/src/components/WebSocketStatus.tsx#L1-L10)

## WebSocket Events and tRPC Notification Integration

WebSocket events are tightly integrated with tRPC procedures. While tRPC handles the request-response cycle for CRUD operations, WebSockets provide real-time push notifications. The `notificationRouter` in tRPC exposes endpoints for listing and managing notifications, while the WebSocket system pushes new notifications as they are created.

When a service like `MessageService` creates a notification, it calls `NotificationService.createNotification()`, which both persists the notification and attempts immediate delivery via WebSocket. If delivery fails (user offline), the notification remains available through tRPC queries.

This hybrid approach ensures reliability: real-time delivery when possible, and fallback to polling when necessary.

```mermaid
graph TB
A["tRPC Request"] --> B["Service Operation"]
B --> C["Create Notification in DB"]
C --> D["Emit via WebSocket if online"]
D --> E["Client receives real-time update"]
C --> F["Available via tRPC query"]
F --> G["Client polls for updates"]
style E fill:#D5E8D4,stroke:#82B366
style G fill:#FFF2CC,stroke:#D6B656
```

**Diagram sources**
- [notification.ts](file://server/src/router/routers/notification.ts#L1-L25)
- [notification.ts](file://server/src/services/notification.ts#L1-L198)

**Section sources**
- [notification.ts](file://server/src/router/routers/notification.ts#L1-L25)
- [notification.ts](file://server/src/services/notification.ts#L1-L198)

## Frontend Integration and UI Components

The frontend integrates WebSocket functionality through a centralized `socket.ts` file that exports a singleton Socket.IO client instance. This client connects to the server using the root path, allowing the connection to be established relative to the current domain.

Key UI components include:
- `ChatPage`: Orchestrates chat interface layout and state
- `MainChat`: Displays message thread and input field
- `ChatPartners`: Shows conversation partners with unread counts
- `Notifications`: Renders real-time notifications from WebSocket events
- `WebSocketStatus`: Visual indicator of connection health

The `useChat` hook abstracts chat state management, integrating tRPC queries for message retrieval and mutations for sending messages, while relying on WebSocket events for real-time updates.

```mermaid
graph TB
subgraph "Frontend Components"
A[socket.ts] --> B[useChat.ts]
B --> C[ChatPage.tsx]
C --> D[MainChat.tsx]
C --> E[ChatPartners.tsx]
A --> F[Notifications.tsx]
A --> G[WebSocketStatus.tsx]
end
subgraph "Backend Events"
H[IO Provider] --> A
I[NotificationService] --> H
end
style A fill:#E1D5E7,stroke:#9673A6
style B fill:#E1D5E7,stroke:#9673A6
style C fill:#D5E8D4,stroke:#82B366
style D fill:#D5E8D4,stroke:#82B366
style E fill:#D5E8D4,stroke:#82B366
style F fill:#D5E8D4,stroke:#82B366
style G fill:#D5E8D4,stroke:#82B366
style H fill:#FCE5CD,stroke:#D6B656
style I fill:#FCE5CD,stroke:#D6B656
```

**Diagram sources**
- [socket.ts](file://frontend/src/socket.ts#L1-L11)
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L141)
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx#L1-L132)
- [ChatPartners.tsx](file://frontend/src/components/chat/ChatPartners.tsx#L1-L40)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L1-L122)
- [WebSocketStatus.tsx](file://frontend/src/components/WebSocketStatus.tsx#L1-L10)

**Section sources**
- [socket.ts](file://frontend/src/socket.ts#L1-L11)
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L141)
- [MainChat.tsx](file://frontend/src/components/chat/MainChat.tsx#L1-L132)
- [ChatPartners.tsx](file://frontend/src/components/chat/ChatPartners.tsx#L1-L40)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L1-L122)
- [WebSocketStatus.tsx](file://frontend/src/components/WebSocketStatus.tsx#L1-L10)

## Conclusion
The WebSocket-based real-time communication system in takeNpass provides a robust foundation for instant messaging and notifications. By combining Socket.IO for real-time push events with tRPC for reliable request-response operations, the system achieves both responsiveness and data consistency. The architecture effectively manages user sessions, delivers targeted notifications, and integrates seamlessly with the React frontend. Future enhancements could include Redis-based scaling, message queuing for offline users, and improved reconnection handling to further strengthen the real-time experience.