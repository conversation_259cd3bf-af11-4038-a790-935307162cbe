# Message Model

<cite>
**Referenced Files in This Document**   
- [MessageSchema.ts](file://server/prisma/generated/zod/modelSchema/MessageSchema.ts)
- [message.ts](file://server/src/services/message.ts)
- [message.ts](file://server/src/router/routers/message.ts)
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Message Entity Structure](#message-entity-structure)
3. [Field Definitions](#field-definitions)
4. [Relationships](#relationships)
5. [Database Constraints and Indexing](#database-constraints-and-indexing)
6. [Message Status and Read Receipts](#message-status-and-read-receipts)
7. [Content Validation and Security](#content-validation-and-security)
8. [Data Retrieval Queries](#data-retrieval-queries)
9. [Conclusion](#conclusion)

## Introduction
The Message model in takeNpass serves as the core entity for the real-time chat system, enabling private communication between users within the context of travel cases. This document provides comprehensive documentation of the Message entity, detailing its structure, relationships, constraints, and operational logic. The model supports essential messaging features including message persistence, read status tracking, and efficient retrieval patterns.

**Section sources**
- [MessageSchema.ts](file://server/prisma/generated/zod/modelSchema/MessageSchema.ts#L0-L46)

## Message Entity Structure
The Message entity is a Prisma-defined database model that captures all aspects of a chat message exchanged between users. It is designed to support high-performance message retrieval, reliable delivery tracking, and integration with user and case contexts.

```mermaid
erDiagram
MESSAGE {
number id PK
date createdAt
string content
string senderId FK
string receiverId FK
boolean read
}
USER {
string id PK
string username
string email
string avatar
string language
}
MESSAGE ||--|| USER : "sender"
MESSAGE ||--|| USER : "receiver"
```

**Diagram sources**
- [MessageSchema.ts](file://server/prisma/generated/zod/modelSchema/MessageSchema.ts#L0-L46)
- [message.ts](file://server/src/services/message.ts#L0-L56)

## Field Definitions
The Message entity consists of the following fields, each serving a specific purpose in the messaging system:

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| **id** | number (integer) | Unique identifier for the message | Auto-generated primary key |
| **createdAt** | date | Timestamp when the message was created | Defaults to current timestamp |
| **content** | string | The actual message text | Required, minimum length enforced |
| **senderId** | string | ID of the user who sent the message | Foreign key to User table |
| **receiverId** | string | ID of the user who received the message | Foreign key to User table |
| **read** | boolean | Indicates whether the message has been read | Defaults to false |

**Section sources**
- [MessageSchema.ts](file://server/prisma/generated/zod/modelSchema/MessageSchema.ts#L0-L46)

## Relationships
The Message entity maintains bidirectional relationships with the User entity through sender and receiver references. Each message is associated with exactly one sender and one receiver, both of whom are User records.

The relationship schema shows that:
- A User can send multiple messages (1-to-many with sent messages)
- A User can receive multiple messages (1-to-many with received messages)
- Each Message has exactly one sender and one receiver

```mermaid
classDiagram
class Message {
+number id
+Date createdAt
+string content
+string senderId
+string receiverId
+boolean read
}
class User {
+string id
+string username
+string email
+string avatar
+string language
}
Message --> User : sender
Message --> User : receiver
```

**Diagram sources**
- [MessageSchema.ts](file://server/prisma/generated/zod/modelSchema/MessageSchema.ts#L43-L80)
- [message.ts](file://server/src/services/message.ts#L0-L56)

## Database Constraints and Indexing
The Message model enforces referential integrity through foreign key constraints on senderId and receiverId, ensuring that every message references valid user accounts. While specific index definitions are not visible in the provided code, the query patterns suggest that composite indexes exist on:
- (receiverId, read) for efficient unread message counting
- (senderId, receiverId) for conversation thread retrieval
- (receiverId, createdAt) for chronological message loading

These indexing strategies optimize the most common access patterns in the chat system, particularly message retrieval by user pairs and unread message queries.

**Section sources**
- [MessageSchema.ts](file://server/prisma/generated/zod/modelSchema/MessageSchema.ts#L0-L46)
- [message.ts](file://server/src/services/message.ts#L0-L56)

## Message Status and Read Receipts
The Message model implements a read receipt system through the boolean `read` field. When a message is delivered, it is created with `read = false`. The system updates this status when the recipient views the message.

The read status management is handled by the MessageService:
- `setAsRead(userId, messageId)` updates a specific message as read
- `unreadCount(userId)` returns the count of unread messages for a user
- Frontend displays visual indicators: single checkmark for delivered, double checkmarks for read

```mermaid
sequenceDiagram
participant Sender
participant Server
participant Receiver
participant DB
Sender->>Server : Send Message
Server->>DB : Create message (read=false)
DB-->>Server : Message stored
Server->>Receiver : Deliver message
Receiver->>Server : View conversation
Server->>DB : Update message.read = true
DB-->>Server : Update confirmed
Server->>Receiver : Show double checkmarks
```

**Diagram sources**
- [message.ts](file://server/src/services/message.ts#L0-L56)
- [MessageItem.tsx](file://frontend/src/components/chat/MessageItem.tsx#L78-L110)

## Content Validation and Security
Message content is validated both at the API and service layers to ensure data integrity and security. The input validation schema enforces:
- Content field is required
- Content must have a minimum length of 1 character
- Both senderId and receiverId must be valid strings

Security measures include:
- Authentication required for all message operations
- Authorization checks ensure users can only mark their own messages as read
- Input sanitization to prevent injection attacks
- Rate limiting on message sending

**Section sources**
- [message.ts](file://server/src/services/message.ts#L0-L56)
- [message.ts](file://server/src/router/routers/message.ts#L0-L47)

## Data Retrieval Queries
The system provides optimized queries for common messaging operations:

### Conversation Thread Retrieval
```typescript
// Retrieve all messages between two users
await MessageService.db.findMany({
  where: {
    OR: [
      { senderId: userId, receiverId: partnerId },
      { senderId: partnerId, receiverId: userId }
    ]
  },
  orderBy: { createdAt: 'asc' }
})
```

### Unread Message Counting
```typescript
// Count unread messages for current user
await MessageService.unreadCount(sessionUser.id)
```

### Recent Chats List
```typescript
// Get list of conversation partners with last message
await MessageService.chats(sessionUser.id)
```

**Section sources**
- [message.ts](file://server/src/services/message.ts#L0-L56)
- [message.ts](file://server/src/router/routers/message.ts#L0-L47)

## Conclusion
The Message model in takeNpass provides a robust foundation for real-time chat functionality, with well-defined fields, relationships, and operational logic. Its design supports efficient message retrieval, reliable read receipt tracking, and secure communication between users. The combination of database constraints, service-layer logic, and frontend integration creates a seamless messaging experience that is both performant and user-friendly.