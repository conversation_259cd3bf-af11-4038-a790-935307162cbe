# Getting Started

<cite>
**Referenced Files in This Document**  
- [package.json](file://frontend/package.json)
- [package.json](file://server/package.json)
- [vite.config.ts](file://frontend/vite.config.ts)
- [tsconfig.json](file://server/tsconfig.json)
- [config.ts](file://server/src/config.ts)
- [server.ts](file://server/src/server.ts)
- [prisma](file://server/prisma)
- [README.md](file://frontend/README.md)
</cite>

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Frontend Installation](#frontend-installation)
3. [Backend Installation](#backend-installation)
4. [Environment Configuration](#environment-configuration)
5. [Database Setup with Prisma](#database-setup-with-prisma)
6. [Running Development Servers](#running-development-servers)
7. [Building for Production](#building-for-production)
8. [Running Tests](#running-tests)
9. [IDE and Development Setup](#ide-and-development-setup)
10. [Troubleshooting Common Issues](#troubleshooting-common-issues)
11. [Verification of Installation](#verification-of-installation)

## Prerequisites

Before setting up takeNpass, ensure the following tools are installed on your system:

- **Node.js** (version 18 or higher)
- **npm** (usually included with Node.js)
- **MySQL** (version 8.0 or higher)
- **Git** (for cloning and version control)
- **Prisma CLI** (installed via `npm install -g prisma`)

Verify installations with the following commands:
```bash
node --version
npm --version
mysql --version
git --version
npx prisma --version
```

## Frontend Installation

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Confirm the installation by checking the `node_modules` directory and ensuring all packages from `package.json` are present.

**Section sources**
- [package.json](file://frontend/package.json#L1-L78)

## Backend Installation

1. Navigate to the server directory:
```bash
cd server
```

2. Install dependencies:
```bash
npm install
```

3. The backend uses TypeScript with ESBuild and Fastify, and dependencies include Prisma, tRPC, and Socket.IO for real-time communication.

**Section sources**
- [package.json](file://server/package.json#L1-L57)

## Environment Configuration

Although `.env.example` files are not present in the repository, the following environment variables are required based on configuration patterns in the codebase.

### Frontend Environment Variables
Create a `.env` file in the `frontend` directory with the following:
```
VITE_API_URL_DEV=http://localhost:3003
VITE_API_URL=http://your-production-api.com
VITE_API_WS_URL_DEV=http://localhost:3003
VITE_API_WS_URL=ws://your-production-ws.com
```

### Backend Environment Variables
Create a `.env` file in the `server` directory. Required variables include:
```
DATABASE_URL=mysql://USER:PASSWORD@localhost:3306/takenpass
AUTH_SECRET=your_jwt_secret_key
NODE_ENV=development
PORT=3003
```

These are inferred from Prisma usage and standard Fastify configuration.

**Section sources**
- [vite.config.ts](file://frontend/vite.config.ts#L1-L39)
- [config.ts](file://server/src/config.ts)

## Database Setup with Prisma

1. Ensure MySQL is running and accessible.

2. Initialize the database schema using Prisma migrations:
```bash
cd server
npx prisma migrate dev --name init
```

3. Seed the database with initial data:
```bash
npx prisma db seed
```

This will execute seed scripts located in `prisma/seeds/`, including `UserSeed.ts` and `UserAuthLogSeed.ts`.

4. Generate Prisma Client:
```bash
npx prisma generate
```

This creates type-safe database clients in `prisma/generated/zod/`.

**Section sources**
- [prisma](file://server/prisma#L1-L10)
- [config.ts](file://server/src/config.ts)

## Running Development Servers

1. Start the backend server:
```bash
cd server
npm run dev
```
This runs `tsx watch` to automatically reload the server on code changes.

2. In a separate terminal, start the frontend:
```bash
cd frontend
npm run dev
```
Vite will launch the development server, typically on `http://localhost:5173`.

3. Access the application in your browser.

**Section sources**
- [server.ts](file://server/src/server.ts)
- [vite.config.ts](file://frontend/vite.config.ts#L1-L39)

## Building for Production

1. Build the frontend assets:
```bash
cd frontend
npm run build
```
This generates optimized static files in the `dist/` directory.

2. Build the backend:
```bash
cd server
npm run build
```
This bundles the server code using ESBuild into the `dist/` folder.

3. Start the production server:
```bash
npm start
```

**Section sources**
- [package.json](file://frontend/package.json#L7-L10)
- [package.json](file://server/package.json#L6-L13)

## Running Tests

The backend includes unit and integration tests using Vitest.

1. Run all tests:
```bash
cd server
npm test
```

2. Run tests in UI mode:
```bash
npm run test:ui
```

3. Specific test files like `auth.test.ts`, `caseService.test.js`, and `geoService.test.js` are located in the `test/` directory.

**Section sources**
- [auth.test.ts](file://server/test/auth.test.ts)
- [caseService.test.js](file://server/test/caseService.test.js)
- [geoService.test.js](file://server/test/geoService.test.js)
- [package.json](file://server/package.json#L14-L16)

## IDE and Development Setup

### Recommended IDE: VS Code

1. Install TypeScript and ESLint extensions.
2. Configure `tsconfig.json` for both frontend and backend:
   - Frontend uses `tsconfig.json` and `tsconfig.node.json`
   - Backend uses `tsconfig.json` with Node.js and ESNext targeting

3. Enable path alias support (`@/*` resolves to `src/` in frontend).

4. For debugging:
   - Use VS Code's built-in debugger with `launch.json`:
     ```json
     {
       "type": "node",
       "request": "attach",
       "name": "Attach to Backend",
       "port": 9229,
       "skipFiles": ["<node_internals>/**"]
     }
     ```
   - Start backend with `npm run dev:server` for debugging support.

**Section sources**
- [tsconfig.json](file://server/tsconfig.json#L1-L21)
- [tsconfig.json](file://frontend/tsconfig.json)
- [vite.config.ts](file://frontend/vite.config.ts#L1-L39)

## Troubleshooting Common Issues

### 1. Database Connection Errors
- **Symptom**: Prisma migration fails with "Connection refused"
- **Solution**: 
  - Verify MySQL is running (`sudo service mysql start`)
  - Check `DATABASE_URL` in `.env`
  - Ensure correct port (default 3306) and credentials

### 2. Port Conflicts
- **Symptom**: "Port 3003 is already in use"
- **Solution**:
  - Change `PORT` in `.env` to another value (e.g., 4003)
  - Or kill the process: `lsof -i :3003` (macOS/Linux) or `netstat -ano | findstr :3003` (Windows)

### 3. Dependency Installation Failures
- **Symptom**: `npm install` fails with EPERM or EACCES
- **Solution**:
  - Run as administrator or use `sudo`
  - Clear npm cache: `npm cache clean --force`
  - Delete `node_modules` and `package-lock.json`, then reinstall

### 4. Prisma Generation Issues
- **Symptom**: `prisma generate` fails or types are missing
- **Solution**:
  - Ensure `prisma` is installed globally
  - Run `npx prisma generate` from the `server` directory
  - Check that `prisma/schema.prisma` exists and is valid

### 5. WebSocket Connection Failures
- **Symptom**: Frontend cannot connect to `/socket.io`
- **Solution**:
  - Verify backend server is running
  - Check `VITE_API_WS_URL_DEV` in frontend `.env`
  - Ensure CORS is properly configured in Fastify

**Section sources**
- [server.ts](file://server/src/server.ts)
- [vite.config.ts](file://frontend/vite.config.ts#L1-L39)
- [config.ts](file://server/src/config.ts)

## Verification of Installation

1. **Frontend Verification**:
   - Open `http://localhost:5173`
   - Confirm the landing page loads without console errors
   - Interact with UI components (e.g., login modal, map)

2. **Backend Verification**:
   - Access `http://localhost:3003/trpc/health` – should return `{"ok":true}`
   - Test API via curl:
     ```bash
     curl http://localhost:3003/trpc/user.get?input=%7B%7D
     ```

3. **WebSocket Verification**:
   - Open browser dev tools and check WebSocket connection to `ws://localhost:3003/socket.io/`
   - Look for successful handshake and `connect` event

4. **End-to-End Test**:
   - Register a new user via the frontend
   - Confirm user appears in the database via `npx prisma studio`
   - Send a message in the chat and verify real-time delivery

**Section sources**
- [server.ts](file://server/src/server.ts)
- [trpc.ts](file://server/src/router/trpc.ts)
- [socket.ts](file://frontend/src/socket.ts)
- [App.tsx](file://frontend/src/App.tsx)