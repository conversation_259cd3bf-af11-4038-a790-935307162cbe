import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayToCreateWithoutCaseInputSchema } from './WayToCreateWithoutCaseInputSchema';
import { WayToUncheckedCreateWithoutCaseInputSchema } from './WayToUncheckedCreateWithoutCaseInputSchema';
import { WayToCreateOrConnectWithoutCaseInputSchema } from './WayToCreateOrConnectWithoutCaseInputSchema';
import { WayToWhereUniqueInputSchema } from './WayToWhereUniqueInputSchema';

export const WayToUncheckedCreateNestedOneWithoutCaseInputSchema: z.ZodType<Prisma.WayToUncheckedCreateNestedOneWithoutCaseInput> = z.object({
  create: z.union([ z.lazy(() => WayToCreateWithoutCaseInputSchema),z.lazy(() => WayToUncheckedCreateWithoutCaseInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => WayToCreateOrConnectWithoutCaseInputSchema).optional(),
  connect: z.lazy(() => WayToWhereUniqueInputSchema).optional()
}).strict();

export default WayToUncheckedCreateNestedOneWithoutCaseInputSchema;
