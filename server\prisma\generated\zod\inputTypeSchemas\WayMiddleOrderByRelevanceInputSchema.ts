import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayMiddleOrderByRelevanceFieldEnumSchema } from './WayMiddleOrderByRelevanceFieldEnumSchema';
import { SortOrderSchema } from './SortOrderSchema';

export const WayMiddleOrderByRelevanceInputSchema: z.ZodType<Prisma.WayMiddleOrderByRelevanceInput> = z.object({
  fields: z.union([ z.lazy(() => WayMiddleOrderByRelevanceFieldEnumSchema),z.lazy(() => WayMiddleOrderByRelevanceFieldEnumSchema).array() ]),
  sort: z.lazy(() => SortOrderSchema),
  search: z.string()
}).strict();

export default WayMiddleOrderByRelevanceInputSchema;
