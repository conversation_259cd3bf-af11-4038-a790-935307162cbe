import { User, UserAvatar } from '@/types'

export interface ChatMessage {
  id: number
  createdAt: Date
  content: string
  senderId: string
  receiverId: string
  read: boolean | null
  sender: ChatUser
  receiver?: ChatUser
}

export interface ChatUser {
  id: string
  username: string
  email: string
  avatar?: UserAvatar | null
}

export interface ChatPartner extends ChatUser {
  lastMessage?: ChatMessage
  unreadCount?: number
}

export interface SendMessageRequest {
  content: string
  receiverId: string
}

export interface SendMessageResponse {
  id: number
}

export interface MarkAsReadRequest {
  messageId: number
}

export interface ChatState {
  messages: ChatMessage[]
  isLoading: boolean
  isSending: boolean
  error: string | null
  hasMore: boolean
}

export interface MessageItemProps {
  message: ChatMessage
  isOwn: boolean
  sessionUserId?: string
  onMarkAsRead?: (messageId: number) => void
}

export interface ChatInputProps {
  value: string
  onChange: (value: string) => void
  onSend: () => void
  isLoading: boolean
  isDisabled: boolean
  placeholder?: string
}

export interface ChatHeaderProps {
  partner?: ChatPartner
  onBack?: () => void
}

export interface ChatMessagesProps {
  messages: ChatMessage[]
  sessionUserId?: string
  isLoading: boolean
  onMarkAsRead?: (messageId: number) => void
  onScrollToBottom?: () => void
}

export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'error'

export interface OptimisticMessage extends Omit<ChatMessage, 'id' | 'createdAt'> {
  id: string // temporary ID for optimistic updates
  createdAt: Date
  status: MessageStatus
  tempId?: string
}
