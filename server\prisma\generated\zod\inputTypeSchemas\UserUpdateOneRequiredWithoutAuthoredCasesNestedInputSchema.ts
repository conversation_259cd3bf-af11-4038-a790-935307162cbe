import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserCreateWithoutAuthoredCasesInputSchema } from './UserCreateWithoutAuthoredCasesInputSchema';
import { UserUncheckedCreateWithoutAuthoredCasesInputSchema } from './UserUncheckedCreateWithoutAuthoredCasesInputSchema';
import { UserCreateOrConnectWithoutAuthoredCasesInputSchema } from './UserCreateOrConnectWithoutAuthoredCasesInputSchema';
import { UserUpsertWithoutAuthoredCasesInputSchema } from './UserUpsertWithoutAuthoredCasesInputSchema';
import { UserWhereUniqueInputSchema } from './UserWhereUniqueInputSchema';
import { UserUpdateToOneWithWhereWithoutAuthoredCasesInputSchema } from './UserUpdateToOneWithWhereWithoutAuthoredCasesInputSchema';
import { UserUpdateWithoutAuthoredCasesInputSchema } from './UserUpdateWithoutAuthoredCasesInputSchema';
import { UserUncheckedUpdateWithoutAuthoredCasesInputSchema } from './UserUncheckedUpdateWithoutAuthoredCasesInputSchema';

export const UserUpdateOneRequiredWithoutAuthoredCasesNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutAuthoredCasesNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutAuthoredCasesInputSchema),z.lazy(() => UserUncheckedCreateWithoutAuthoredCasesInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutAuthoredCasesInputSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutAuthoredCasesInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => UserUpdateToOneWithWhereWithoutAuthoredCasesInputSchema),z.lazy(() => UserUpdateWithoutAuthoredCasesInputSchema),z.lazy(() => UserUncheckedUpdateWithoutAuthoredCasesInputSchema) ]).optional(),
}).strict();

export default UserUpdateOneRequiredWithoutAuthoredCasesNestedInputSchema;
