# Real-time Communication

<cite>
**Referenced Files in This Document**   
- [ws.ts](file://server/src/providers/ws.ts)
- [depr_ws.ts](file://server/src/services/depr_ws.ts)
- [socket.ts](file://frontend/src/socket.ts)
- [useChat.ts](file://frontend/src/hooks/useChat.ts)
- [chat.ts](file://frontend/src/types/chat.ts)
- [message.ts](file://server/src/services/message.ts)
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts)
- [WebSocketStatus.tsx](file://frontend/src/components/WebSocketStatus.tsx)
- [App.tsx](file://frontend/src/App.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [WebSocket Implementation Overview](#websocket-implementation-overview)
3. [Server-Side WebSocket Providers](#server-side-websocket-providers)
4. [Client-Side Socket Management](#client-side-socket-management)
5. [Chat State Handling](#chat-state-handling)
6. [Message Formats and Event Types](#message-formats-and-event-types)
7. [Real-Time Interaction Patterns](#real-time-interaction-patterns)
8. [Protocol-Specific Examples](#protocol-specific-examples)
9. [Error Handling and Reconnection Logic](#error-handling-and-reconnection-logic)
10. [Security Considerations](#security-considerations)
11. [Performance Optimization Tips](#performance-optimization-tips)
12. [Client Implementation Guidelines](#client-implementation-guidelines)

## Introduction
The takeNpass real-time communication system enables bidirectional messaging between clients and server using WebSocket technology. This document details the implementation of Socket.IO for real-time chat functionality, covering both modern and legacy WebSocket providers, client-side socket management, and state handling mechanisms. The system supports instant message delivery, read receipts, and live notifications while maintaining robust error handling and security practices.

## WebSocket Implementation Overview
The real-time communication system in takeNpass utilizes Socket.IO for bidirectional communication between clients and server. The implementation supports persistent connections for instant messaging, notifications, and presence updates. Two WebSocket providers exist in the codebase: a modern Socket.IO-based implementation in `ws.ts` and a legacy `ws`-based implementation in `depr_ws.ts`. The client establishes connections using the Socket.IO client library, with automatic reconnection capabilities and connection status monitoring.

```mermaid
graph TD
A[Client Application] --> B[Socket.IO Client]
B --> C[Load Balancer/Reverse Proxy]
C --> D[Socket.IO Server]
D --> E[Authentication Service]
D --> F[Message Service]
D --> G[Notification Service]
F --> H[Database]
G --> H
```

**Diagram sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [socket.ts](file://frontend/src/socket.ts#L1-L9)

**Section sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [socket.ts](file://frontend/src/socket.ts#L1-L9)

## Server-Side WebSocket Providers
The server implements two WebSocket providers for backward compatibility and gradual migration. The primary provider in `ws.ts` uses Socket.IO for enhanced features including automatic reconnection, rooms, and event acknowledgment. The legacy provider in `depr_ws.ts` uses the raw `ws` library for basic WebSocket functionality.

The Socket.IO provider (`ws.ts`) initializes with the server instance and handles authentication via session cookies. Upon successful authentication, it maps user IDs to socket connections in a static clients map, enabling targeted message delivery. The legacy provider (`depr_ws.ts`) follows a similar pattern but uses the native WebSocket API without Socket.IO's additional features.

Both providers implement static methods for retrieving user sockets and sending messages to specific users or broadcast to all connected clients. The Socket.IO provider uses `emit()` for event-based messaging, while the legacy provider uses `send()` with JSON stringification.

```mermaid
classDiagram
class IO {
+static wss : any
+static clients : Map<string, Socket>
+static init(serverInst : any) : void
+static getUserSocket(userId : string) : Socket
+static sendMessageToUser(userId : string, data : any) : void
}
class WebSocketService {
+static wss : typeof serverInstance.server.websocketServer
+static clients : Map<string, WebSocket>
+static init() : void
+static getUserSocket(userId : string) : WebSocket
+static sendMessageToUser(userId : string, data : MessageType) : void
+static sendMessageToRoom(message : MessageType) : void
}
IO --> "uses" Socket : Socket.IO
WebSocketService --> "uses" WebSocket : ws library
```

**Diagram sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [depr_ws.ts](file://server/src/services/depr_ws.ts#L1-L82)

**Section sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [depr_ws.ts](file://server/src/services/depr_ws.ts#L1-L82)

## Client-Side Socket Management
The client-side WebSocket management is implemented in `socket.ts`, which creates a singleton Socket.IO client instance. The connection URL is set to the root path, allowing the client to automatically determine the server address from the window location. This configuration enables seamless operation in both development and production environments without requiring environment-specific configuration.

The socket instance is exported as a module-level constant, ensuring a single shared connection across the application. Connection events are monitored in `App.tsx`, where listeners for 'connect' and 'disconnect' events update the application's WebSocket connection status. This status is visualized in the UI through the `WebSocketStatus` component, which displays a connection indicator icon.

```mermaid
sequenceDiagram
participant App as App.tsx
participant Socket as socket.ts
participant Server as WebSocket Server
App->>Socket : Import socket instance
Socket->>Server : Connect to /
Server-->>Socket : Connection established
Socket-->>App : Emit 'connect' event
App->>App : Update wsIsConnect state to true
Note over App,Server : Connection maintained for real-time communication
```

**Diagram sources**
- [socket.ts](file://frontend/src/socket.ts#L1-L9)
- [App.tsx](file://frontend/src/App.tsx#L32-L86)
- [WebSocketStatus.tsx](file://frontend/src/components/WebSocketStatus.tsx#L1-L10)

**Section sources**
- [socket.ts](file://frontend/src/socket.ts#L1-L9)
- [App.tsx](file://frontend/src/App.tsx#L32-L86)

## Chat State Handling
The `useChat` hook provides comprehensive chat state management for the application. It handles message fetching, sending, and UI state synchronization using React's useState and useEffect hooks. The hook integrates with tRPC for data fetching and mutation, leveraging React Query's caching and refetching capabilities.

Key features of the chat state management include:
- Automatic message fetching when a partner is selected
- Optimistic UI updates for message sending
- Auto-scrolling to the bottom of the chat container when new messages arrive
- Input validation and error handling
- Toast notifications for send success and failure

The hook exposes a structured API including state variables, action functions, and refs for integration with chat UI components. It also listens for 'NEW_MESSAGE' events via the event bus to trigger message refetching when new messages arrive.

```mermaid
flowchart TD
A[useChat Hook] --> B[Initialize State]
B --> C[Fetch Messages via tRPC]
C --> D[Set isLoading to true]
D --> E[Messages Fetched]
E --> F[Update messages state]
F --> G[Auto-scroll to bottom]
G --> H[Listen for NEW_MESSAGE event]
H --> I[Refetch messages when triggered]
I --> C
J[User Types Message] --> K[Update inputValue]
K --> L[Click Send Button]
L --> M[Validate Input]
M --> N{Valid?}
N --> |Yes| O[Call sendMessageMutation]
N --> |No| P[Show Error Toast]
O --> Q[Show Success Toast]
Q --> R[Clear Input]
R --> S[Refetch Messages]
S --> C
```

**Diagram sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L138)

**Section sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L138)

## Message Formats and Event Types
The system defines structured message formats for different types of real-time communication. Chat messages follow the `ChatMessage` interface defined in `chat.ts`, containing fields for message content, sender and receiver IDs, timestamps, and read status. The system also supports notification events with the `SocketNotification` type, which includes metadata about the notification type, creation time, and associated entities.

Key message types include:
- **Chat messages**: Text messages between users with delivery and read status
- **New message notifications**: Events triggered when a user receives a message
- **Connection status events**: 'connect' and 'disconnect' events for presence tracking
- **Custom events**: 'xnotification' events for various system notifications

The message format includes sender and receiver information with user details such as username, email, and avatar, enabling rich UI rendering without additional API calls.

```mermaid
erDiagram
CHAT_MESSAGE {
number id PK
datetime createdAt
string content
string senderId FK
string receiverId FK
boolean read
}
USER {
string id PK
string username
string email
json avatar
}
NOTIFICATION {
number id PK
datetime createdAt
string userId FK
string senderId FK
string translationKey
boolean read
json data
}
CHAT_MESSAGE ||--|{ USER : sender
CHAT_MESSAGE ||--|{ USER : receiver
NOTIFICATION ||--|{ USER : user
NOTIFICATION ||--|{ USER : sender
```

**Diagram sources**
- [chat.ts](file://frontend/src/types/chat.ts#L1-L83)
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts#L1-L47)

**Section sources**
- [chat.ts](file://frontend/src/types/chat.ts#L1-L83)
- [SocketNotificationType.ts](file://server/src/types/SocketNotificationType.ts#L1-L47)

## Real-Time Interaction Patterns
The real-time communication system implements several interaction patterns to ensure responsive and reliable messaging. These patterns include request-response flows for message sending, publish-subscribe for notifications, and connection management for presence tracking.

When a user sends a message, the client makes a tRPC mutation call to the server. Upon successful storage in the database, the server triggers a notification service that uses the WebSocket provider to send a real-time update to the recipient. The recipient's client then receives the notification and triggers a message refetch, ensuring the UI stays up-to-date.

The system also implements read receipts through a separate mechanism. When a user views messages from a specific partner, the application marks those messages as read via a tRPC mutation, which updates the database and could potentially trigger a read receipt notification to the sender.

```mermaid
sequenceDiagram
participant Sender as Sender Client
participant Server as Server
participant Recipient as Recipient Client
participant DB as Database
Sender->>Server : send message (tRPC mutation)
Server->>DB : Store message
DB-->>Server : Message ID
Server->>Server : Trigger notification
Server->>Recipient : Emit 'xnotification' event
Recipient->>Server : Refetch messages (tRPC query)
Server->>DB : Query messages
DB-->>Server : Message data
Server-->>Recipient : Return messages
Recipient->>Recipient : Update UI with new message
Note over Sender,Recipient : Real-time message delivery complete
```

**Diagram sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L138)
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [message.ts](file://server/src/services/message.ts#L1-L263)

**Section sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L138)
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [message.ts](file://server/src/services/message.ts#L1-L263)

## Protocol-Specific Examples
The WebSocket implementation supports several protocol-specific patterns for different messaging scenarios:

**Message Sending Flow:**
1. User types message in chat input
2. Client validates input content
3. tRPC mutation sends message to server
4. Server stores message in database
5. Server sends notification to recipient via WebSocket
6. Recipient client receives notification and refetches messages
7. UI updates with new message

**Delivery Confirmation:**
The system uses tRPC mutation status to confirm message delivery to the server. The `sendMessageMutation.isPending` state indicates when a message is being sent, while success and error callbacks handle the outcomes. Server-side logging in the WebSocket providers confirms message delivery to connected clients.

**Read Receipts:**
Read status is managed through the message entity's `read` field. When a user views a conversation, the application could implement automatic read receipt marking by calling the `setAsRead` method in the MessageService for all unread messages in that conversation.

```mermaid
flowchart LR
A[User Sends Message] --> B{Content Valid?}
B --> |No| C[Show Validation Error]
B --> |Yes| D[Call sendMessageMutation]
D --> E[Server Stores Message]
E --> F[Server Sends Notification]
F --> G[Recipient Receives Notification]
G --> H[Recipient Refetches Messages]
H --> I[UI Updates with New Message]
I --> J[Message Marked as Delivered]
K[Recipient Views Message] --> L[Call setAsRead]
L --> M[Update read status in DB]
M --> N[Send Read Receipt (optional)]
N --> O[Sender Sees Read Status]
```

**Diagram sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L138)
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)

**Section sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L138)
- [message.ts](file://server/src/services/message.ts#L1-L263)

## Error Handling and Reconnection Logic
The WebSocket implementation includes comprehensive error handling and reconnection mechanisms. The Socket.IO client automatically attempts to reconnect when the connection is lost, with exponential backoff to prevent overwhelming the server. Connection status is monitored through 'connect' and 'disconnect' events, which update the application state accordingly.

On the server side, both WebSocket providers include error handling in the connection lifecycle. The Socket.IO provider wraps session validation in try-catch blocks to prevent connection failures from unhandled exceptions. When a client disconnects, both providers clean up the client mapping to prevent memory leaks.

Client-side error handling is implemented in the `useChat` hook, where mutation error callbacks display user-friendly toast notifications when message sending fails. The hook also handles loading states to prevent duplicate message sends during network latency.

```mermaid
stateDiagram-v2
[*] --> Disconnected
Disconnected --> Connecting : Attempt connection
Connecting --> Connected : Connection successful
Connecting --> Reconnecting : Connection failed
Reconnecting --> Connecting : Retry after delay
Reconnecting --> Disconnected : Max retries exceeded
Connected --> Disconnected : Manual disconnect or network failure
Connected --> Connected : Heartbeat keep-alive
note right of Reconnecting
Exponential backoff
with jitter
end note
```

**Diagram sources**
- [socket.ts](file://frontend/src/socket.ts#L1-L9)
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L138)

**Section sources**
- [socket.ts](file://frontend/src/socket.ts#L1-L9)
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L138)

## Security Considerations
The WebSocket implementation incorporates several security measures to protect user data and prevent abuse. Authentication is performed during the WebSocket handshake by validating session cookies, ensuring only authenticated users can establish connections. The server extracts the session ID from the cookie header and validates it against the authentication service before associating the socket with a user ID.

Message validation occurs on the server side through the tRPC procedures, which validate input data before processing. The system also implements rate limiting through the `rateLimit.ts` utility to prevent abuse of the messaging system. All message operations require authentication, with the `authedProcedure` ensuring only authorized users can perform actions.

Data transmitted over WebSocket connections includes sensitive information such as user IDs and message content, making secure transport essential. The system relies on HTTPS/TLS for encryption in production environments, with the client automatically using the secure protocol when the application is served over HTTPS.

**Section sources**
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [utils/rateLimit.ts](file://server/src/utils/rateLimit.ts)

## Performance Optimization Tips
To handle high message volumes efficiently, consider the following optimization strategies:

1. **Connection Pooling**: Maintain the singleton socket instance to avoid multiple connections per client
2. **Batch Updates**: When processing multiple messages, batch DOM updates to minimize re-renders
3. **Virtual Scrolling**: Implement virtual scrolling for chat histories with many messages
4. **Message Caching**: Leverage React Query's caching to minimize redundant API calls
5. **Throttled Scrolling**: Use throttling for scroll event handlers to improve performance
6. **Efficient State Updates**: Batch related state updates to reduce re-renders
7. **WebSocket Compression**: Enable WebSocket compression for reduced bandwidth usage
8. **Selective Event Listening**: Only listen for events relevant to the current view

For high-traffic scenarios, consider implementing message queuing and worker processes to handle notification dispatch asynchronously, preventing blocking of the main event loop.

**Section sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L138)
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)

## Client Implementation Guidelines
When implementing client-side features that interact with the real-time communication system, follow these guidelines:

1. **Use the Shared Socket Instance**: Import and use the existing socket instance from `socket.ts` rather than creating new connections
2. **Handle Loading States**: Always account for loading and error states in UI components
3. **Clean Up Event Listeners**: Remove event listeners in useEffect cleanup functions to prevent memory leaks
4. **Leverage tRPC Integration**: Use tRPC for data mutations and queries to ensure type safety and proper error handling
5. **Implement User Feedback**: Provide visual feedback for message sending, delivery, and errors
6. **Respect Rate Limits**: Design UI to prevent users from triggering rate-limited actions too frequently
7. **Handle Reconnection Gracefully**: Ensure the UI reflects connection status and recovers properly after reconnection

The `useChat` hook provides a well-abstracted interface for chat functionality, so prefer using this hook rather than directly interacting with the socket for chat-related operations.

**Section sources**
- [socket.ts](file://frontend/src/socket.ts#L1-L9)
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L138)
- [WebSocketStatus.tsx](file://frontend/src/components/WebSocketStatus.tsx#L1-L10)