import { memo, useRef, useEffect } from 'react'
import { Input, Button } from '@heroui/react'
import { useTranslation } from 'react-i18next'
import IconSendPlaneFill from '@/lib/svg/SendIcon'

interface ChatInputProps {
  value: string
  onChange: (value: string) => void
  onSend: () => void
  isLoading: boolean
  isDisabled: boolean
  placeholder?: string
  autoFocus?: boolean
}

const ChatInput = memo(({ 
  value, 
  onChange, 
  onSend, 
  isLoading, 
  isDisabled, 
  placeholder,
  autoFocus = true
}: ChatInputProps) => {
  const { t } = useTranslation()
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (autoFocus && !isDisabled && inputRef.current) {
      // Small delay to ensure component is fully mounted
      const timer = setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [autoFocus, isDisabled])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Allow Shift+Enter for new lines
        return
      }
      e.preventDefault()
      if (!isLoading && !isDisabled && value.trim()) {
        onSend()
      }
    }
  }

  const canSend = !isLoading && !isDisabled && value.trim().length > 0

  return (
    <div className="flex gap-3 items-end">
      <Input
        ref={inputRef}
        aria-label={t('Type your message')}
        value={value}
        onValueChange={onChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder || t('Input text here')}
        isDisabled={isDisabled}
        size="lg"
        className="flex-1"
        classNames={{
          input: 'min-h-[44px] py-3',
          inputWrapper: 'min-h-[44px]'
        }}
        maxLength={254} // Match database constraint
      />
      
      <Button
        isLoading={isLoading}
        onPress={onSend}
        size="lg"
        isIconOnly
        isDisabled={!canSend}
        color="primary"
        aria-label={t('Send message')}
        className="min-w-[44px] h-[44px]"
      >
        <IconSendPlaneFill className="w-5 h-5" />
      </Button>
    </div>
  )
})

ChatInput.displayName = 'ChatInput'

export { ChatInput }
