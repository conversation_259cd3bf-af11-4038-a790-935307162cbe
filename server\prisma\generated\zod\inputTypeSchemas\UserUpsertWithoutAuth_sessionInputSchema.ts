import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserUpdateWithoutAuth_sessionInputSchema } from './UserUpdateWithoutAuth_sessionInputSchema';
import { UserUncheckedUpdateWithoutAuth_sessionInputSchema } from './UserUncheckedUpdateWithoutAuth_sessionInputSchema';
import { UserCreateWithoutAuth_sessionInputSchema } from './UserCreateWithoutAuth_sessionInputSchema';
import { UserUncheckedCreateWithoutAuth_sessionInputSchema } from './UserUncheckedCreateWithoutAuth_sessionInputSchema';
import { UserWhereInputSchema } from './UserWhereInputSchema';

export const UserUpsertWithoutAuth_sessionInputSchema: z.ZodType<Prisma.UserUpsertWithoutAuth_sessionInput> = z.object({
  update: z.union([ z.lazy(() => UserUpdateWithoutAuth_sessionInputSchema),z.lazy(() => UserUncheckedUpdateWithoutAuth_sessionInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutAuth_sessionInputSchema),z.lazy(() => UserUncheckedCreateWithoutAuth_sessionInputSchema) ]),
  where: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export default UserUpsertWithoutAuth_sessionInputSchema;
