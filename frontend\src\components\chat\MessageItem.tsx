import { useRef, useEffect, memo } from 'react'
import { Avatar, Snippet } from '@heroui/react'
import { useIntersectionObserver } from '@uidotdev/usehooks'
import { useTranslation } from 'react-i18next'
import { ChatMessage } from '@/types/chat'
import { useMessageRead } from '@/hooks/useMessageRead'

interface MessageItemProps {
  message: ChatMessage
  isOwn: boolean
  sessionUserId?: string
  showAvatar?: boolean
  showTimestamp?: boolean
}

const MessageItem = memo(({ 
  message, 
  isOwn, 
  sessionUserId, 
  showAvatar = true, 
  showTimestamp = true 
}: MessageItemProps) => {
  const { t } = useTranslation()
  const elementRef = useRef<HTMLDivElement>(null)
  const [entry] = useIntersectionObserver(elementRef, {
    threshold: 0.5,
    rootMargin: '0px'
  })
  
  const isVisible = !!entry?.isIntersecting
  const { markMessageAsRead } = useMessageRead({ sessionUserId })

  // Mark message as read when it becomes visible
  useEffect(() => {
    if (isVisible && !isOwn && !message.read) {
      markMessageAsRead(message, isVisible)
    }
  }, [isVisible, isOwn, message, markMessageAsRead])

  const messageColor = isOwn ? 'primary' : 'default'
  const avatarColor = isOwn ? 'primary' : 'default'

  return (
    <div 
      ref={elementRef}
      className={`flex mb-5 ${isOwn ? 'justify-end' : 'justify-start'}`}
    >
      <div className={`max-w-[70%] ${isOwn ? 'order-2' : 'order-1'}`}>
        {showTimestamp && (
          <div className={`text-default-500 text-xs mb-2 ${isOwn ? 'text-right' : 'text-left'}`}>
            {message.createdAt.toLocaleString()}
          </div>
        )}
        
        <div className={`flex items-end gap-3 ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
          {showAvatar && (
            <Avatar
              size="sm"
              color={avatarColor}
              name={message.sender.username}
              src={message.sender.avatar?.base64string}
              className="flex-shrink-0"
            />
          )}
          
          <div className="flex-1">
            <Snippet
              color={messageColor}
              hideSymbol
              disableTooltip={false}
              tooltipProps={{
                content: t('Copy to clipboard')
              }}
              size="md"
              className="w-full"
              classNames={{
                base: `${isOwn ? 'bg-primary-100' : 'bg-default-100'}`,
                content: 'whitespace-pre-wrap break-words'
              }}
            >
              {message.content}
            </Snippet>
            
            {/* Message status indicator */}
            {isOwn && (
              <div className="flex justify-end mt-1">
                <div className="flex items-center gap-1 text-xs">
                  {message.read ? (
                    <div className="flex items-center gap-1 text-primary-500">
                      <span>✓✓</span>
                      <span className="text-xs">Прочитано</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-1 text-default-400">
                      <span>✓</span>
                      <span className="text-xs">Доставлено</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
})

MessageItem.displayName = 'MessageItem'

export { MessageItem }
