import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayFromCreateWithoutCaseInputSchema } from './WayFromCreateWithoutCaseInputSchema';
import { WayFromUncheckedCreateWithoutCaseInputSchema } from './WayFromUncheckedCreateWithoutCaseInputSchema';
import { WayFromCreateOrConnectWithoutCaseInputSchema } from './WayFromCreateOrConnectWithoutCaseInputSchema';
import { WayFromUpsertWithoutCaseInputSchema } from './WayFromUpsertWithoutCaseInputSchema';
import { WayFromWhereInputSchema } from './WayFromWhereInputSchema';
import { WayFromWhereUniqueInputSchema } from './WayFromWhereUniqueInputSchema';
import { WayFromUpdateToOneWithWhereWithoutCaseInputSchema } from './WayFromUpdateToOneWithWhereWithoutCaseInputSchema';
import { WayFromUpdateWithoutCaseInputSchema } from './WayFromUpdateWithoutCaseInputSchema';
import { WayFromUncheckedUpdateWithoutCaseInputSchema } from './WayFromUncheckedUpdateWithoutCaseInputSchema';

export const WayFromUpdateOneWithoutCaseNestedInputSchema: z.ZodType<Prisma.WayFromUpdateOneWithoutCaseNestedInput> = z.object({
  create: z.union([ z.lazy(() => WayFromCreateWithoutCaseInputSchema),z.lazy(() => WayFromUncheckedCreateWithoutCaseInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => WayFromCreateOrConnectWithoutCaseInputSchema).optional(),
  upsert: z.lazy(() => WayFromUpsertWithoutCaseInputSchema).optional(),
  disconnect: z.union([ z.boolean(),z.lazy(() => WayFromWhereInputSchema) ]).optional(),
  delete: z.union([ z.boolean(),z.lazy(() => WayFromWhereInputSchema) ]).optional(),
  connect: z.lazy(() => WayFromWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => WayFromUpdateToOneWithWhereWithoutCaseInputSchema),z.lazy(() => WayFromUpdateWithoutCaseInputSchema),z.lazy(() => WayFromUncheckedUpdateWithoutCaseInputSchema) ]).optional(),
}).strict();

export default WayFromUpdateOneWithoutCaseNestedInputSchema;
