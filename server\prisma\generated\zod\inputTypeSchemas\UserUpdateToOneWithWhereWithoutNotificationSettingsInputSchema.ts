import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserWhereInputSchema } from './UserWhereInputSchema';
import { UserUpdateWithoutNotificationSettingsInputSchema } from './UserUpdateWithoutNotificationSettingsInputSchema';
import { UserUncheckedUpdateWithoutNotificationSettingsInputSchema } from './UserUncheckedUpdateWithoutNotificationSettingsInputSchema';

export const UserUpdateToOneWithWhereWithoutNotificationSettingsInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutNotificationSettingsInput> = z.object({
  where: z.lazy(() => UserWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => UserUpdateWithoutNotificationSettingsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutNotificationSettingsInputSchema) ]),
}).strict();

export default UserUpdateToOneWithWhereWithoutNotificationSettingsInputSchema;
