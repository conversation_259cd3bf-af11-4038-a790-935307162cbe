# Case Management API

<cite>
**Referenced Files in This Document**   
- [case.ts](file://server/src/router/routers/case.ts)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts)
- [case.ts](file://server/src/services/case.ts)
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx)
- [CasePage.tsx](file://frontend/src/pages/CasePage.tsx)
- [types.ts](file://frontend/src/types.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Zod Validation Schemas](#zod-validation-schemas)
3. [tRPC Procedures](#trpc-procedures)
4. [Service Layer Business Logic](#service-layer-business-logic)
5. [User Relationships](#user-relationships)
6. [Frontend Integration](#frontend-integration)
7. [Data Flow Overview](#data-flow-overview)
8. [Edge Case Handling](#edge-case-handling)
9. [Error Codes](#error-codes)

## Introduction
The Case Management API in takeNpass enables users to manage delivery cases through a comprehensive set of CRUD operations. This document details the procedures for creating, reading, updating, and deleting delivery cases, including validation rules, service logic, and frontend integration patterns. The system supports both delivery offers and route subscriptions, with rich metadata for origin, destination, waypoints, pricing, and package details.

**Section sources**
- [case.ts](file://server/src/router/routers/case.ts#L1-L74)
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx#L1-L584)

## Zod Validation Schemas
The API employs Zod for robust input validation, ensuring data integrity across case operations.

### Case Creation Schema
The `CaseCreateSchema` validates all fields required for case creation:

```mermaid
classDiagram
class CaseCreateSchema {
+string description?
+string[] baggage?
+number price?
+boolean isRequest?
+Date expire_at?
+WayPointSchema from
+WayPointSchema to
+WayPointSchema[] middlepoints?
}
class WayPointSchema {
+number id?
+GeoMetaSchema geometa
+Date date?
+number|string lat
+number|string lon
+string? comment
}
class GeoMetaSchema {
+object address
+number|string lat
+number|string lon
+string? display_name
+string? type
}
CaseCreateSchema --> WayPointSchema : "contains"
WayPointSchema --> GeoMetaSchema : "references"
```

**Diagram sources**
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L30-L47)

**Section sources**
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L1-L72)

### Validation Rules
- **Origin/Destination**: Both `from` and `to` waypoints are required and must include valid latitude/longitude coordinates
- **Date Constraints**: Waypoint dates must be valid Date objects; expiration date must be in the future
- **Package Details**: Baggage field accepts array of strings or comma-separated string, normalized on storage
- **Geolocation**: Latitude and longitude values are normalized to numbers during processing
- **Waypoints**: Middle points support dynamic creation, update, and deletion through ID-based operations

## tRPC Procedures
The API exposes several tRPC procedures for case management operations.

```mermaid
sequenceDiagram
participant Frontend
participant tRPC
participant Service
participant Database
Frontend->>tRPC : updateOrCreate(input)
tRPC->>tRPC : Validate input with Zod
tRPC->>Service : Queue operation
Service->>Database : Create/Update case
Database-->>Service : Return result
Service-->>tRPC : Process result
tRPC-->>Frontend : Return case ID
```

**Diagram sources**
- [case.ts](file://server/src/router/routers/case.ts#L10-L25)
- [case.ts](file://server/src/services/case.ts#L380-L480)

### Procedure Details
The following tRPC procedures are available:

| Procedure | HTTP Method | Authentication | Purpose |
|---------|------------|----------------|--------|
| updateOrCreate | POST | Required | Create new case or update existing one |
| list | GET | Public | Search cases by criteria |
| myList | GET | Required | Retrieve user's cases by type |
| id | GET | Public | Get specific case by ID |
| assignClient | POST | Required | Assign/unassign user to case |
| toFavofite | POST | Required | Toggle case in user's favorites |

**Section sources**
- [case.ts](file://server/src/router/routers/case.ts#L1-L74)

## Service Layer Business Logic
The `CaseService` class implements the core business logic for case management.

### Case Creation
When creating a case, the service:
1. Normalizes baggage data to comma-separated string
2. Converts string lat/lon values to numbers
3. Creates associated waypoint records
4. For non-request cases, finds subscribers and sends notifications

```mermaid
flowchart TD
Start([Create Case]) --> Normalize["Normalize baggage data"]
Normalize --> Convert["Convert lat/lon to numbers"]
Convert --> CreateWaypoints["Create from/to waypoints"]
CreateWaypoints --> CreateMiddle["Create middle points if present"]
CreateMiddle --> CheckRequest["Is this a request?"]
CheckRequest --> |No| FindSubscribers["Find matching subscribers"]
FindSubscribers --> Notify["Send notifications to subscribers"]
CheckRequest --> |Yes| ReturnResult["Return created case"]
Notify --> ReturnResult
ReturnResult --> End([Case Created])
```

**Diagram sources**
- [case.ts](file://server/src/services/case.ts#L380-L435)

### Case Update
The update operation includes sophisticated logic for managing waypoints:

- **Middle Points**: Uses Prisma's `deleteMany`, `updateMany`, and `createMany` operations
- **Data Integrity**: Preserves existing waypoint IDs while updating properties
- **Authorization**: Verifies that the author is modifying their own case
- **Status Protection**: Prevents modification of cases with status other than OPEN

**Section sources**
- [case.ts](file://server/src/services/case.ts#L300-L480)

## User Relationships
Cases maintain several relationships with users, enabling social and collaborative features.

### Relationship Types
| Relationship | Field | Description |
|------------|-------|-------------|
| Author | authorId | User who created the case |
| Clients | clients | Users assigned to fulfill the delivery |
| Favorites | favorites | Users who have bookmarked the case |
| Subscribers | N/A | Users who have subscribed to similar routes |

### Favorite Management
The `toFavorite` procedure toggles a case in a user's favorites:

```mermaid
flowchart TD
Start([Toggle Favorite]) --> CheckExisting["Check if already favorited"]
CheckExisting --> |Yes| Remove["Remove from favorites"]
CheckExisting --> |No| Add["Add to favorites"]
Remove --> Return["Return isFavorited: true"]
Add --> Return["Return isFavorited: false"]
Return --> End([Operation Complete])
```

**Diagram sources**
- [case.ts](file://server/src/services/case.ts#L120-L140)

**Section sources**
- [case.ts](file://server/src/services/case.ts#L120-L150)

## Frontend Integration
The frontend implements case management through dedicated pages and components.

### CreateCasePage.tsx
The creation interface provides:
- Interactive map for selecting waypoints
- Timeline UI for managing origin, destination, and middle points
- Form validation and submission handling
- Support for both case creation and editing

```mermaid
sequenceDiagram
participant User
participant CreateCasePage
participant tRPC
participant API
User->>CreateCasePage : Fill form and submit
CreateCasePage->>CreateCasePage : Validate form data
CreateCasePage->>tRPC : mutateAsync(input)
tRPC->>API : POST /trpc/case.updateOrCreate
API-->>tRPC : Return case ID
tRPC-->>CreateCasePage : Success event
CreateCasePage->>User : Show success toast
CreateCasePage->>User : Redirect to case page
```

**Diagram sources**
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx#L300-L350)

**Section sources**
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx#L1-L584)

### CasePage.tsx
The case detail page displays:
- Author information with contact details
- Full route timeline with waypoints
- Interactive map showing the complete route
- Assignment controls for interested users
- Rating functionality for completed cases

## Data Flow Overview
The complete data flow from frontend to database:

```mermaid
flowchart LR
A[Frontend Form] --> B[React State]
B --> C[tRPC Mutation]
C --> D[Zod Validation]
D --> E[Rate Limited Queue]
E --> F[CaseService]
F --> G[Prisma Operations]
G --> H[Database]
H --> G
G --> F
F --> C
C --> B
B --> I[UI Update]
```

**Diagram sources**
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx#L300-L350)
- [case.ts](file://server/src/services/case.ts#L380-L480)

**Section sources**
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx#L300-L350)
- [case.ts](file://server/src/services/case.ts#L380-L480)

## Edge Case Handling
The system implements specific logic for handling edge cases:

### In-Progress or Completed Cases
- The API prevents modification of cases with status other than OPEN
- Assignment is only allowed for OPEN cases
- The frontend hides edit controls for non-author users and non-OPEN cases
- Attempting to modify a non-OPEN case returns "Not authorized" error

### Waypoint Management
- Duplicate waypoints (same coordinates) are prevented in the UI
- Middle points can be dynamically added, edited, and removed
- Geolocation data is preserved even if display names change
- Date constraints prevent past dates for future waypoints

**Section sources**
- [case.ts](file://server/src/services/case.ts#L439-L480)
- [CasePage.tsx](file://frontend/src/pages/CasePage.tsx#L1-L345)

## Error Codes
The API returns standardized error codes for various failure scenarios:

| Error Code | Scenario | Handling Recommendation |
|----------|---------|------------------------|
| NOT_AUTHORIZED | User not authenticated or not case author | Redirect to login or show permission error |
| VALIDATION_FAILED | Input fails Zod validation | Display specific field errors to user |
| RESOURCE_NOT_FOUND | Case ID does not exist | Show 404 page or redirect to search |
| NOT_AUTHORIZED | Attempting to modify non-OPEN case | Inform user case cannot be edited |
| SERVER_ERROR | Internal server error | Log error and retry operation |

The frontend components handle these errors by displaying appropriate toast notifications and guiding users toward resolution.

**Section sources**
- [CreateCasePage.tsx](file://frontend/src/pages/CreateCasePage.tsx#L250-L270)
- [case.ts](file://server/src/services/case.ts#L439-L450)