# Rating Model

<cite>
**Referenced Files in This Document**   
- [UserRating.tsx](file://frontend/src/components/UserRating.tsx)
- [schema.prisma](file://server/prisma/schema.prisma)
- [user.ts](file://server/src/services/user.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Data Model Structure](#data-model-structure)
3. [Composite Key and Constraints](#composite-key-and-constraints)
4. [Relationships with Other Entities](#relationships-with-other-entities)
5. [Rating Validation and Anti-Gaming Measures](#rating-validation-and-anti-gaming-measures)
6. [Reputation Aggregation and UI Display](#reputation-aggregation-and-ui-display)
7. [Rating Moderation and Edit/Delete Policies](#rating-moderation-and-editdelete-policies)
8. [Impact on User Trust Metrics](#impact-on-user-trust-metrics)
9. [Sample Queries](#sample-queries)
10. [Conclusion](#conclusion)

## Introduction
The UserRating entity in takeNpass implements a reputation system that allows users to rate each other after participating in a shared case. This model supports trust building through peer feedback, enables reputation scoring, and contributes to community safety. Ratings are tied to specific cases to ensure context and authenticity, preventing arbitrary or malicious scoring. The system includes safeguards against rating manipulation and provides mechanisms for moderation.

**Section sources**
- [schema.prisma](file://server/prisma/schema.prisma#L100-L120)

## Data Model Structure
The UserRating entity captures peer evaluations within the takeNpass platform. Each rating is associated with a specific case and reflects one user's assessment of another.

| Field | Type | Description |
|-------|------|-------------|
| id | Int | Auto-incrementing unique identifier for the rating |
| rating | Int | Numerical score, typically on a 1-5 scale |
| comment | String? | Optional textual feedback provided by the rater |
| userId | String | ID of the user being rated |
| senderId | String | ID of the user providing the rating |
| caseId | String | ID of the case in which both users participated |
| createdAt | DateTime | Timestamp when the rating was created |
| updatedAt | DateTime | Timestamp of last update to the rating |
| confirm | Boolean? | Indicates whether the rating has been verified or confirmed |

**Section sources**
- [schema.prisma](file://server/prisma/schema.prisma#L100-L120)

## Composite Key and Constraints
The UserRating model enforces a unique constraint on the combination of caseId, userId, and senderId to prevent duplicate ratings. This ensures that a user can only rate another user once per case, maintaining fairness and preventing spam.

```mermaid
erDiagram
USER ||--o{ USERRATING : "receives"
USER ||--o{ USERRATING : "sends"
CASE ||--o{ USERRATING : "contains"
USERRATING {
int id
int rating
string comment
string userId
string senderId
string caseId
datetime createdAt
datetime updatedAt
boolean confirm
}
```

**Diagram sources**
- [schema.prisma](file://server/prisma/schema.prisma#L115)

## Relationships with Other Entities
The UserRating entity maintains relationships with three core entities: User (as both recipient and sender), and Case.

- **User (rated)**: Connected via `userId`, represents the recipient of the rating
- **User (sender)**: Connected via `senderId`, represents the user who submitted the rating
- **Case**: Connected via `caseId`, provides the context in which the rating was given

These relationships enable rich queries such as retrieving all ratings for a user across cases, or viewing feedback from participants in a specific case.

```mermaid
classDiagram
class UserRating {
+Int id
+Int rating
+String? comment
+String userId
+String senderId
+String caseId
+DateTime createdAt
+DateTime updatedAt
+Boolean? confirm
}
class User {
+String id
+String email
+String? username
+UserRating[] ratings
+UserRating[] sendedRatings
}
class Case {
+String id
+UserRating[] UserRating
}
UserRating --> User : "rated user"
UserRating --> User : "sender"
UserRating --> Case : "context"
```

**Diagram sources**
- [schema.prisma](file://server/prisma/schema.prisma#L100-L120)
- [user.ts](file://server/src/services/user.ts#L134-L170)

## Rating Validation and Anti-Gaming Measures
The system implements several validation and anti-abuse mechanisms:

- **Rating Scale Enforcement**: Ratings must be integers, with business logic enforcing a 1-5 scale
- **Single Rating Per Case**: The composite unique index prevents multiple ratings from the same sender to the same recipient within one case
- **Contextual Binding**: Ratings are tied to completed cases, ensuring only actual interactions can be rated
- **Confirmation Flag**: New ratings are marked as unconfirmed (`confirm: false`) until validated, potentially by moderation or system checks

When a rating is created or updated, the `upsert` operation in the service layer ensures idempotency and prevents duplicates.

**Section sources**
- [schema.prisma](file://server/prisma/schema.prisma#L115)
- [user.ts](file://server/src/services/user.ts#L134-L170)

## Reputation Aggregation and UI Display
User reputation scores are calculated by averaging confirmed ratings from peers. The frontend component `UserRating.tsx` renders star-based visualizations using the `@smastrom/react-rating` library.

The average rating is computed client-side when user data is available:
```typescript
const ratingsSum = user.ratings.reduce((acc, v) => acc + (v.rating || 0), 0)
setRating(ratingsSum / user.ratings.length)
```

The UI displays ratings in read-only mode by default, with yellow stars for active ratings and gray for inactive. The component supports dynamic width and can be made interactive when used in rating forms.

```mermaid
flowchart TD
A[Fetch User Data] --> B{Has Ratings?}
B --> |Yes| C[Sum All Ratings]
C --> D[Divide by Count]
D --> E[Display Average as Stars]
B --> |No| F[Show Default Rating]
```

**Diagram sources**
- [UserRating.tsx](file://frontend/src/components/UserRating.tsx#L30-L40)

**Section sources**
- [UserRating.tsx](file://frontend/src/components/UserRating.tsx#L25-L50)

## Rating Moderation and Edit/Delete Policies
Ratings cannot be deleted but can be updated through the `upsert` pattern. Editing is allowed to correct mistakes, but the system logs changes via `updatedAt`. 

Moderation policies include:
- New ratings are unconfirmed by default
- Suspicious patterns (e.g., mass low ratings) may trigger manual review
- Users cannot rate themselves
- Only participants in a case can rate each other

The backend service ensures that only authenticated users can submit ratings, and authorization checks should confirm case participation (though this logic may be enforced at the application level).

**Section sources**
- [user.ts](file://server/src/services/user.ts#L134-L170)

## Impact on User Trust Metrics
User ratings directly influence trust metrics within takeNpass:
- High average ratings improve visibility in search results
- Users with consistently low ratings may face restrictions
- The number of received ratings serves as a credibility signal
- Recent negative feedback triggers notifications to moderators

Trust is contextual: ratings are case-specific, so a user's reliability in one type of interaction doesn't automatically transfer to others. This granular approach prevents reputation inflation while still enabling meaningful peer assessment.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L134-L170)
- [schema.prisma](file://server/prisma/schema.prisma#L100-L120)

## Sample Queries
### Calculate Average Rating for a User
```sql
SELECT AVG(rating) as average_rating, COUNT(*) as total_ratings
FROM UserRating
WHERE userId = 'user-123' 
  AND confirm = true
GROUP BY userId;
```

### Retrieve Recent Feedback for a User
```sql
SELECT ur.rating, ur.comment, ur.createdAt, 
       u.username as senderName, 
       c.id as caseId, c.createdAt as caseDate
FROM UserRating ur
JOIN User u ON ur.senderId = u.id
JOIN Case c ON ur.caseId = c.id
WHERE ur.userId = 'user-123'
  AND ur.confirm = true
ORDER BY ur.createdAt DESC
LIMIT 10;
```

### Check if User Can Rate Another in a Case
```sql
SELECT COUNT(*) as existing_ratings
FROM UserRating
WHERE caseId = 'case-456' 
  AND userId = 'user-789'
  AND senderId = 'current-user';
-- If count = 0, rating is allowed
```

**Section sources**
- [user.ts](file://server/src/services/user.ts#L134-L170)
- [schema.prisma](file://server/prisma/schema.prisma#L100-L120)

## Conclusion
The UserRating model in takeNpass forms the foundation of a robust reputation system that balances user feedback with anti-abuse measures. By tying ratings to specific cases and enforcing one-rating-per-participant constraints, the system ensures meaningful and contextually relevant feedback. The integration between frontend display components and backend validation logic creates a seamless experience for building trust within the community. Future enhancements could include rating categories, detailed feedback dimensions, or machine learning-based anomaly detection.