import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { StringFieldUpdateOperationsInputSchema } from './StringFieldUpdateOperationsInputSchema';
import { NullableStringFieldUpdateOperationsInputSchema } from './NullableStringFieldUpdateOperationsInputSchema';
import { BoolFieldUpdateOperationsInputSchema } from './BoolFieldUpdateOperationsInputSchema';
import { DateTimeFieldUpdateOperationsInputSchema } from './DateTimeFieldUpdateOperationsInputSchema';
import { RolesSchema } from './RolesSchema';
import { EnumRolesFieldUpdateOperationsInputSchema } from './EnumRolesFieldUpdateOperationsInputSchema';
import { CaseUpdateManyWithoutAuthorNestedInputSchema } from './CaseUpdateManyWithoutAuthorNestedInputSchema';
import { FavoriteUpdateManyWithoutUserNestedInputSchema } from './FavoriteUpdateManyWithoutUserNestedInputSchema';
import { AccountUpdateManyWithoutUserNestedInputSchema } from './AccountUpdateManyWithoutUserNestedInputSchema';
import { MessageUpdateManyWithoutReceiverNestedInputSchema } from './MessageUpdateManyWithoutReceiverNestedInputSchema';
import { MessageUpdateManyWithoutSenderNestedInputSchema } from './MessageUpdateManyWithoutSenderNestedInputSchema';
import { NotificationUpdateManyWithoutSenderNestedInputSchema } from './NotificationUpdateManyWithoutSenderNestedInputSchema';
import { NotificationUpdateManyWithoutUserNestedInputSchema } from './NotificationUpdateManyWithoutUserNestedInputSchema';
import { SessionUpdateManyWithoutUserNestedInputSchema } from './SessionUpdateManyWithoutUserNestedInputSchema';
import { UserAuthenticationLogUpdateManyWithoutUserNestedInputSchema } from './UserAuthenticationLogUpdateManyWithoutUserNestedInputSchema';
import { UserAvatarUpdateOneWithoutUserNestedInputSchema } from './UserAvatarUpdateOneWithoutUserNestedInputSchema';
import { UserRatingUpdateManyWithoutSenderNestedInputSchema } from './UserRatingUpdateManyWithoutSenderNestedInputSchema';
import { UserRatingUpdateManyWithoutUserNestedInputSchema } from './UserRatingUpdateManyWithoutUserNestedInputSchema';
import { CaseUpdateManyWithoutClientsNestedInputSchema } from './CaseUpdateManyWithoutClientsNestedInputSchema';

export const UserUpdateWithoutNotificationSettingsInputSchema: z.ZodType<Prisma.UserUpdateWithoutNotificationSettingsInput> = z.object({
  id: z.union([ z.string().uuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  username: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  email: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  emailVerified: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  image: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  name: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  about_me: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  phone: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  blocked: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  confirmed: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  role: z.union([ z.lazy(() => RolesSchema),z.lazy(() => EnumRolesFieldUpdateOperationsInputSchema) ]).optional(),
  language: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  authoredCases: z.lazy(() => CaseUpdateManyWithoutAuthorNestedInputSchema).optional(),
  favorites: z.lazy(() => FavoriteUpdateManyWithoutUserNestedInputSchema).optional(),
  accounts: z.lazy(() => AccountUpdateManyWithoutUserNestedInputSchema).optional(),
  receivedMessages: z.lazy(() => MessageUpdateManyWithoutReceiverNestedInputSchema).optional(),
  sentMessages: z.lazy(() => MessageUpdateManyWithoutSenderNestedInputSchema).optional(),
  sendedNotifications: z.lazy(() => NotificationUpdateManyWithoutSenderNestedInputSchema).optional(),
  notifications: z.lazy(() => NotificationUpdateManyWithoutUserNestedInputSchema).optional(),
  auth_session: z.lazy(() => SessionUpdateManyWithoutUserNestedInputSchema).optional(),
  userAuthenticationLog: z.lazy(() => UserAuthenticationLogUpdateManyWithoutUserNestedInputSchema).optional(),
  avatar: z.lazy(() => UserAvatarUpdateOneWithoutUserNestedInputSchema).optional(),
  sendedRatings: z.lazy(() => UserRatingUpdateManyWithoutSenderNestedInputSchema).optional(),
  ratings: z.lazy(() => UserRatingUpdateManyWithoutUserNestedInputSchema).optional(),
  cases: z.lazy(() => CaseUpdateManyWithoutClientsNestedInputSchema).optional()
}).strict();

export default UserUpdateWithoutNotificationSettingsInputSchema;
