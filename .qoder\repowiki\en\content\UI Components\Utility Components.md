# Utility Components

<cite>
**Referenced Files in This Document**   
- [CaseCard.tsx](file://frontend/src/components/CaseCard.tsx)
- [CaseCardSkeleton.tsx](file://frontend/src/components/CaseCardSkeleton.tsx)
- [FavoriteButton.tsx](file://frontend/src/components/FavoriteButton.tsx)
- [MessagesWidget.tsx](file://frontend/src/components/MessagesWidget.tsx)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx)
- [Timeline.tsx](file://frontend/src/components/Timeline.tsx)
- [Upload.tsx](file://frontend/src/components/Upload.tsx)
- [WebSocketStatus.tsx](file://frontend/src/components/WebSocketStatus.tsx)
- [IndexPage.tsx](file://frontend/src/pages/IndexPage.tsx)
- [MyCases.tsx](file://frontend/src/pages/MyCases.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [CaseCard and CaseCardSkeleton](#casecard-and-casecardskeleton)
3. [FavoriteButton](#favoritebutton)
4. [MessagesWidget and Notifications](#messageswidget-and-notifications)
5. [Timeline](#timeline)
6. [Upload](#upload)
7. [WebSocketStatus](#websocketstatus)
8. [Usage Examples](#usage-examples)
9. [Performance and Accessibility](#performance-and-accessibility)
10. [State Management and Event Handling](#state-management-and-event-handling)
11. [Conclusion](#conclusion)

## Introduction
This document provides comprehensive documentation for the utility and reusable components in the takeNpass application. These components are designed to enhance user experience through consistent UI patterns, efficient state management, and responsive interactions. The documentation covers core components such as CaseCard, FavoriteButton, MessagesWidget, Notifications, Timeline, Upload, and WebSocketStatus, detailing their functionality, usage patterns, and integration within key pages like IndexPage and MyCases.

**Section sources**
- [CaseCard.tsx](file://frontend/src/components/CaseCard.tsx)
- [FavoriteButton.tsx](file://frontend/src/components/FavoriteButton.tsx)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx)

## CaseCard and CaseCardSkeleton

The `CaseCard` component displays delivery case information in a structured format, showing origin, destination, intermediate points, author details, and status. It supports optional features like favorite button integration and status indicators. The component calculates and displays distance information between points when geographical metadata is available.

The `CaseCardSkeleton` provides a loading state visualization with animated placeholders that mimic the layout of the actual CaseCard. It accepts a `qty` prop to render multiple skeleton instances, useful during data fetching operations.

```mermaid
flowchart TD
A[CaseCard] --> B[Display Author Info]
A --> C[Render Address Lines]
A --> D[Show Status Chip]
A --> E[Include FavoriteButton]
F[CaseCardSkeleton] --> G[Show Avatar Placeholder]
F --> H[Render Input Skeletons]
F --> I[Display Address Line Placeholders]
```

**Diagram sources**
- [CaseCard.tsx](file://frontend/src/components/CaseCard.tsx#L1-L108)
- [CaseCardSkeleton.tsx](file://frontend/src/components/CaseCardSkeleton.tsx#L1-L25)

**Section sources**
- [CaseCard.tsx](file://frontend/src/components/CaseCard.tsx#L1-L108)
- [CaseCardSkeleton.tsx](file://frontend/src/components/CaseCardSkeleton.tsx#L1-L25)

## FavoriteButton

The `FavoriteButton` component enables users to bookmark delivery cases with visual feedback. It uses heart icons (filled/empty) to indicate favorite status and provides smooth animations on hover and click interactions using Framer Motion. The component manages local state for immediate UI feedback while asynchronously updating the server via TRPC mutation.

When clicked, it toggles the favorite state locally and triggers a mutation to update the backend. During the mutation process, a spinner is displayed to indicate loading state. The component integrates with the global user store to determine the current user's identity for proper favorite tracking.

```mermaid
sequenceDiagram
participant User
participant FavoriteButton
participant TRPC
participant Backend
User->>FavoriteButton : Click heart icon
FavoriteButton->>FavoriteButton : Toggle local state
FavoriteButton->>TRPC : mutateAsync(caseId)
TRPC->>Backend : Send favorite update
Backend-->>TRPC : Confirmation
TRPC-->>FavoriteButton : Success response
FavoriteButton->>User : Visual feedback (animation)
```

**Diagram sources**
- [FavoriteButton.tsx](file://frontend/src/components/FavoriteButton.tsx#L1-L57)

**Section sources**
- [FavoriteButton.tsx](file://frontend/src/components/FavoriteButton.tsx#L1-L57)

## MessagesWidget and Notifications

The `MessagesWidget` displays an unread message count badge on a message icon button. When clicked, it navigates to the messages page. It uses TRPC to fetch unread message counts and integrates with the router for navigation.

The `Notifications` component provides a comprehensive notification system with real-time updates via WebSocket. It displays unread counts, supports filtering between read/unread notifications, and handles various notification types with specific templates. The component subscribes to 'xnotification' events and shows toast notifications for new messages while updating the notification list.

```mermaid
flowchart TD
A[MessagesWidget] --> B[Fetch unread count]
B --> C[Display badge]
C --> D[Handle click]
D --> E[Navigate to messages]
F[Notifications] --> G[WebSocket subscription]
G --> H[Receive xnotification]
H --> I[Update list]
I --> J[Show toast]
J --> K[Render templates]
```

**Diagram sources**
- [MessagesWidget.tsx](file://frontend/src/components/MessagesWidget.tsx#L1-L34)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L1-L323)

**Section sources**
- [MessagesWidget.tsx](file://frontend/src/components/MessagesWidget.tsx#L1-L34)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L1-L323)

## Timeline

The `Timeline` component visualizes case status progression with a vertical timeline layout. It accepts an array of `TimeLineItem` objects containing timestamp labels, headers, bodies, and optional custom point icons. Each item is rendered with a circular indicator and can display either string or component-based time labels using Chip elements.

The component uses a left border to create the timeline track and positions point icons absolutely. It supports custom icons through the `pointIcon` property or falls back to a default circular indicator. The layout is responsive and maintains clear visual hierarchy for status progression.

```mermaid
flowchart TD
A[Timeline] --> B[Map items]
B --> C[Render point icon]
C --> D[Display time label]
D --> E[Show header if present]
E --> F[Render body content]
```

**Diagram sources**
- [Timeline.tsx](file://frontend/src/components/Timeline.tsx#L1-L41)

**Section sources**
- [Timeline.tsx](file://frontend/src/components/Timeline.tsx#L1-L41)

## Upload

The `Upload` component facilitates file uploads with base64 encoding. It provides both a default cloud upload button and support for custom activator components. When a file is selected, it reads the file as a data URL and passes the base64 string to callback functions (`setValue` or `onChangeBase64`).

The component uses a hidden file input controlled by a ref, with click events delegated from the visible activator. It supports custom activators through the `Activator` prop, allowing flexible integration into different UI contexts. The current implementation only processes the first selected file.

```mermaid
flowchart TD
A[Upload] --> B[Handle click]
B --> C[Trigger file input]
C --> D[Process file selection]
D --> E[Read as base64]
E --> F[Call callback functions]
```

**Diagram sources**
- [Upload.tsx](file://frontend/src/components/Upload.tsx#L1-L73)

**Section sources**
- [Upload.tsx](file://frontend/src/components/Upload.tsx#L1-L73)

## WebSocketStatus

The `WebSocketStatus` component provides visual feedback about the WebSocket connection state. When connected, it displays a green connection icon. When disconnected, it shows a pulsing red dot animation to indicate connection loss.

This component is designed for status monitoring in real-time features like messaging and notifications. It accepts an `isConnected` boolean prop to determine the display state and uses simple SVG icons from the component library for visual representation.

```mermaid
flowchart TD
A[WebSocketStatus] --> B{isConnected?}
B --> |true| C[Show green icon]
B --> |false| D[Show pulsing red dot]
```

**Diagram sources**
- [WebSocketStatus.tsx](file://frontend/src/components/WebSocketStatus.tsx#L1-L11)

**Section sources**
- [WebSocketStatus.tsx](file://frontend/src/components/WebSocketStatus.tsx#L1-L11)

## Usage Examples

The `IndexPage` demonstrates comprehensive usage of utility components in a production context. It integrates `CaseCard` with `CaseCardSkeleton` for loading states, uses `FavoriteButton` within case listings, and incorporates `MessagesWidget` and `Notifications` in the navigation area.

The page implements responsive design patterns, showing a floating action button with filters on small devices and a full-width search bar on larger screens. It manages complex state for location selection, date ranges, and pagination while maintaining URL synchronization through search parameters.

```mermaid
flowchart TD
A[IndexPage] --> B[Search functionality]
B --> C[Location selection]
C --> D[Date range picker]
D --> E[Case listing]
E --> F[CaseCard with favorites]
F --> G[Map integration]
G --> H[Real-time updates]
```

**Section sources**
- [IndexPage.tsx](file://frontend/src/pages/IndexPage.tsx#L1-L562)
- [MyCases.tsx](file://frontend/src/pages/MyCases.tsx)

## Performance and Accessibility

The components implement several performance optimizations including memoization patterns, efficient state updates, and conditional rendering. The `CaseCard` component calculates distances only when geographical data is available, preventing unnecessary computations.

Accessibility features include proper ARIA labels on interactive elements, keyboard navigation support, and semantic HTML structure. The components use relative units and responsive layouts to ensure usability across device sizes. Loading states are clearly communicated through skeleton screens and spinner indicators.

For rendering multiple cards, the application uses pagination to limit the number of simultaneously rendered components, improving initial load performance and scroll responsiveness.

**Section sources**
- [CaseCard.tsx](file://frontend/src/components/CaseCard.tsx#L1-L108)
- [IndexPage.tsx](file://frontend/src/pages/IndexPage.tsx#L1-L562)

## State Management and Event Handling

The utility components follow consistent state management patterns using React hooks. Local component state is managed with `useState`, while global state (user data) is accessed through the `useUserStore` hook. Side effects and data synchronization are handled with `useEffect`.

Event handling is implemented with proper delegation and prevention of default behaviors when necessary. The components integrate with the application's TRPC system for data mutations and queries, ensuring type-safe API interactions. WebSocket events are properly cleaned up in useEffect cleanup functions to prevent memory leaks.

The notification system demonstrates sophisticated event handling with WebSocket subscriptions that are established when the user is authenticated and cleaned up on unmount.

**Section sources**
- [FavoriteButton.tsx](file://frontend/src/components/FavoriteButton.tsx#L1-L57)
- [Notifications.tsx](file://frontend/src/components/Notifications.tsx#L1-L323)
- [MessagesWidget.tsx](file://frontend/src/components/MessagesWidget.tsx#L1-L34)

## Conclusion

The utility components in takeNpass provide a robust foundation for the application's user interface, offering reusable, accessible, and performant building blocks. They demonstrate effective patterns for state management, real-time updates, and responsive design that can be consistently applied across the application. The components are well-integrated with the application's data layer and provide clear visual feedback for user interactions.