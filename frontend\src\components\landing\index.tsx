import { <PERSON><PERSON>, <PERSON><PERSON> } from '@heroui/react'
import { MapPin, Star, Globe, Truck, ArrowRight, Zap, Target, Sparkles, Brain, Rocket, Users, Lock } from 'lucide-react'
import { Cover } from '../ui/cover'
import { TextGenerateEffect } from '../ui/text-generate-effect'
import { WorldMapWrapper } from './WorldMapWrapper'
import { FlickeringGrid } from '../magicui/flickering-grid'
import { BentoGrid, BentoGridItem } from '../ui/bento-grid'
import { useMemo } from 'react'
import './landing.css'

export const LandingPage = () => {

  // const memoizedWorldMap = useMemo(() => <WorldMapWrapper />, [])

  return (
    <div className='min-h-screen bg-background overflow-hidden'>
      <h1 className='text-4xl md:text-4xl lg:text-6xl font-semibold max-w-7xl mx-auto text-center mt-6 relative z-20 py-6 bg-clip-text text-transparent bg-gradient-to-b from-neutral-800 via-neutral-700 to-neutral-700 dark:from-neutral-800 dark:via-white dark:to-white'>
        <TextGenerateEffect duration={1} words='Встречайте доставку в 5 раз' /> <Cover>быстрее</Cover>
      </h1>

      {/* {memoizedWorldMap} */}

      <section className='relative min-h-screen flex items-center overflow-hidden pt-20'>
        <div>
          {/* <FlickeringGrid
            className='absolute w-full h-full'
            squareSize={4}
            gridGap={6}
            color='#6B7280'
            maxOpacity={0.5}
            flickerChance={0.1}
            // height={1024}
            // width={800}
          /> */}
        </div>
        {/* Animated background elements */}
        <div className='absolute inset-0 bg-gradient-to-br from-background via-primary/5 to-secondary/5'></div>
        <div className='absolute top-20 right-10 w-72 h-72 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full blur-3xl floating'></div>
        <div
          className='absolute bottom-20 left-10 w-96 h-96 bg-gradient-to-tr from-secondary/15 to-accent/15 rounded-full blur-3xl floating'
          style={{ animationDelay: '-3s' }}
        ></div>

        <div className='container mx-auto px-6 relative z-10'>
          <div className='grid lg:grid-cols-2 gap-16 items-center'>
            {/* Left side - Brutalist typography */}
            <div className='space-y-8'>
              <div className='space-y-6'>
                <h1 className='text-6xl lg:text-8xl font-black leading-none text-balance'>
                  <span className='block text-foreground'>TAKE</span>
                  <span className='block gradient-text'>N'PASS</span>
                </h1>

                <div className='relative'>
                  <div className='absolute -left-4 top-0 w-1 h-full bg-gradient-to-b from-secondary to-accent'></div>
                  <p className='text-xl lg:text-2xl text-muted-foreground leading-relaxed pl-8 font-medium'>
                    Революционная платформа краудшиппинга.
                    <span className='text-primary font-bold'> Доставляй быстрее.</span>
                    <span className='text-primary font-bold'> Зарабатывай больше.</span>
                  </p>
                </div>
              </div>

              <div className='flex flex-col sm:flex-row gap-6'>
                <Button
                  size='lg'
                  className='bg-primary hover:bg-primary/90 text-primary-foreground font-black px-8 py-4 text-lg shadow-lg hover:shadow-xl hover:translate-x-2 hover:translate-y-2 transition-all duration-300 group'
                >
                  <Target className='w-6 h-6 mr-3 group-hover:rotate-90 transition-transform duration-300' />
                  НАЙТИ ДОСТАВКУ
                </Button>
                <Button
                  size='lg'
                  variant='bordered'
                  className='glass-card border-2 border-secondary text-primary hover:bg-secondary hover:text-secondary-foreground font-bold px-8 py-4 text-lg group bg-transparent'
                >
                  <Zap className='w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-300' />
                  СТАТЬ КУРЬЕРОМ
                </Button>
              </div>
            </div>

            {/* Right side - 3D geometric shapes */}
            <div className='relative lg:block hidden'>
              <div className='relative w-full h-96'>
                {/* Floating geometric elements */}
                <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary to-secondary rounded-3xl brutalist-shadow floating transform rotate-12'></div>
                <div
                  className='absolute top-20 left-10 w-24 h-24 bg-gradient-to-br from-secondary to-accent rounded-2xl brutalist-shadow floating transform -rotate-12'
                  style={{ animationDelay: '-2s' }}
                ></div>
                <div className='absolute bottom-10 right-20 w-40 h-40 glass-card rounded-3xl brutalist-shadow floating transform rotate-6' style={{ animationDelay: '-4s' }}></div>
                <div
                  className='absolute bottom-0 left-0 w-28 h-28 bg-gradient-to-br from-accent to-primary rounded-full brutalist-shadow floating'
                  style={{ animationDelay: '-1s' }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id='features' className='py-32 relative overflow-hidden'>
        <div className='absolute inset-0 bg-gradient-to-br from-background via-primary/3 to-secondary/3'></div>

        {/* Floating background elements with glass effect */}
        <div className='absolute top-20 left-10 w-64 h-64 glass-card-primary rounded-full blur-2xl floating'></div>
        <div className='absolute bottom-20 right-10 w-80 h-80 glass-card-light rounded-full blur-2xl floating' style={{ animationDelay: '-2s' }}></div>

        <div className='container mx-auto px-6 relative z-10'>
          <div className='text-center mb-20'>
            <div className='inline-flex items-center gap-2 glass-card-light rounded-full px-6 py-2 mb-6 border border-primary/20'>
              <Sparkles className='w-5 h-5 text-primary' />
              <span className='text-sm font-bold text-primary'>РЕВОЛЮЦИОННЫЕ ВОЗМОЖНОСТИ</span>
            </div>
            <h2 className='text-5xl lg:text-7xl font-black mb-6 text-balance leading-tight glass-text'>
              ПОЧЕМУ <span className='gradient-text-contrast' data-text='TAKENPASS'>TAKENPASS</span>?
            </h2>
            <div className='w-32 h-2 glass-card-primary mx-auto mb-8 rounded-full border border-primary/30'></div>
            <p className='text-xl lg:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-medium'>
              Передовые технологии встречают человеческие потребности.
              <span className='text-primary font-bold'> Будущее доставки уже здесь.</span>
            </p>
          </div>

          <BentoGrid className='max-w-7xl mx-auto stagger-children'>
            {/* Main feature - AI Intelligence */}
            <BentoGridItem
              className='md:col-span-2 md:row-span-2 glass-card-light border-primary/20 hover:border-primary/40 transition-all duration-500 group overflow-hidden relative hover-lift'
              title={
                <div className='flex items-center gap-3'>
                  <div className='w-12 h-12 glass-card-primary rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 border border-primary/30'>
                    <Brain className='w-6 h-6 text-primary' />
                  </div>
                  <span className='text-2xl font-black text-foreground'>AI ИНТЕЛЛЕКТ</span>
                </div>
              }
              description={
                <div className='space-y-4'>
                  <p className='text-base text-muted-foreground leading-relaxed'>
                    Машинное обучение анализирует миллионы маршрутов для оптимальной доставки.
                    Предсказание времени с точностью 95%.
                  </p>
                  <div className='flex flex-wrap gap-2'>
                    <Badge className='glass-card-primary text-primary border-primary/30'>Предиктивная аналитика</Badge>
                    <Badge className='glass-card-light text-secondary border-secondary/30'>Оптимизация маршрутов</Badge>
                    <Badge className='glass-card-dark text-accent border-accent/30'>Умные рекомендации</Badge>
                  </div>
                </div>
              }
              header={
                <div className='absolute inset-0 glass-card-primary opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl'></div>
              }
            />

            {/* Real-time tracking */}
            <BentoGridItem
              className='glass-card-light border-secondary/20 hover:border-secondary/40 transition-all duration-500 group hover-lift magnetic'
              title={
                <div className='flex items-center gap-2'>
                  <MapPin className='w-5 h-5 text-secondary' />
                  <span className='font-bold text-foreground'>LIVE ТРЕКИНГ</span>
                </div>
              }
              description='GPS в реальном времени с точностью до метра'
              icon={<div className='w-8 h-8 glass-card-light rounded-lg flex items-center justify-center border border-secondary/20'><MapPin className='w-4 h-4 text-secondary' /></div>}
            />

            {/* Blockchain security */}
            <BentoGridItem
              className='glass-card-dark border-accent/20 hover:border-accent/40 transition-all duration-500 group hover-lift magnetic'
              title={
                <div className='flex items-center gap-2'>
                  <Lock className='w-5 h-5 text-accent' />
                  <span className='font-bold text-foreground'>БЛОКЧЕЙН</span>
                </div>
              }
              description='Неизменяемые записи и криптографическая защита'
              icon={<div className='w-8 h-8 glass-card-dark rounded-lg flex items-center justify-center border border-accent/20'><Lock className='w-4 h-4 text-accent' /></div>}
            />

            {/* Global network */}
            <BentoGridItem
              className='glass-card-primary border-primary/20 hover:border-primary/40 transition-all duration-500 group hover-lift magnetic'
              title={
                <div className='flex items-center gap-2'>
                  <Globe className='w-5 h-5 text-primary' />
                  <span className='font-bold text-foreground'>ГЛОБАЛЬНАЯ СЕТЬ</span>
                </div>
              }
              description='180+ стран, 2.5M+ активных пользователей'
              icon={<div className='w-8 h-8 glass-card-primary rounded-lg flex items-center justify-center border border-primary/20'><Globe className='w-4 h-4 text-primary' /></div>}
            />

            {/* Community power */}
            <BentoGridItem
              className='glass-card-light border-secondary/20 hover:border-secondary/40 transition-all duration-500 group hover-lift magnetic'
              title={
                <div className='flex items-center gap-2'>
                  <Users className='w-5 h-5 text-secondary' />
                  <span className='font-bold text-foreground'>СООБЩЕСТВО</span>
                </div>
              }
              description='Проверенные курьеры с рейтингом и отзывами'
              icon={<div className='w-8 h-8 glass-card-light rounded-lg flex items-center justify-center border border-secondary/20'><Users className='w-4 h-4 text-secondary' /></div>}
            />

            {/* Speed optimization */}
            <BentoGridItem
              className='md:col-span-2 glass-card-light border-accent/20 hover:border-accent/40 transition-all duration-500 group relative overflow-hidden hover-lift'
              title={
                <div className='flex items-center gap-3'>
                  <div className='w-10 h-10 glass-card-primary rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 border border-accent/30'>
                    <Rocket className='w-5 h-5 text-accent' />
                  </div>
                  <span className='text-xl font-black text-foreground'>СКОРОСТЬ СВЕТА</span>
                </div>
              }
              description={
                <div className='space-y-3'>
                  <p className='text-muted-foreground'>
                    Доставка в 5 раз быстрее традиционных служб благодаря краудшиппингу
                  </p>
                  <div className='grid grid-cols-3 gap-4 text-center'>
                    <div>
                      <div className='text-2xl font-black text-accent'>3x</div>
                      <div className='text-xs text-muted-foreground'>Быстрее</div>
                    </div>
                    <div>
                      <div className='text-2xl font-black text-secondary'>50%</div>
                      <div className='text-xs text-muted-foreground'>Дешевле</div>
                    </div>
                    <div>
                      <div className='text-2xl font-black text-primary'>99%</div>
                      <div className='text-xs text-muted-foreground'>Надежность</div>
                    </div>
                  </div>
                </div>
              }
              header={
                <div className='absolute top-0 right-0 w-32 h-32 glass-card-dark rounded-bl-full border-l border-b border-accent/20'></div>
              }
            />
          </BentoGrid>
        </div>
      </section>

      <section id='process' className='py-32 relative overflow-hidden'>
        <div className='absolute inset-0 bg-gradient-to-br from-background via-primary/5 to-secondary/5'></div>

        {/* Animated background patterns */}
        <div className='absolute inset-0 opacity-30'>
          <div className='absolute top-1/4 left-1/4 w-2 h-2 bg-primary rounded-full animate-ping'></div>
          <div className='absolute top-3/4 right-1/4 w-2 h-2 bg-secondary rounded-full animate-ping' style={{ animationDelay: '1s' }}></div>
          <div className='absolute top-1/2 left-3/4 w-2 h-2 bg-accent rounded-full animate-ping' style={{ animationDelay: '2s' }}></div>
        </div>

        <div className='container mx-auto px-6 relative z-10'>
          <div className='text-center mb-24'>
            <div className='inline-flex items-center gap-2 glass-card-light rounded-full px-6 py-2 mb-6 border border-secondary/20'>
              <Zap className='w-5 h-5 text-secondary' />
              <span className='text-sm font-bold text-secondary'>ПРОСТОЙ ПРОЦЕСС</span>
            </div>
            <h2 className='text-5xl lg:text-7xl font-black mb-6 leading-tight glass-text'>
              КАК ЭТО <span className='gradient-text-contrast' data-text='РАБОТАЕТ'>РАБОТАЕТ</span>
            </h2>
            <div className='w-40 h-2 glass-card-primary mx-auto mb-8 rounded-full border border-secondary/30'></div>
            <p className='text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed'>
              Три простых шага к революционной доставке
            </p>
          </div>

          <div className='grid lg:grid-cols-2 gap-16 lg:gap-24'>
            {/* Senders */}
            <div className='space-y-8'>
              <div className='text-center lg:text-left mb-12'>
                <div className='inline-flex items-center gap-4 glass-card-light rounded-2xl p-6 mb-6 border border-primary/20'>
                  <div className='w-16 h-16 glass-card-primary rounded-2xl flex items-center justify-center shadow-lg border border-primary/30'>
                    <span className='text-3xl'>📦</span>
                  </div>
                  <div>
                    <h3 className='text-3xl lg:text-4xl font-black text-foreground'>ОТПРАВИТЕЛИ</h3>
                    <p className='text-muted-foreground'>Отправь быстро и надежно</p>
                  </div>
                </div>
              </div>

              <div className='space-y-6'>
                {[
                  {
                    num: '01',
                    title: 'СОЗДАЙ ЗАЯВКУ',
                    desc: 'AI подберет оптимальный маршрут и рассчитает стоимость',
                    icon: Target,
                    color: 'from-primary to-secondary'
                  },
                  {
                    num: '02',
                    title: 'ВЫБЕРИ КУРЬЕРА',
                    desc: 'Проверенные путешественники с рейтингом и отзывами',
                    icon: Users,
                    color: 'from-secondary to-accent'
                  },
                  {
                    num: '03',
                    title: 'ОТСЛЕЖИВАЙ',
                    desc: 'Real-time GPS трекинг + blockchain верификация',
                    icon: MapPin,
                    color: 'from-accent to-primary'
                  }
                ].map((step, i) => (
                  <div key={i} className='group relative'>
                    <div className='flex items-start space-x-6 glass-card-light rounded-2xl p-6 border border-primary/10 hover:border-primary/30 transition-all duration-500 hover:shadow-xl hover-lift'>
                      <div className={`w-16 h-16 glass-card-primary rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 border border-primary/30`}>
                        <span className='text-primary font-black text-lg'>{step.num}</span>
                      </div>
                      <div className='flex-1'>
                        <div className='flex items-center gap-3 mb-3'>
                          <step.icon className='w-5 h-5 text-primary' />
                          <h4 className='text-xl font-black text-foreground'>{step.title}</h4>
                        </div>
                        <p className='text-muted-foreground leading-relaxed'>{step.desc}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Carriers */}
            <div className='space-y-8'>
              <div className='text-center lg:text-left mb-12'>
                <div className='inline-flex items-center gap-4 glass-card-dark rounded-2xl p-6 mb-6 border border-accent/20'>
                  <div className='w-16 h-16 glass-card-primary rounded-2xl flex items-center justify-center shadow-lg border border-accent/30'>
                    <span className='text-3xl'>✈️</span>
                  </div>
                  <div>
                    <h3 className='text-3xl lg:text-4xl font-black text-foreground'>КУРЬЕРЫ</h3>
                    <p className='text-muted-foreground'>Зарабатывай в путешествиях</p>
                  </div>
                </div>
              </div>

              <div className='space-y-6'>
                {[
                  {
                    num: '01',
                    title: 'УКАЖИ МАРШРУТ',
                    desc: 'Автоматическое сопоставление с заявками по твоему пути',
                    icon: MapPin,
                    color: 'from-accent to-primary'
                  },
                  {
                    num: '02',
                    title: 'НАЙДИ ЗАКАЗЫ',
                    desc: 'AI рекомендации по доходности и удобству доставки',
                    icon: Brain,
                    color: 'from-primary to-secondary'
                  },
                  {
                    num: '03',
                    title: 'ЗАРАБОТАЙ',
                    desc: 'Мгновенные выплаты + бонусы за высокий рейтинг',
                    icon: Rocket,
                    color: 'from-secondary to-accent'
                  }
                ].map((step, i) => (
                  <div key={i} className='group relative'>
                    <div className='flex items-start space-x-6 glass-card-dark rounded-2xl p-6 border border-accent/10 hover:border-accent/30 transition-all duration-500 hover:shadow-xl hover-lift'>
                      <div className={`w-16 h-16 glass-card-primary rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 border border-accent/30`}>
                        <span className='text-accent font-black text-lg'>{step.num}</span>
                      </div>
                      <div className='flex-1'>
                        <div className='flex items-center gap-3 mb-3'>
                          <step.icon className='w-5 h-5 text-accent' />
                          <h4 className='text-xl font-black text-foreground'>{step.title}</h4>
                        </div>
                        <p className='text-muted-foreground leading-relaxed'>{step.desc}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id='community' className='py-32 relative overflow-hidden'>
        <div className='absolute inset-0 bg-gradient-to-br from-secondary/5 via-accent/5 to-primary/5'></div>

        {/* Floating elements */}
        <div className='absolute top-10 right-10 w-32 h-32 bg-gradient-to-br from-secondary/20 to-accent/20 rounded-full blur-2xl animate-pulse'></div>
        <div className='absolute bottom-10 left-10 w-40 h-40 bg-gradient-to-tr from-accent/20 to-primary/20 rounded-full blur-2xl animate-pulse' style={{ animationDelay: '-1s' }}></div>

        <div className='container mx-auto px-6 relative z-10'>
          <div className='text-center mb-24'>
            <div className='inline-flex items-center gap-2 glass-card-dark rounded-full px-6 py-2 mb-6 border border-accent/20'>
              <Users className='w-5 h-5 text-accent' />
              <span className='text-sm font-bold text-accent'>ГЛОБАЛЬНОЕ СООБЩЕСТВО</span>
            </div>
            <h2 className='text-5xl lg:text-7xl font-black mb-6 leading-tight glass-text'>
              НАШЕ <span className='gradient-text-contrast' data-text='СООБЩЕСТВО'>СООБЩЕСТВО</span>
            </h2>
            <div className='w-36 h-2 glass-card-primary mx-auto mb-8 rounded-full border border-accent/30'></div>
            <p className='text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed'>
              Миллионы людей по всему миру уже доверяют TakeNPass
            </p>
          </div>

          {/* Statistics */}
          <div className='grid md:grid-cols-3 gap-8 mb-24'>
            {[
              {
                value: '2.5M+',
                label: 'АКТИВНЫХ ПОЛЬЗОВАТЕЛЕЙ',
                color: 'from-primary to-secondary',
                icon: Users,
                description: 'Растем на 15% каждый месяц'
              },
              {
                value: '15M+',
                label: 'УСПЕШНЫХ ДОСТАВОК',
                color: 'from-secondary to-accent',
                icon: Truck,
                description: '99.8% успешных доставок'
              },
              {
                value: '180+',
                label: 'СТРАН И ГОРОДОВ',
                color: 'from-accent to-primary',
                icon: Globe,
                description: 'Покрытие всех континентов'
              }
            ].map((stat, i) => (
              <div key={i} className='group relative'>
                <div className='glass-card-light rounded-3xl p-8 text-center border border-primary/10 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl group-hover:scale-105 hover-lift'>
                  <div className={`w-16 h-16 glass-card-primary rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:rotate-12 transition-transform duration-300 border border-primary/30`}>
                    <stat.icon className='w-8 h-8 text-primary' />
                  </div>
                  <div className='text-5xl lg:text-6xl font-black bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-4'>
                    {stat.value}
                  </div>
                  <div className='text-foreground font-bold text-lg mb-2'>{stat.label}</div>
                  <div className='text-muted-foreground text-sm'>{stat.description}</div>
                </div>
              </div>
            ))}
          </div>

          {/* Testimonials */}
          <div className='text-center mb-12'>
            <h3 className='text-3xl font-black text-foreground mb-4'>ЧТО ГОВОРЯТ НАШИ ПОЛЬЗОВАТЕЛИ</h3>
            <div className='w-24 h-1 bg-gradient-to-r from-secondary to-accent mx-auto'></div>
          </div>

          <div className='grid md:grid-cols-3 gap-8'>
            {[
              {
                name: 'Анна Михайлова',
                role: 'Отправитель',
                rating: 5,
                text: 'Революционная платформа! Доставка из Москвы в Берлин за 18 часов. Невероятно быстро и надежно!',
                avatar: 'АМ',
                color: 'from-primary to-secondary'
              },
              {
                name: 'Михаил Козлов',
                role: 'Курьер',
                rating: 5,
                text: 'Зарабатываю $500+ в месяц на путешествиях. TakeNPass изменил мою жизнь к лучшему.',
                avatar: 'МК',
                color: 'from-secondary to-accent'
              },
              {
                name: 'Елена Смирнова',
                role: 'Отправитель',
                rating: 5,
                text: 'Безопасность на высшем уровне. Блокчейн-верификация дает полное спокойствие.',
                avatar: 'ЕС',
                color: 'from-accent to-primary'
              }
            ].map((testimonial, i) => (
              <div key={i} className='group relative'>
                <div className='glass-card-light rounded-2xl p-6 border border-secondary/10 hover:border-secondary/30 transition-all duration-500 hover:shadow-xl group-hover:scale-105 hover-lift'>
                  <div className='flex items-center space-x-4 mb-6'>
                    <div className={`w-14 h-14 glass-card-primary rounded-full flex items-center justify-center shadow-lg border border-primary/30`}>
                      <span className='text-primary font-bold text-lg'>{testimonial.avatar}</span>
                    </div>
                    <div>
                      <div className='font-black text-foreground text-lg'>{testimonial.name}</div>
                      <div className='text-muted-foreground text-sm mb-1'>{testimonial.role}</div>
                      <div className='flex'>
                        {[...Array(testimonial.rating)].map((_, j) => (
                          <Star key={j} className='w-4 h-4 fill-accent text-accent' />
                        ))}
                      </div>
                    </div>
                  </div>
                  <blockquote className='text-muted-foreground leading-relaxed italic'>
                    "{testimonial.text}"
                  </blockquote>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className='py-32 relative overflow-hidden'>
        <div className='absolute inset-0 bg-gradient-to-br from-primary via-secondary to-accent'></div>
        <div className='absolute inset-0 bg-black/30'></div>

        {/* Animated background elements */}
        <div className='absolute inset-0'>
          <div className='absolute top-1/4 left-1/4 w-4 h-4 bg-white/30 rounded-full animate-bounce'></div>
          <div className='absolute top-3/4 right-1/4 w-3 h-3 bg-white/40 rounded-full animate-bounce' style={{ animationDelay: '0.5s' }}></div>
          <div className='absolute top-1/2 left-3/4 w-2 h-2 bg-white/50 rounded-full animate-bounce' style={{ animationDelay: '1s' }}></div>
          <div className='absolute bottom-1/4 left-1/2 w-5 h-5 bg-white/20 rounded-full animate-bounce' style={{ animationDelay: '1.5s' }}></div>
        </div>

        <div className='container mx-auto px-6 text-center relative z-10'>
          <div className='max-w-5xl mx-auto'>
            <div className='inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 mb-8'>
              <Rocket className='w-5 h-5 text-white' />
              <span className='text-sm font-bold text-white'>ПРИСОЕДИНЯЙСЯ К РЕВОЛЮЦИИ</span>
            </div>

            <h2 className='text-5xl lg:text-8xl font-black text-white mb-8 text-balance leading-tight'>
              ГОТОВ К <span className='text-black drop-shadow-lg'>БУДУЩЕМУ</span>?
            </h2>

            <p className='text-xl lg:text-2xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed font-medium'>
              Присоединяйся к революции краудшиппинга.
              <span className='font-black text-white'> Более 2.5 миллионов пользователей уже с нами.</span>
            </p>

            <div className='flex flex-col sm:flex-row gap-6 justify-center mb-12'>
              <Button
                size='lg'
                className='bg-white hover:bg-white/90 text-black font-black px-12 py-6 text-xl shadow-2xl hover:scale-105 transition-all duration-300 group border-2 border-white'
              >
                <Target className='w-6 h-6 mr-3 group-hover:rotate-90 transition-transform duration-300' />
                НАЧАТЬ СЕЙЧАС
              </Button>
              <Button
                size='lg'
                variant='bordered'
                className='border-4 border-white text-white hover:bg-white hover:text-black font-black px-12 py-6 text-xl bg-transparent backdrop-blur-sm hover:scale-105 transition-all duration-300 group'
              >
                <Zap className='w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-300' />
                УЗНАТЬ БОЛЬШЕ
              </Button>
            </div>

            {/* Trust indicators */}
            <div className='grid grid-cols-2 md:grid-cols-4 gap-8 text-white/80 text-center'>
              <div>
                <div className='text-2xl font-black text-white mb-1'>99.8%</div>
                <div className='text-sm'>Успешных доставок</div>
              </div>
              <div>
                <div className='text-2xl font-black text-white mb-1'>24/7</div>
                <div className='text-sm'>Поддержка</div>
              </div>
              <div>
                <div className='text-2xl font-black text-white mb-1'>256-bit</div>
                <div className='text-sm'>Шифрование</div>
              </div>
              <div>
                <div className='text-2xl font-black text-white mb-1'>5★</div>
                <div className='text-sm'>Средний рейтинг</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <footer className='glass-card-light border-t border-primary/20 py-20'>
        <div className='container mx-auto px-6'>
          <div className='grid md:grid-cols-4 gap-12 mb-16'>
            <div className='md:col-span-2'>
              <div className='flex items-center space-x-4 mb-8'>
                <div className='w-16 h-16 glass-card-primary rounded-2xl flex items-center justify-center shadow-lg border border-primary/30'>
                  <Truck className='w-8 h-8 text-primary' />
                </div>
                <div>
                  <span className='text-4xl font-black text-foreground'>TakeNPass</span>
                  <div className='text-sm text-muted-foreground'>Будущее доставки</div>
                </div>
              </div>
              <p className='text-muted-foreground text-lg leading-relaxed max-w-md mb-8'>
                Революционная платформа краудшиппинга для быстрой и выгодной доставки по всему миру.
              </p>

              {/* Social proof */}
              <div className='flex items-center gap-6 text-sm text-muted-foreground'>
                <div className='flex items-center gap-2'>
                  <div className='w-2 h-2 bg-green-500 rounded-full animate-pulse'></div>
                  <span>2.5M+ пользователей онлайн</span>
                </div>
                <div className='flex items-center gap-2'>
                  <Star className='w-4 h-4 fill-yellow-400 text-yellow-400' />
                  <span>4.9/5 рейтинг</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className='font-black text-foreground mb-8 text-xl flex items-center gap-2'>
                <Target className='w-5 h-5 text-primary' />
                ПЛАТФОРМА
              </h4>
              <ul className='space-y-4'>
                {[
                  { name: 'Как это работает', new: false },
                  { name: 'Безопасность', new: false },
                  { name: 'Тарифы', new: false },
                  { name: 'API для разработчиков', new: true }
                ].map((item, i) => (
                  <li key={i}>
                    <a href='#' className='text-muted-foreground hover:text-primary transition-colors font-medium flex items-center gap-2 group'>
                      {item.name}
                      {item.new && (
                        <Badge className='bg-primary/20 text-primary text-xs px-2 py-1'>NEW</Badge>
                      )}
                      <ArrowRight className='w-4 h-4 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-200' />
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className='font-black text-foreground mb-8 text-xl flex items-center gap-2'>
                <Users className='w-5 h-5 text-secondary' />
                КОМПАНИЯ
              </h4>
              <ul className='space-y-4'>
                {[
                  { name: 'О нас', new: false },
                  { name: 'Блог', new: false },
                  { name: 'Карьера', new: true },
                  { name: 'Пресс-центр', new: false }
                ].map((item, i) => (
                  <li key={i}>
                    <a href='#' className='text-muted-foreground hover:text-secondary transition-colors font-medium flex items-center gap-2 group'>
                      {item.name}
                      {item.new && (
                        <Badge className='bg-secondary/20 text-secondary text-xs px-2 py-1'>NEW</Badge>
                      )}
                      <ArrowRight className='w-4 h-4 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-200' />
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Newsletter */}
          <div className='glass-card-light rounded-2xl p-8 mb-12 text-center border border-primary/20'>
            <h3 className='text-2xl font-black text-foreground mb-4'>Будь в курсе новостей</h3>
            <p className='text-muted-foreground mb-6'>Получай обновления о новых функциях и специальных предложениях</p>
            <div className='flex flex-col sm:flex-row gap-4 max-w-md mx-auto'>
              <input
                type='email'
                placeholder='Твой email'
                className='flex-1 px-4 py-3 rounded-xl border border-primary/20 glass-card-light focus:outline-none focus:border-primary/50 transition-colors'
              />
              <Button className='glass-card-primary text-primary hover:text-white font-bold px-6 py-3 rounded-xl border border-primary/30 hover:bg-primary/20'>
                Подписаться
              </Button>
            </div>
          </div>

          <div className='border-t border-primary/20 pt-8 flex flex-col md:flex-row justify-between items-center gap-4'>
            <p className='text-muted-foreground flex items-center gap-2'>
              © 2025 TakeNPass. Все права защищены.
              <Lock className='w-4 h-4' />
            </p>
            <div className='flex flex-wrap gap-6'>
              <a href='#' className='text-muted-foreground hover:text-primary transition-colors text-sm'>
                Политика конфиденциальности
              </a>
              <a href='#' className='text-muted-foreground hover:text-primary transition-colors text-sm'>
                Условия использования
              </a>
              <a href='#' className='text-muted-foreground hover:text-primary transition-colors text-sm'>
                Cookies
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
