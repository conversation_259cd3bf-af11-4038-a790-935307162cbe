import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayMiddleCreateWithoutCaseInputSchema } from './WayMiddleCreateWithoutCaseInputSchema';
import { WayMiddleUncheckedCreateWithoutCaseInputSchema } from './WayMiddleUncheckedCreateWithoutCaseInputSchema';
import { WayMiddleCreateOrConnectWithoutCaseInputSchema } from './WayMiddleCreateOrConnectWithoutCaseInputSchema';
import { WayMiddleUpsertWithWhereUniqueWithoutCaseInputSchema } from './WayMiddleUpsertWithWhereUniqueWithoutCaseInputSchema';
import { WayMiddleCreateManyCaseInputEnvelopeSchema } from './WayMiddleCreateManyCaseInputEnvelopeSchema';
import { WayMiddleWhereUniqueInputSchema } from './WayMiddleWhereUniqueInputSchema';
import { WayMiddleUpdateWithWhereUniqueWithoutCaseInputSchema } from './WayMiddleUpdateWithWhereUniqueWithoutCaseInputSchema';
import { WayMiddleUpdateManyWithWhereWithoutCaseInputSchema } from './WayMiddleUpdateManyWithWhereWithoutCaseInputSchema';
import { WayMiddleScalarWhereInputSchema } from './WayMiddleScalarWhereInputSchema';

export const WayMiddleUncheckedUpdateManyWithoutCaseNestedInputSchema: z.ZodType<Prisma.WayMiddleUncheckedUpdateManyWithoutCaseNestedInput> = z.object({
  create: z.union([ z.lazy(() => WayMiddleCreateWithoutCaseInputSchema),z.lazy(() => WayMiddleCreateWithoutCaseInputSchema).array(),z.lazy(() => WayMiddleUncheckedCreateWithoutCaseInputSchema),z.lazy(() => WayMiddleUncheckedCreateWithoutCaseInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => WayMiddleCreateOrConnectWithoutCaseInputSchema),z.lazy(() => WayMiddleCreateOrConnectWithoutCaseInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => WayMiddleUpsertWithWhereUniqueWithoutCaseInputSchema),z.lazy(() => WayMiddleUpsertWithWhereUniqueWithoutCaseInputSchema).array() ]).optional(),
  createMany: z.lazy(() => WayMiddleCreateManyCaseInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => WayMiddleWhereUniqueInputSchema),z.lazy(() => WayMiddleWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => WayMiddleWhereUniqueInputSchema),z.lazy(() => WayMiddleWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => WayMiddleWhereUniqueInputSchema),z.lazy(() => WayMiddleWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => WayMiddleWhereUniqueInputSchema),z.lazy(() => WayMiddleWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => WayMiddleUpdateWithWhereUniqueWithoutCaseInputSchema),z.lazy(() => WayMiddleUpdateWithWhereUniqueWithoutCaseInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => WayMiddleUpdateManyWithWhereWithoutCaseInputSchema),z.lazy(() => WayMiddleUpdateManyWithWhereWithoutCaseInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => WayMiddleScalarWhereInputSchema),z.lazy(() => WayMiddleScalarWhereInputSchema).array() ]).optional(),
}).strict();

export default WayMiddleUncheckedUpdateManyWithoutCaseNestedInputSchema;
