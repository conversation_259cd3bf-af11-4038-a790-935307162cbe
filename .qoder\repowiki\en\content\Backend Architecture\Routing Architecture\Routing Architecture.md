# Routing Architecture

<cite>
**Referenced Files in This Document**   
- [trpc.ts](file://server/src/router/trpc.ts)
- [context.ts](file://server/src/router/context.ts)
- [case.ts](file://server/src/router/routers/case.ts)
- [user.ts](file://server/src/router/routers/user.ts)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts)
- [case.ts](file://server/src/services/case.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [tRPC Router Setup](#trpc-router-setup)
3. [Context Management](#context-management)
4. [Route Organization by Domain](#route-organization-by-domain)
5. [Procedure Definition and Input Validation](#procedure-definition-and-input-validation)
6. [Error Formatting](#error-formatting)
7. [Defining New tRPC Routes](#defining-new-trpc-routes)
8. [Relationship Between Routers and Services Layer](#relationship-between-routers-and-services-layer)
9. [Authentication Integration and Request Lifecycle Hooks](#authentication-integration-and-request-lifecycle-hooks)

## Introduction
The tRPC-based routing architecture in takeNpass provides a type-safe, end-to-end TypeScript API layer that enables seamless communication between frontend and backend. This document details the implementation of tRPC routers, context initialization, domain-based route organization, input validation using Zod, error handling, and integration with authentication and service layers. The architecture promotes modularity, reusability, and strong typing across the full stack.

## tRPC Router Setup
The tRPC router is initialized in `trpc.ts`, where the core tRPC instance is configured with SuperJSON for data serialization and custom error formatting. The setup defines reusable procedures—`publicProcedure` and `authedProcedure`—that serve as building blocks for all API endpoints. These procedures encapsulate common middleware such as authentication checks, ensuring consistent security policies across routes.

```mermaid
flowchart TD
A["initTRPC.context<Context>().create()"] --> B["Configure transformer: superjson"]
B --> C["Define errorFormatter"]
C --> D["Create base procedure"]
D --> E["Define middleware: isAuthed"]
E --> F["Export publicProcedure and authedProcedure"]
F --> G["Export router, mergeRouters, middleware"]
```

**Diagram sources**
- [trpc.ts](file://server/src/router/trpc.ts#L1-L39)

**Section sources**
- [trpc.ts](file://server/src/router/trpc.ts#L1-L39)

## Context Management
The request context is established in `context.ts`, which creates a per-request context object containing Fastify request and response objects, session data, and user information. It integrates with the Better Auth provider to retrieve session details from incoming headers. The context initialization includes i18n setup and safely handles potential errors during session retrieval, making user and session data available throughout the request lifecycle.

```mermaid
sequenceDiagram
participant Client
participant createContext
participant AuthAPI
Client->>createContext : Request with headers
createContext->>AuthAPI : auth.api.getSession(headers)
AuthAPI-->>createContext : Session and user data
createContext->>createContext : Attach req, res, session, user
createContext-->>Client : Context object
```

**Diagram sources**
- [context.ts](file://server/src/router/context.ts#L1-L38)

**Section sources**
- [context.ts](file://server/src/router/context.ts#L1-L38)

## Route Organization by Domain
Routes are organized into domain-specific modules within the `routers` directory, including `case.ts`, `user.ts`, `geo.ts`, `message.ts`, and `notification.ts`. Each router file exports a scoped tRPC router that groups related procedures. These individual routers are later merged into a single API surface, promoting separation of concerns and maintainability.

```mermaid
graph TB
subgraph Routers
A[case.ts]
B[user.ts]
C[geo.ts]
D[message.ts]
E[notification.ts]
end
F[trpc.ts] --> A
F --> B
F --> C
F --> D
F --> E
A --> |mergeRouters| G[Main Router]
B --> |mergeRouters| G
C --> |mergeRouters| G
D --> |mergeRouters| G
E --> |mergeRouters| G
```

**Diagram sources**
- [trpc.ts](file://server/src/router/trpc.ts#L1-L39)
- [routers/case.ts](file://server/src/router/routers/case.ts#L1-L73)
- [routers/user.ts](file://server/src/router/routers/user.ts#L1-L265)

**Section sources**
- [routers/case.ts](file://server/src/router/routers/case.ts#L1-L73)
- [routers/user.ts](file://server/src/router/routers/user.ts#L1-L265)

## Procedure Definition and Input Validation
Each route defines procedures using either `publicProcedure` or `authedProcedure`, with input validation enforced via Zod schemas. For example, the `caseRouter` uses `CaseCreateSchema` and `CaseListParamsSchema` to validate mutation and query inputs. This ensures type safety at runtime and provides clear error messages for invalid payloads.

```mermaid
flowchart TD
A[Client Request] --> B{Procedure Type}
B --> |Public| C[publicProcedure.input(schema)]
B --> |Authenticated| D[authedProcedure.input(schema)]
C --> E[Validate Input with Zod]
D --> E
E --> F{Valid?}
F --> |Yes| G[Execute Handler]
F --> |No| H[Return 400 with Errors]
```

**Diagram sources**
- [case.ts](file://server/src/router/routers/case.ts#L1-L73)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L1-L71)

**Section sources**
- [case.ts](file://server/src/router/routers/case.ts#L1-L73)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L1-L71)

## Error Formatting
Error formatting is configured in `trpc.ts` using the `errorFormatter` option, which currently passes through the default shape of the error without modification. This allows clients to receive structured error responses including status codes and messages. While minimal now, this hook can be extended to include additional metadata, logging, or custom error transformations.

**Section sources**
- [trpc.ts](file://server/src/router/trpc.ts#L1-L39)

## Defining New tRPC Routes
To define a new tRPC route, create a corresponding file in the `routers` directory (e.g., `newDomain.ts`) and export a router using `router({})`. Define procedures using `publicProcedure` or `authedProcedure`, apply input validation with Zod, and implement the handler logic. Finally, import and merge the new router into the main router composition.

Example pattern:
```ts
export const newRouter = router({
  get: publicProcedure
    .input(z.string())
    .query(async ({ input }) => {
      // logic here
    })
});
```

**Section sources**
- [trpc.ts](file://server/src/router/trpc.ts#L1-L39)
- [case.ts](file://server/src/router/routers/case.ts#L1-L73)

## Relationship Between Routers and Services Layer
Routers act as the API interface, delegating business logic to dedicated service classes such as `CaseService`, `UserService`, etc. This separation ensures that routing concerns (authentication, validation) are decoupled from domain logic (data processing, persistence). For instance, `caseRouter` calls methods on `CaseService` to create, update, or list cases, maintaining a clean abstraction boundary.

```mermaid
classDiagram
class caseRouter {
+updateOrCreate()
+list()
+myList()
+id()
}
class CaseService {
+create()
+update()
+list()
+myList()
+id()
}
caseRouter --> CaseService : "delegates to"
```

**Diagram sources**
- [routers/case.ts](file://server/src/router/routers/case.ts#L1-L73)
- [services/case.ts](file://server/src/services/case.ts#L1-L635)

**Section sources**
- [routers/case.ts](file://server/src/router/routers/case.ts#L1-L73)
- [services/case.ts](file://server/src/services/case.ts#L1-L635)

## Authentication Integration and Request Lifecycle Hooks
Authentication is integrated via the `authedProcedure`, which uses a middleware function to verify the presence of a valid user session. The `checkIsAuthed` utility function retrieves the user from context and throws an `UNAUTHORIZED` error if missing. This mechanism ensures that protected routes only proceed when a valid session exists, and it runs on every authenticated request.

Additionally, lifecycle actions such as setting session cookies during login or clearing them during logout are handled directly within route handlers, demonstrating how tRPC procedures can interact with HTTP-level concerns like headers and cookies.

```mermaid
sequenceDiagram
participant Client
participant AuthedProc
participant Middleware
participant Handler
Client->>AuthedProc : Call authedProcedure
AuthedProc->>Middleware : Run isAuthed middleware
Middleware->>Middleware : Check ctx.user?.id
alt User not authenticated
Middleware-->>Client : Throw UNAUTHORIZED
else User authenticated
Middleware->>Handler : Proceed with next()
Handler-->>Client : Return response
end
```

**Diagram sources**
- [trpc.ts](file://server/src/router/trpc.ts#L1-L39)
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)

**Section sources**
- [trpc.ts](file://server/src/router/trpc.ts#L1-L39)
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)