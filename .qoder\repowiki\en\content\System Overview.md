# System Overview

<cite>
**Referenced Files in This Document**   
- [PROJECT_DESCRIPTION.md](file://PROJECT_DESCRIPTION.md)
- [TECH_STACK.md](file://TECH_STACK.md)
- [server.ts](file://server/src/server.ts)
- [index.ts](file://server/src/router/index.ts)
- [case.ts](file://server/src/router/routers/case.ts)
- [geo.ts](file://server/src/router/routers/geo.ts)
- [case.ts](file://server/src/services/case.ts)
- [geo.ts](file://server/src/services/geo.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Business Value Proposition](#business-value-proposition)
3. [Technical Innovation](#technical-innovation)
4. [High-Level Architecture](#high-level-architecture)
5. [Frontend Architecture](#frontend-architecture)
6. [Backend Architecture](#backend-architecture)
7. [Real-Time Communication](#real-time-communication)
8. [User Workflows](#user-workflows)
9. [System Context Diagram](#system-context-diagram)

## Introduction

TakeNPass is a modern package crowdsourcing platform that connects senders who need to deliver packages and documents with travelers willing to transport them. The platform leverages contemporary web technologies to provide an intuitive interface for organizing small cargo deliveries, offering an economical alternative to traditional shipping services.

The system enables users to create delivery requests, search for suitable travel routes, communicate in real-time chat, manage delivery cases, and rate completed deliveries. It supports multiple languages (Russian, English, Spanish) and features responsive design for optimal experience across devices.

**Section sources**
- [PROJECT_DESCRIPTION.md](file://PROJECT_DESCRIPTION.md#L1-L231)

## Business Value Proposition

TakeNPass addresses the real-world challenge of small parcel delivery by creating a peer-to-peer marketplace where senders can find travelers on compatible routes. This model provides several key business advantages:

- **Cost Efficiency**: Eliminates traditional logistics overhead by leveraging existing travel plans
- **Environmental Impact**: Reduces carbon footprint by utilizing already-planned journeys
- **Flexibility**: Supports various package types, sizes, and delivery requirements
- **Trust System**: Implements a comprehensive rating and review mechanism to ensure reliability
- **Global Reach**: Connects users across international borders through a unified platform

The platform serves multiple user roles including senders, travelers (carriers), universal users who can perform both functions, moderators, and administrators, creating a complete ecosystem for package delivery coordination.

**Section sources**
- [PROJECT_DESCRIPTION.md](file://PROJECT_DESCRIPTION.md#L1-L231)

## Technical Innovation

TakeNPass distinguishes itself through several technical innovations that enhance both user experience and system reliability:

- **End-to-End Type Safety**: Full TypeScript integration from database schema to UI components via tRPC and Prisma, ensuring type consistency across the entire stack
- **Real-Time Geospatial Search**: Advanced route matching algorithm that finds compatible delivery cases within configurable radius parameters
- **Multi-Provider Geocoding**: Integration with both LocationIQ and Geoapify geoservices for reliable address autocompletion and reverse geocoding
- **Automated Case Management**: Server-side service that automatically closes expired delivery cases and notifies relevant parties
- **Intelligent Notification System**: Context-aware notifications triggered by user actions and system events

The platform's architecture emphasizes security with JWT token authentication, data validation through Zod schemas, rate limiting, and proper CORS configuration.

**Section sources**
- [TECH_STACK.md](file://TECH_STACK.md#L1-L208)
- [PROJECT_DESCRIPTION.md](file://PROJECT_DESCRIPTION.md#L1-L231)

## High-Level Architecture

The TakeNPass system follows a modern three-tier architecture with clear separation of concerns between frontend, backend, and data layers. The application employs a microservices-inspired approach within a monolithic structure, organizing functionality into distinct modules.

Key architectural characteristics include:
- **Frontend**: React/Vite/Tailwind CSS with Tanstack Router and Query
- **Backend**: Fastify server with tRPC for type-safe APIs
- **Database**: MySQL with Prisma ORM for type-safe database operations
- **Real-Time Communication**: WebSocket via Socket.IO for instant updates
- **Authentication**: Better Auth for secure user management

The system uses superjson for data serialization, enabling transmission of complex data types across the network boundary while maintaining type integrity.

```mermaid
graph TB
subgraph "Frontend"
A[React/Vite/Tailwind]
B[Tanstack Router]
C[Tanstack Query]
D[Zustand]
E[Leaflet Maps]
F[Socket.IO Client]
end
subgraph "Backend"
G[Fastify Server]
H[tRPC API]
I[Prisma ORM]
J[Socket.IO Server]
K[Better Auth]
end
subgraph "Data Layer"
L[MySQL Database]
M[Redis Cache]
end
A --> H
B --> H
C --> H
D --> A
E --> H
F --> J
G --> I
H --> I
I --> L
J --> F
K --> G
```

**Diagram sources**
- [TECH_STACK.md](file://TECH_STACK.md#L1-L208)
- [PROJECT_DESCRIPTION.md](file://PROJECT_DESCRIPTION.md#L1-L231)

## Frontend Architecture

The frontend of TakeNPass is built with React 18 and TypeScript, providing a robust foundation for the user interface. The application uses Vite as the build tool, offering fast development server startup and hot module replacement.

Key frontend components include:
- **UI Framework**: Tailwind CSS with HeroUI components for consistent styling
- **State Management**: Zustand for global state and React Query for server state management
- **Routing**: Tanstack Router for type-safe navigation
- **Maps**: Leaflet integrated with React-Leaflet for interactive route visualization
- **Forms**: Comprehensive form handling with real-time validation
- **Internationalization**: i18next with automatic language detection and switching

The frontend architecture supports lazy loading of components and efficient data caching through React Query, optimizing performance and reducing server load.

```mermaid
graph TD
A[Main App] --> B[Router]
A --> C[State Store]
A --> D[API Client]
B --> E[Case Page]
B --> F[Chat Page]
B --> G[Create Case]
B --> H[Profile]
C --> I[Zustand]
C --> J[React Query]
D --> K[tRPC Client]
D --> L[Socket.IO]
E --> M[Case Card]
E --> N[Map Component]
F --> O[Chat Interface]
G --> P[Form Validation]
H --> Q[User Settings]
```

**Diagram sources**
- [TECH_STACK.md](file://TECH_STACK.md#L1-L208)
- [frontend/src/App.tsx](file://frontend/src/App.tsx)
- [frontend/src/main.tsx](file://frontend/src/main.tsx)

## Backend Architecture

The backend of TakeNPass is implemented with Fastify, a high-performance web framework for Node.js, providing excellent throughput and low overhead. The server exposes a type-safe API through tRPC, enabling seamless integration with the frontend while maintaining strong typing guarantees.

Core backend modules include:
- **tRPC Routers**: Organized by domain (users, case, geo, message, notification)
- **Services Layer**: Business logic encapsulation in service classes (CaseService, GeoService, etc.)
- **Data Access**: Prisma ORM for database operations with generated type-safe clients
- **Authentication**: Better Auth integration for secure user sessions
- **Validation**: Zod schemas for input validation at API boundaries

The server implements rate limiting to prevent abuse and uses environment-based configuration for different deployment scenarios.

```mermaid
graph TD
A[Fastify Server] --> B[tRPC Router]
A --> C[CORS Middleware]
A --> D[Cookie Parser]
A --> E[Socket.IO]
B --> F[User Router]
B --> G[Case Router]
B --> H[Geo Router]
B --> I[Message Router]
B --> J[Notification Router]
F --> K[UserService]
G --> L[CaseService]
H --> M[GeoService]
I --> N[MessageService]
J --> O[NotificationService]
L --> P[Prisma Client]
M --> Q[Geocoding APIs]
P --> R[MySQL Database]
Q --> S[LocationIQ]
Q --> T[Geoapify]
```

**Diagram sources**
- [server/src/server.ts](file://server/src/server.ts#L1-L253)
- [server/src/router/index.ts](file://server/src/router/index.ts#L1-L20)
- [server/src/services/case.ts](file://server/src/services/case.ts#L1-L635)

## Real-Time Communication

TakeNPass incorporates real-time communication capabilities through WebSocket technology powered by Socket.IO. This enables instant updates and interactive features essential for a dynamic delivery coordination platform.

Key real-time features include:
- **Instant Messaging**: Real-time chat between senders and carriers
- **Live Notifications**: Immediate alerts for case assignments, messages, and status changes
- **Presence Indicators**: Online/offline status for users
- **Live Updates**: Automatic refresh of delivery case lists and statuses

The WebSocket implementation is integrated with the authentication system to ensure secure communication channels. The server maintains connection state and handles reconnection logic to provide reliable messaging even under unstable network conditions.

```mermaid
sequenceDiagram
participant UserA as Sender
participant UserB as Traveler
participant Frontend as Frontend App
participant Backend as Backend Server
participant DB as Database
UserA->>Frontend : Send Message
Frontend->>Backend : WebSocket Message Event
Backend->>DB : Save Message
Backend->>UserB : Push Message via WebSocket
Backend->>UserA : Confirm Delivery
UserB->>Frontend : View Message
Frontend->>Backend : Mark as Read
Backend->>DB : Update Read Status
```

**Diagram sources**
- [server/src/server.ts](file://server/src/server.ts#L1-L253)
- [server/src/providers/ws.ts](file://server/src/providers/ws.ts)
- [frontend/src/socket.ts](file://frontend/src/socket.ts)

## User Workflows

TakeNPass supports several key user workflows that represent the core functionality of the platform:

### Creating a Delivery Case
1. User navigates to "Create Case" page
2. Specifies origin and destination locations using geocoded search
3. Sets pickup and delivery dates/times
4. Describes package details (size, weight, fragility)
5. Sets delivery price and special requirements
6. Submits case to platform
7. System validates input and creates case record
8. Relevant parties receive notifications

### Searching Routes
1. User enters origin and destination points
2. Specifies search radius (up to 300km)
3. Filters by date range and delivery type
4. System queries database using geospatial conditions
5. Results displayed on interactive map and list
6. User can save searches or mark cases as favorites

### Rating Completed Deliveries
1. After delivery completion, users can access rating interface
2. Select star rating (1-5) and provide written feedback
3. Submit rating which is stored in database
4. Recipient's average rating is recalculated
5. Both parties receive confirmation of rating submission

These workflows are supported by the underlying service layer which handles business logic, data validation, and side effects like notifications.

**Section sources**
- [PROJECT_DESCRIPTION.md](file://PROJECT_DESCRIPTION.md#L1-L231)
- [server/src/services/case.ts](file://server/src/services/case.ts#L1-L635)
- [server/src/router/routers/case.ts](file://server/src/router/routers/case.ts#L1-L73)

## System Context Diagram

The following diagram illustrates the complete system context of TakeNPass, showing all major components and their interactions:

```mermaid
graph TD
subgraph "External Systems"
A[LocationIQ API]
B[Geoapify API]
C[Email Service]
D[Push Notification Service]
end
subgraph "Client Tier"
E[Web Browser]
F[React Frontend]
G[Leaflet Maps]
H[Socket.IO Client]
end
subgraph "Application Tier"
I[Fastify Server]
J[tRPC Endpoints]
K[WebSocket Server]
L[Better Auth]
M[Rate Limiter]
end
subgraph "Data Tier"
N[MySQL Database]
O[Prisma ORM]
P[Redis Cache]
end
E --> F
F --> G
F --> H
H --> K
F --> J
J --> I
I --> L
I --> M
J --> O
O --> N
K --> H
M --> P
J --> A
J --> B
I --> C
I --> D
```

**Diagram sources**
- [PROJECT_DESCRIPTION.md](file://PROJECT_DESCRIPTION.md#L1-L231)
- [TECH_STACK.md](file://TECH_STACK.md#L1-L208)
- [server/src/server.ts](file://server/src/server.ts#L1-L253)