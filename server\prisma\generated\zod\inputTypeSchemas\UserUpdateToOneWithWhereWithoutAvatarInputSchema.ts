import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserWhereInputSchema } from './UserWhereInputSchema';
import { UserUpdateWithoutAvatarInputSchema } from './UserUpdateWithoutAvatarInputSchema';
import { UserUncheckedUpdateWithoutAvatarInputSchema } from './UserUncheckedUpdateWithoutAvatarInputSchema';

export const UserUpdateToOneWithWhereWithoutAvatarInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutAvatarInput> = z.object({
  where: z.lazy(() => UserWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => UserUpdateWithoutAvatarInputSchema),z.lazy(() => UserUncheckedUpdateWithoutAvatarInputSchema) ]),
}).strict();

export default UserUpdateToOneWithWhereWithoutAvatarInputSchema;
