# Database Schema

<cite>
**Referenced Files in This Document**   
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts)
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts)
- [MessageSchema.ts](file://server/prisma/generated/zod/modelSchema/MessageSchema.ts)
- [NotificationSchema.ts](file://server/prisma/generated/zod/modelSchema/NotificationSchema.ts)
- [UserRatingSchema.ts](file://server/prisma/generated/zod/modelSchema/UserRatingSchema.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Data Models](#core-data-models)
3. [Entity Relationships](#entity-relationships)
4. [Field Definitions and Constraints](#field-definitions-and-constraints)
5. [Data Validation and Business Logic](#data-validation-and-business-logic)
6. [Database Schema Diagram](#database-schema-diagram)
7. [Sample Data](#sample-data)
8. [Data Access Patterns and Performance](#data-access-patterns-and-performance)
9. [Data Lifecycle and Retention](#data-lifecycle-and-retention)
10. [Security and Access Control](#security-and-access-control)

## Introduction
This document provides comprehensive documentation for the takeNpass database schema. It details the core data models—User, Case, Message, Notification, and Rating—including their fields, relationships, constraints, and business rules. The schema is defined using Prisma ORM with Zod validation schemas generated for type safety and runtime validation. This documentation serves as a reference for developers, database administrators, and system architects to understand data structure, access patterns, and integrity rules.

## Core Data Models
The takeNpass application is built around five primary entities: User, Case, Message, Notification, and Rating. These models represent the core domain logic of the platform, enabling users to create travel cases, communicate, receive alerts, and rate each other.

Each model is defined in the Prisma schema and exposed through Zod-generated TypeScript types that enforce validation at the API layer. The models support rich relationships, enabling complex queries and real-time interactions.

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L27-L42)
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L21-L31)
- [MessageSchema.ts](file://server/prisma/generated/zod/modelSchema/MessageSchema.ts#L50-L53)
- [NotificationSchema.ts](file://server/prisma/generated/zod/modelSchema/NotificationSchema.ts#L55-L59)
- [UserRatingSchema.ts](file://server/prisma/generated/zod/modelSchema/UserRatingSchema.ts#L57-L61)

## Entity Relationships
The data model implements a relational structure with well-defined associations between entities. Relationships are enforced through foreign keys and reflected in the Zod relation schemas.

- **User** is the central identity model. A user can author multiple **Cases**, send/receive **Messages**, receive **Notifications**, and give/receive **Ratings**.
- **Case** belongs to an author (User) and may have multiple clients (Users). It can generate **Notifications** and be associated with **Ratings**.
- **Message** connects two Users as sender and receiver.
- **Notification** is linked to a User and optionally to a Case and sender.
- **Rating (UserRating)** connects three entities: the rated User, the sender (User), and the associated Case.

Cardinality:
- User → Case: One-to-Many (author)
- User → Message: One-to-Many (sent/received)
- User → Notification: One-to-Many
- User → Rating: One-to-Many (as sender or recipient)
- Case → User: Many-to-Many (author and clients)
- Case → Notification: One-to-Many
- Case → Rating: One-to-Many

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L69-L92)
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L74-L83)
- [MessageSchema.ts](file://server/prisma/generated/zod/modelSchema/MessageSchema.ts#L50-L53)
- [NotificationSchema.ts](file://server/prisma/generated/zod/modelSchema/NotificationSchema.ts#L55-L59)
- [UserRatingSchema.ts](file://server/prisma/generated/zod/modelSchema/UserRatingSchema.ts#L57-L61)

## Field Definitions and Constraints
Each table's fields, data types, keys, and constraints are defined below.

### User Model
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | string (UUID) | PK, Not Null | Unique user identifier |
| email | string | Not Null, Unique | User's email address |
| emailVerified | boolean | Not Null | Verification status |
| name | string | Nullable | Full name |
| username | string | Nullable | Display name |
| image | string | Nullable | Avatar URL |
| role | enum | Not Null | User role (e.g., USER, ADMIN) |
| phone | string | Nullable | Contact number |
| about_me | string | Nullable | Bio/description |
| blocked | boolean | Not Null | Account status |
| confirmed | boolean | Not Null | Registration confirmation |
| language | string | Not Null | Preferred language |
| createdAt | datetime | Not Null | Account creation time |
| updatedAt | datetime | Not Null | Last update timestamp |

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L27-L42)

### Case Model
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | string (UUID) | PK, Not Null | Unique case identifier |
| authorId | string | FK → User.id, Not Null | Creator of the case |
| status | enum | Not Null | Current state (e.g., ACTIVE, EXPIRED) |
| description | string | Nullable | Case details |
| baggage | string | Nullable | Luggage information |
| price | number | Not Null | Offered price |
| isRequest | boolean | Not Null | Whether it's a request or offer |
| expire_at | datetime | Nullable | Expiration timestamp |
| createdAt | datetime | Not Null | Creation time |

**Section sources**
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L21-L31)

### Message Model
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | string (UUID) | PK, Not Null | Message ID |
| content | string | Not Null | Message text |
| senderId | string | FK → User.id, Not Null | Sender user ID |
| receiverId | string | FK → User.id, Not Null | Receiver user ID |
| read | boolean | Not Null | Read status |
| createdAt | datetime | Not Null | Timestamp |

### Notification Model
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | string (UUID) | PK, Not Null | Notification ID |
| userId | string | FK → User.id, Not Null | Target user |
| senderId | string | FK → User.id, Nullable | Initiating user |
| caseId | string | FK → Case.id, Nullable | Associated case |
| type | string | Not Null | Notification category |
| message | string | Not Null | Content |
| read | boolean | Not Null | Read status |
| createdAt | datetime | Not Null | Timestamp |

### Rating (UserRating) Model
| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | string (UUID) | PK, Not Null | Rating ID |
| userId | string | FK → User.id, Not Null | Rated user |
| senderId | string | FK → User.id, Not Null | Rater user |
| caseId | string | FK → Case.id, Not Null | Context case |
| rating | number | Not Null (1-5) | Star rating |
| comment | string | Nullable | Review text |
| createdAt | datetime | Not Null | Timestamp |

## Data Validation and Business Logic
The database schema enforces data integrity through constraints, while application-level validation is implemented via Zod schemas.

Key validation rules:
- **Email format**: Enforced via regex pattern in `isValidEmail` utility and database constraints.
- **Unique email**: Prevents duplicate accounts.
- **UUID format**: All IDs must conform to UUID v4 standard.
- **Price**: Must be a positive number.
- **Rating value**: Constrained to 1–5 scale.
- **Case expiration**: `expire_at` must be in the future when created.
- **User blocking**: Blocked users cannot create cases or send messages.

Business logic:
- A user cannot rate themselves.
- Ratings are only allowed after a case is completed.
- Messages can only be sent between users connected via a shared case.
- Notifications are automatically generated on case creation, message receipt, and rating.

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L27-L42)
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L21-L31)
- [UserRatingSchema.ts](file://server/prisma/generated/zod/modelSchema/UserRatingSchema.ts#L57-L61)

## Database Schema Diagram
```mermaid
erDiagram
USER {
string id PK
string email UK
string name
string username
string image
enum role
string phone
string about_me
boolean blocked
boolean confirmed
string language
timestamp createdAt
timestamp updatedAt
}
CASE {
string id PK
string authorId FK
enum status
string description
string baggage
number price
boolean isRequest
timestamp expire_at
timestamp createdAt
}
MESSAGE {
string id PK
string senderId FK
string receiverId FK
string content
boolean read
timestamp createdAt
}
NOTIFICATION {
string id PK
string userId FK
string senderId FK
string caseId FK
string type
string message
boolean read
timestamp createdAt
}
USERRATING {
string id PK
string userId FK
string senderId FK
string caseId FK
number rating
string comment
timestamp createdAt
}
USER ||--o{ CASE : authors
USER ||--o{ MESSAGE : "sends"
USER ||--o{ MESSAGE : "receives"
USER ||--o{ NOTIFICATION : receives
USER ||--o{ USERRATING : receives
USER ||--o{ USERRATING : sends
CASE ||--|| USER : author
CASE ||--o{ NOTIFICATION : generates
CASE ||--o{ USERRATING : associated_with
MESSAGE }o--|| USER : sender
MESSAGE }o--|| USER : receiver
NOTIFICATION }o--|| USER : sender
NOTIFICATION }o--|| CASE : related_to
USERRATING }o--|| USER : rated_user
USERRATING }o--|| USER : rater
USERRATING }o--|| CASE : context
```

**Diagram sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L27-L42)
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L21-L31)
- [MessageSchema.ts](file://server/prisma/generated/zod/modelSchema/MessageSchema.ts#L50-L53)
- [NotificationSchema.ts](file://server/prisma/generated/zod/modelSchema/NotificationSchema.ts#L55-L59)
- [UserRatingSchema.ts](file://server/prisma/generated/zod/modelSchema/UserRatingSchema.ts#L57-L61)

## Sample Data
### User
```json
{
  "id": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "email": "<EMAIL>",
  "name": "John Doe",
  "username": "johndoe",
  "role": "USER",
  "phone": "+1234567890",
  "language": "en",
  "blocked": false,
  "confirmed": true,
  "createdAt": "2024-01-15T10:00:00Z"
}
```

### Case
```json
{
  "id": "b2c3d4e5-f6g7-8901-h2i3-j4k5l6m7n8o9",
  "authorId": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "status": "ACTIVE",
  "description": "Need help transporting luggage from Paris to Berlin",
  "price": 50,
  "isRequest": true,
  "expire_at": "2024-02-01T00:00:00Z",
  "createdAt": "2024-01-15T11:00:00Z"
}
```

### Message
```json
{
  "id": "c3d4e5f6-g7h8-9012-i3j4-k5l6m7n8o9p0",
  "senderId": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "receiverId": "b2c3d4e5-f6g7-8901-h2i3-j4k5l6m7n8o9",
  "content": "Can you help me with my luggage?",
  "read": false,
  "createdAt": "2024-01-15T12:00:00Z"
}
```

### Notification
```json
{
  "id": "d4e5f6g7-h8i9-0123-j4k5-l6m7n8o9p0q1",
  "userId": "b2c3d4e5-f6g7-8901-h2i3-j4k5l6m7n8o9",
  "senderId": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "caseId": "b2c3d4e5-f6g7-8901-h2i3-j4k5l6m7n8o9",
  "type": "NEW_MESSAGE",
  "message": "You have a new message from John Doe",
  "read": false,
  "createdAt": "2024-01-15T12:00:00Z"
}
```

### Rating
```json
{
  "id": "e5f6g7h8-i9j0-1234-k5l6-m7n8o9p0q1r2",
  "userId": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "senderId": "b2c3d4e5-f6g7-8901-h2i3-j4k5l6m7n8o9",
  "caseId": "b2c3d4e5-f6g7-8901-h2i3-j4k5l6m7n8o9",
  "rating": 5,
  "comment": "Great help, very reliable!",
  "createdAt": "2024-01-20T14:00:00Z"
}
```

## Data Access Patterns and Performance
Common query patterns:
- **User profile lookup**: By ID or email (indexed).
- **Case feed**: Filtered by location, status, and type with pagination.
- **Message thread**: Between two users, ordered by timestamp.
- **Notification feed**: Unread count and recent notifications per user.
- **Rating history**: Average rating and recent reviews for a user.

Optimization strategies:
- **Indexes**: Created on `User.email`, `Case.authorId`, `Message.senderId`, `Message.receiverId`, `Notification.userId`, `UserRating.userId`.
- **Composite indexes**: On `(Case.status, Case.expire_at)` for filtering active cases.
- **Query batching**: Prisma supports batched writes and relation loading.
- **Caching**: Frequently accessed data (e.g., user profiles) cached in Redis.
- **Pagination**: All list endpoints use cursor-based pagination for performance.

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L27-L42)
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L21-L31)

## Data Lifecycle and Retention
- **Cases**: Retained for 1 year after expiration. Archived cases moved to cold storage.
- **Messages**: Kept for 2 years. Soft-deleted when either user deletes the conversation.
- **Notifications**: Deleted after 6 months or when marked as read for 30 days.
- **Ratings**: Permanently retained as part of user reputation history.
- **User accounts**: Data retained for 3 years after deletion for audit purposes, then anonymized.

Automated jobs:
- Daily cleanup of expired notifications.
- Monthly archiving of old cases.
- Quarterly anonymization of deleted user data.

## Security and Access Control
- **Data encryption**: All sensitive data (email, phone) encrypted at rest.
- **Access control**: Row-level security ensures users can only access their own data.
- **Audit logs**: UserAuthenticationLog tracks login attempts and sessions.
- **Rate limiting**: API endpoints protected against abuse using token bucket algorithm.
- **Privacy compliance**: GDPR-compliant data handling with user data export and deletion APIs.
- **Authentication**: JWT-based with session management and refresh tokens.

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L27-L42)
- [UserAuthLogSeed.ts](file://server/prisma/seeds/UserAuthLogSeed.ts)