import { MailIcon } from '@/lib/svg/MailIcon'
import { Avatar, Button, Card, CardBody, CardFooter, CardHeader, Input } from "@heroui/react"
import { useEffect, useMemo, useState } from 'react'
import { LockIcon } from './LockIcon'
import IconUser from '@/lib/svg/UserIcon'
import { Upload } from './Upload'
import { PhoneInput } from './PhoneInput'
import { addToast } from '@heroui/react'
import { useUserStore } from '@/store'
import { Link, useRouter } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { trpc } from '@/trpc'
import { TRPCErrorMessage } from '@/types'

interface Props {}

export const RegForm = ({}: Props) => {
  const { t } = useTranslation()

  const [username, setUsername] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [avatar, setAvatar] = useState()
  const [phone, setPhone] = useState()

  const [errors, setErrors] = useState({
    phone: undefined,
    email: undefined,
    username: undefined,
    password: undefined
  })

  const userStore = useUserStore()
  const { navigate } = useRouter()

  const [isLoading, setIsLoading] = useState(false)

  // Используем tRPC для создания пользователя
  const { mutate: createUser, isError, isPending, error } = trpc.users.createUser.useMutation()

  function onAvatarUpdate(base64string: string) {}

  const AvatarComponent = () =>
    useMemo(
      () => (
        <Avatar
          classNames={{
            name: 'font-semibold'
          }}
          className='shadow-xl'
          color='default'
          showFallback
          name={String(username?.split(' ').slice(0, 10) || '')}
          size='lg'
          isBordered
          src={avatar}
        />
      ),
      [avatar]
    )

  const UploadAvatar = useMemo(() => <Upload Activator={AvatarComponent} onChangeBase64={onAvatarUpdate} setValue={setAvatar} />, [avatar, username])

  async function signUp() {
    try {
      setIsLoading(true)

      // Очищаем предыдущие ошибки
      setErrors({
        phone: undefined,
        email: undefined,
        username: undefined,
        password: undefined
      })

      // Используем tRPC для создания пользователя (который внутри использует better-auth)
      createUser({
        email,
        password,
        username,
        phone,
        avatar
      }, {
        onSuccess: (userData) => {
          // Сохраняем данные пользователя в store
          userStore.setData(userData as any)
          addToast({ color: 'success', title: t('Signed up successfully') })
          navigate({ to: '/' })
        },
        onError: (error) => {
          console.error('Registration error:', error)
          addToast({ color: 'danger', title: error.message || t('Registration failed') })

          // Обрабатываем ошибки валидации
          if (error.message) {
            try {
              const validationErrors: TRPCErrorMessage[] = JSON.parse(error.message)

              const newErrors = {
                phone: undefined as string[] | undefined,
                email: undefined as string[] | undefined,
                username: undefined as string[] | undefined,
                password: undefined as string[] | undefined
              }

              validationErrors.forEach((err) => {
                if (err.path.includes('phone')) {
                  newErrors.phone = [...(newErrors.phone || []), err.message]
                }
                if (err.path.includes('email')) {
                  newErrors.email = [...(newErrors.email || []), err.message]
                }
                if (err.path.includes('username')) {
                  newErrors.username = [...(newErrors.username || []), err.message]
                }
                if (err.path.includes('password')) {
                  newErrors.password = [...(newErrors.password || []), err.message]
                }
              })

              setErrors(newErrors)
            } catch {
              // Если не удалось распарсить ошибки валидации, показываем общую ошибку
              if (error.message.toLowerCase().includes('email')) {
                setErrors(prev => ({ ...prev, email: [error.message] }))
              } else if (error.message.toLowerCase().includes('phone')) {
                setErrors(prev => ({ ...prev, phone: [error.message] }))
              } else if (error.message.toLowerCase().includes('username')) {
                setErrors(prev => ({ ...prev, username: [error.message] }))
              } else if (error.message.toLowerCase().includes('password')) {
                setErrors(prev => ({ ...prev, password: [error.message] }))
              }
            }
          }
        }
      })
    } catch (error) {
      console.error('Registration error:', error)
      addToast({ color: 'danger', title: t('Registration failed') })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <Card shadow='lg'>
        <div className='mt-3'>
          <div className='flex justify-center'>{UploadAvatar}</div>
        </div>
        <CardBody>
          <CardHeader>
            <div className='font-semibold text-lg'>{t('Join us')}</div>
          </CardHeader>
          <div className='flex flex-col gap-4 mt-5'>
            <Input
              // validationState={errors.username?.length ? 'invalid' : 'valid'}
              isInvalid={errors.username?.length}
              errorMessage={errors.username?.join?.(', ')}
              label={t('Username')}
              labelPlacement='inside'
              endContent={<IconUser className='text-2xl text-default-400 pointer-events-none shrink-0' />}
              value={username}
              onValueChange={setUsername}
            />
            <Input
              // validationState={errors.email?.length ? 'invalid' : 'valid'}
              isInvalid={errors.email?.length}
              errorMessage={errors.email?.join?.(', ')}
              endContent={<MailIcon className='text-2xl text-default-400 pointer-events-none shrink-0' />}
              type='email'
              label='E-mail'
              labelPlacement='inside'
              value={email}
              onValueChange={setEmail}
            />
            <Input
              // validationState={errors.password?.length ? 'invalid' : 'valid'}
              isInvalid={errors.password?.length}
              errorMessage={errors.password?.join?.(', ')}
              onValueChange={setPassword}
              endContent={<LockIcon className='text-2xl text-default-400 pointer-events-none shrink-0' />}
              label={t('Password')}
              value={password}
              type='password'
            />
            <PhoneInput
              // validationState={errors.phone?.length ? 'invalid' : 'valid'}
              isInvalid={errors.phone?.length}
              errorMessage={errors.phone?.join?.(', ')}
              onValueChange={(value) => setPhone(value)}
              value={phone}
            />
          </div>
        </CardBody>
        <div className='flex justify-center md:justify-end p-3 items-center'>
          <Button onClick={signUp} isLoading={isLoading || isPending} variant='flat' size='lg' color='success'>
            {t('Sign up')}
          </Button>
        </div>
      </Card>

      <div className='flex justify-center mt-5'>
        <Button variant='flat'>
          <Link to='/login'>{t('Sign in')}</Link>
        </Button>
      </div>
    </>
  )
}
