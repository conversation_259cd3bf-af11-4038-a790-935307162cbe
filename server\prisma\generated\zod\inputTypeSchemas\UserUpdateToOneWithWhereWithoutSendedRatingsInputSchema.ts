import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserWhereInputSchema } from './UserWhereInputSchema';
import { UserUpdateWithoutSendedRatingsInputSchema } from './UserUpdateWithoutSendedRatingsInputSchema';
import { UserUncheckedUpdateWithoutSendedRatingsInputSchema } from './UserUncheckedUpdateWithoutSendedRatingsInputSchema';

export const UserUpdateToOneWithWhereWithoutSendedRatingsInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutSendedRatingsInput> = z.object({
  where: z.lazy(() => UserWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => UserUpdateWithoutSendedRatingsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutSendedRatingsInputSchema) ]),
}).strict();

export default UserUpdateToOneWithWhereWithoutSendedRatingsInputSchema;
