# Geolocation API

<cite>
**Referenced Files in This Document**   
- [geo.ts](file://server/src/router/routers/geo.ts)
- [geo.ts](file://server/src/services/geo.ts)
- [GeoMeta.ts](file://server/src/types/GeoMeta.ts)
- [config.ts](file://server/src/config.ts)
- [MainMap.tsx](file://frontend/src/components/map/MainMap.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Geolocation Procedures](#geolocation-procedures)
3. [Zod Validation Schemas](#zod-validation-schemas)
4. [External Service Integration](#external-service-integration)
5. [Data Flow Overview](#data-flow-overview)
6. [Client-Side Usage](#client-side-usage)
7. [Performance and Reliability](#performance-and-reliability)
8. [Error Handling](#error-handling)

## Introduction
The geolocation system in takeNpass provides robust location-based functionality for the map interface, enabling address autocompletion, geocoding, and reverse geocoding through integration with external services. This API powers location input components and map interactions by transforming user queries into structured geographic data. The system is built on tRPC procedures with Zod validation, implements rate limiting, supports multiple geolocation providers, and includes fallback mechanisms to ensure reliability.

**Section sources**
- [geo.ts](file://server/src/router/routers/geo.ts#L1-L41)
- [geo.ts](file://server/src/services/geo.ts#L1-L219)

## Geolocation Procedures

The geolocation API exposes four primary tRPC procedures via the `geoRouter`, supporting both synchronous queries and asynchronous mutations for flexible client integration.

### findLocation
Public procedure for geocoding address queries into geographic coordinates. Accepts a search query and optional language parameter, returning a list of matching locations.

### lazyFindLocation
Mutation variant of `findLocation` intended for deferred execution scenarios where immediate response is not required.

### reverse
Public procedure for reverse geocoding, converting geographic coordinates (latitude and longitude) into human-readable addresses.

### lazyReverse
Mutation variant of `reverse` for asynchronous reverse geocoding operations.

All procedures utilize a shared rate-limited queue to control external API call frequency and prevent service overuse.

```mermaid
sequenceDiagram
participant Client
participant tRPC
participant GeoService
participant ExternalAPI
Client->>tRPC : findLocation(query, lang)
tRPC->>GeoService : queue.add(findLocation)
loop For each enabled service
GeoService->>ExternalAPI : API Request
ExternalAPI-->>GeoService : Response
alt Valid Response
GeoService-->>tRPC : Return result
break Success
end
end
tRPC-->>Client : GeoMeta[]
Client->>tRPC : reverse(lat, lng, lang)
tRPC->>GeoService : queue.add(reverse)
loop For each enabled service
GeoService->>ExternalAPI : Reverse Request
ExternalAPI-->>GeoService : Data
alt Success
GeoService-->>tRPC : Transformed result
break Return
end
end
tRPC-->>Client : GeoMeta | GeoMeta[]
```

**Diagram sources**
- [geo.ts](file://server/src/router/routers/geo.ts#L1-L41)
- [geo.ts](file://server/src/services/geo.ts#L1-L219)

**Section sources**
- [geo.ts](file://server/src/router/routers/geo.ts#L1-L41)

## Zod Validation Schemas

Input validation is enforced using Zod schemas to ensure data integrity and security.

### findQuerySchema
Validates location search queries:
- `query`: Required string (minimum 1 character)
- `lang`: Optional language code (e.g., "en", "es")

### reverseSchema
Validates reverse geocoding parameters:
- `lat`: Required latitude (string or number, minimum length 3 characters when string)
- `lng`: Required longitude (string or number, minimum length 3 characters when string)
- `lang`: Optional language preference for response formatting

These schemas are applied to both query and mutation procedures, ensuring consistent validation across all geolocation endpoints.

**Section sources**
- [geo.ts](file://server/src/router/routers/geo.ts#L4-L18)

## External Service Integration

The geolocation system integrates with two external providers: Geoapify and LocationIQ, with configurable fallback behavior.

### Service Configuration
The active geolocation services are defined in `geoConfig` within `config.ts`. By default, both LocationIQ and Geoapify are enabled, providing redundancy in case one service fails.

```typescript
export const geoConfig: GeoConfig = {
  activeGeoServices: ['LocationIQ', 'Geoapify']
}
```

### Geoapify Implementation
- Uses `GEOPIFY_API_KEY` and `GEOPIFY_API_URL` environment variables
- Provides autocomplete, search, and reverse geocoding endpoints
- Transforms responses into standardized `GeoMeta` format
- Supports language-specific results via `lang` parameter

### LocationIQ Implementation
- Utilizes `LOCATIONIQ_API_KEY` and `LOCATIONIQ_API_URL` environment variables
- Implements autocomplete for address suggestions
- Offers reverse geocoding with detailed address components
- Applies normalization to city names and handles duplicates

### Fallback Mechanism
The `GeoService` class implements a sequential fallback strategy:
1. Iterates through enabled services in configuration order
2. Returns results from the first successful provider
3. Catches and logs errors without interrupting the chain
4. Returns empty array or undefined if all services fail

```mermaid
flowchart TD
A[Start findLocation] --> B{Try LocationIQ}
B --> C[Success?]
C --> |Yes| D[Return Result]
C --> |No| E{Try Geoapify}
E --> F[Success?]
F --> |Yes| D
F --> |No| G[Return Empty Array]
H[Start reverse] --> I{Try LocationIQ}
I --> J[Success?]
J --> |Yes| K[Return Result]
J --> |No| L{Try Geoapify}
L --> M[Success?]
M --> |Yes| K
M --> |No| N[Return Undefined]
```

**Diagram sources**
- [geo.ts](file://server/src/services/geo.ts#L1-L219)
- [config.ts](file://server/src/config.ts#L1-L30)

**Section sources**
- [geo.ts](file://server/src/services/geo.ts#L1-L219)
- [config.ts](file://server/src/config.ts#L1-L30)

## Data Flow Overview

The geolocation data flow follows a structured pipeline from user input to formatted response:

1. **User Input**: Address query or coordinate input from map interface
2. **Client Request**: tRPC call with validated parameters
3. **Server Processing**: 
   - Input validation via Zod
   - Rate-limited queue processing
   - Sequential service invocation
4. **External API Calls**: HTTP requests to Geoapify and/or LocationIQ
5. **Response Transformation**: Normalization into `GeoMeta` interface
6. **Client Delivery**: Structured location data for UI rendering

The `GeoMeta` interface standardizes location data across providers, ensuring consistent client-side handling regardless of the source service.

```mermaid
flowchart LR
A[User Input] --> B[MainMap.tsx]
B --> C[tRPC Procedure]
C --> D[Zod Validation]
D --> E[RateLimitedQueue]
E --> F[GeoService]
F --> G[LocationIQ]
F --> H[Geoapify]
G --> I[Transform to GeoMeta]
H --> I
I --> J[Client Response]
J --> K[Map Rendering]
```

**Diagram sources**
- [geo.ts](file://server/src/services/geo.ts#L1-L219)
- [GeoMeta.ts](file://server/src/types/GeoMeta.ts#L1-L22)
- [MainMap.tsx](file://frontend/src/components/map/MainMap.tsx)

**Section sources**
- [geo.ts](file://server/src/services/geo.ts#L1-L219)
- [GeoMeta.ts](file://server/src/types/GeoMeta.ts#L1-L22)

## Client-Side Usage

The geolocation API is primarily consumed by `MainMap.tsx` and associated location input components.

### tRPC Client Integration
The frontend uses tRPC for type-safe API calls, automatically generating types from server procedures. Components invoke geolocation procedures through the tRPC client with proper error handling.

### Example Invocation
```typescript
// In MainMap.tsx or location components
const { data, isLoading } = trpc.geo.findLocation.useQuery({
  query: searchInput,
  lang: 'en'
})

const reverseMutation = trpc.geo.reverse.useMutation()
const result = await reverseMutation.mutateAsync({
  lat: 40.7128,
  lng: -74.0060,
  lang: 'en'
})
```

### Input Components
Location search fields use `findLocation` with debounced input to minimize API calls, while map interactions trigger `reverse` when users click or drop pins.

**Section sources**
- [MainMap.tsx](file://frontend/src/components/map/MainMap.tsx)
- [geo.ts](file://server/src/router/routers/geo.ts#L1-L41)

## Performance and Reliability

The geolocation system implements several performance and reliability features.

### Rate Limiting
A `RateLimitedQueue` controls the frequency of external API calls:
- Maximum concurrency: 2 simultaneous requests
- Delay between requests: 1000ms
- Error tolerance: Continues processing despite individual request failures

This prevents exceeding provider rate limits and ensures fair resource usage.

### Request Debouncing
Client-side implementations should debounce user input (typically 300-500ms) to avoid excessive API calls during typing.

### Caching Strategy
While explicit caching is not implemented in the current code, the sequential service fallback provides implicit redundancy. Future enhancements could include Redis or in-memory caching of frequent queries.

### Fallback Mechanisms
The system automatically falls back to alternative providers when:
- Primary service returns an error
- Request times out
- Response is invalid or empty

This multi-provider approach ensures continued functionality even if one service experiences downtime.

**Section sources**
- [geo.ts](file://server/src/router/routers/geo.ts#L1-L41)
- [geo.ts](file://server/src/services/geo.ts#L1-L219)

## Error Handling

The geolocation API handles various error conditions with appropriate responses.

### Error Types
- **Invalid Queries**: Rejected by Zod validation with descriptive error messages
- **Service Timeouts**: Caught and logged, triggering fallback to next provider
- **Rate Limiting**: Managed by RateLimitedQueue to prevent triggering external limits
- **Empty Results**: Return empty array (`[]`) for `findLocation`, `undefined` for `reverse`
- **Provider Failures**: Individual service errors are caught and do not terminate the fallback chain

### Error Codes and Responses
- Validation errors return 400-level status codes with detailed Zod error objects
- Service unavailability returns empty results after exhausting all providers
- Authentication failures (invalid API keys) are logged and trigger fallback
- Network errors are handled by ky HTTP client with retry logic

Client applications should handle both successful responses (array or object) and empty/undefined results gracefully, providing appropriate user feedback.

**Section sources**
- [geo.ts](file://server/src/services/geo.ts#L1-L219)
- [geo.ts](file://server/src/router/routers/geo.ts#L1-L41)