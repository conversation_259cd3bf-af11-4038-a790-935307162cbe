# Authentication API

<cite>
**Referenced Files in This Document**   
- [better-auth.ts](file://server/src/providers/better-auth.ts)
- [auth.ts](file://server/src/providers/auth.ts)
- [user.ts](file://server/src/router/routers/user.ts)
- [LoginModal.tsx](file://frontend/src/components/LoginModal.tsx)
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Authentication Flow](#authentication-flow)
3. [API Endpoints](#api-endpoints)
4. [Input Validation with Zod](#input-validation-with-zod)
5. [Session and Token Management](#session-and-token-management)
6. [Frontend Integration](#frontend-integration)
7. [Security Considerations](#security-considerations)
8. [Error Handling](#error-handling)
9. [Conclusion](#conclusion)

## Introduction
The takeNpass authentication system provides secure user registration, login, session management, and profile retrieval via tRPC procedures. Built on better-auth and integrated with tRPC, the system ensures type-safe, validated, and secure authentication flows. This document details the authentication endpoints, validation schemas, JWT and session handling, frontend integration, and security practices.

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [user.ts](file://server/src/router/routers/user.ts#L1-L266)

## Authentication Flow
The authentication process in takeNpass follows a secure, stateful session-based model using better-auth. Upon successful login or registration, a session is created and stored in the database, with a corresponding JWT-like token delivered via HTTP-only cookies. The session is validated on each authenticated request.

```mermaid
sequenceDiagram
participant Frontend
participant Backend
participant betterAuth
participant Database
Frontend->>Backend : POST /api/auth/login {login, password}
Backend->>UserService : validate credentials
UserService->>Database : find user by email
Database-->>UserService : user data
UserService->>UserService : verify password
UserService-->>Backend : user + session data
Backend->>betterAuth : create session
betterAuth->>Database : store session
betterAuth-->>Backend : session cookie
Backend->>Frontend : set-cookie header + user data
Frontend->>Backend : GET /api/auth/me (with cookie)
Backend->>betterAuth : validate session
betterAuth->>Database : verify session
betterAuth-->>Backend : session + user
Backend-->>Frontend : user profile
```

**Diagram sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [user.ts](file://server/src/router/routers/user.ts#L150-L180)

## API Endpoints
The authentication endpoints are implemented as tRPC procedures under the user router.

### Registration (POST /api/auth/register)
- **Procedure**: `createUser`
- **Access**: Public
- **Input**: `createUserSchema`
- **Output**: Created user object (without password)
- **Session**: No session created automatically

### Login (POST /api/auth/login)
- **Procedure**: `login`
- **Access**: Public
- **Input**: `loginSchema` (login, password)
- **Output**: User object
- **Session**: Session cookie set via `set-cookie`

### Profile Retrieval (GET /api/auth/me)
- **Procedure**: `validate`
- **Access**: Authenticated
- **Input**: None (uses session)
- **Output**: Full user profile
- **Session**: Validates active session

### Logout (POST /api/auth/logout)
- **Procedure**: `logout`
- **Access**: Public (but requires active session)
- **Input**: None
- **Output**: Boolean success
- **Session**: Session invalidated, cookies cleared

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L150-L266)

## Input Validation with Zod
All input data is validated using Zod schemas to ensure data integrity and security.

### createUserSchema
Validates user registration data:
- `username`: string, 4–30 characters
- `email`: valid email format
- `password`: 4–30 characters
- `phone`: 7–15 digits, valid phone number
- `avatar`: optional base64 string (max 3000 chars)

### loginSchema
Validates login credentials:
- `login`: string, 4–30 characters (email or username)
- `password`: 4–30 characters

### updateUserSchema
Used for profile updates:
- All fields optional
- Same validation rules as creation

```mermaid
flowchart TD
Start([Input Received]) --> ValidateEmail["Validate Email Format"]
ValidateEmail --> EmailValid{"Email Valid?"}
EmailValid --> |No| Return400["Return 400: Invalid Email"]
EmailValid --> |Yes| ValidatePassword["Validate Password Length"]
ValidatePassword --> PassValid{"Password 4-30 chars?"}
PassValid --> |No| Return400
PassValid --> |Yes| ValidatePhone["Validate Phone Number"]
ValidatePhone --> PhoneValid{"Phone Valid?"}
PhoneValid --> |No| Return400
PhoneValid --> |Yes| Proceed["Proceed to Authentication"]
Return400 --> End([Error Response])
Proceed --> End
```

**Diagram sources**
- [user.ts](file://server/src/router/routers/user.ts#L10-L50)

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L10-L50)

## Session and Token Management
Session handling is managed by better-auth with the following configuration:

- **Session Expiry**: 7 days
- **Cookie Max-Age**: 24 hours (renewed daily)
- **Cookie Cache**: Enabled for performance
- **Storage**: MySQL via Prisma adapter
- **Security**: HTTP-only, secure cookies (in production)

The session is automatically renewed every 24 hours if the user is active.

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L10-L25)

## Frontend Integration
Frontend components interact with the authentication API through tRPC clients.

### LoginModal.tsx
Handles user login:
- Collects email/username and password
- Invokes `auth.login` tRPC mutation
- On success, stores user in global state
- Redirects to dashboard

### RegForm.tsx
Handles user registration:
- Collects full user data
- Validates input before submission
- Calls `auth.createUser` tRPC mutation
- On success, triggers login flow

Both components use `trpc.ts` client to communicate securely with the backend.

**Section sources**
- [LoginModal.tsx](file://frontend/src/components/LoginModal.tsx)
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx)
- [user.ts](file://server/src/router/routers/user.ts)

## Security Considerations
The authentication system implements multiple security measures:

- **Password Hashing**: Handled automatically by better-auth
- **Rate Limiting**: Implemented via `rateLimit.ts` utility
- **Session Protection**: HTTP-only, secure cookies
- **Input Validation**: Zod schemas prevent injection
- **Origin Control**: Trusted origins configured in better-auth
- **Error Obfuscation**: Generic error messages to prevent enumeration

Email verification is disabled in development but should be enabled in production.

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [utils/rateLimit.ts](file://server/src/utils/rateLimit.ts)
- [user.ts](file://server/src/router/routers/user.ts)

## Error Handling
The API returns standardized error codes:

| Code | Scenario | HTTP Status |
|------|---------|-------------|
| `UNAUTHORIZED` | Invalid credentials | 401 |
| `NOT_FOUND` | User not found | 404 |
| `CONFLICT` | Email already in use | 409 |
| `BAD_REQUEST` | Validation failure | 400 |
| `FORBIDDEN` | Already logged in | 403 |
| `INTERNAL_SERVER_ERROR` | Server error | 500 |

Errors are logged server-side for debugging, but only generic messages are exposed to clients.

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L160-L170)

## Conclusion
The takeNpass authentication API provides a secure, type-safe, and well-structured system for user management. By leveraging better-auth and tRPC, it ensures robust session handling, input validation, and seamless frontend integration. Developers should follow the documented procedures and security practices when extending or maintaining the authentication flow.