{"code_snippets": [{"id": "3731ac9c59298a76ef93c402ccb225de", "path": "\\server\\src\\router\\routers\\case.ts", "line_range": "10-73", "gmt_create": "2025-09-12T22:54:42.0249291+02:00", "gmt_modified": "2025-09-12T22:54:42.0249291+02:00"}, {"id": "1b13cad207f31cb3dcfcf83078544d54", "path": "\\server\\src\\shemas\\CaseShemas.ts", "line_range": "0-71", "gmt_create": "2025-09-12T22:54:42.0296852+02:00", "gmt_modified": "2025-09-12T22:54:42.0296852+02:00"}, {"id": "48dce13813c07e178e475ce165830135", "path": "\\server\\src\\router\\routers\\geo.ts", "line_range": "10-40", "gmt_create": "2025-09-12T22:54:42.0339758+02:00", "gmt_modified": "2025-09-12T22:54:42.0339758+02:00"}, {"id": "25a64600b2e1237aad9eec1841818694", "path": "\\frontend\\src\\trpc.ts", "line_range": "0-3", "gmt_create": "2025-09-12T22:54:42.0376788+02:00", "gmt_modified": "2025-09-12T22:54:42.0376788+02:00"}, {"id": "b8a2ea37d203cadef3d45038eeaaa0d0", "path": "\\server\\src\\router\\routers\\case.ts", "line_range": "10", "gmt_create": "2025-09-12T22:54:42.0408065+02:00", "gmt_modified": "2025-09-12T22:54:42.0408065+02:00"}, {"id": "a345308b40d5619290c6e482ae10f4f3", "path": "\\server\\src\\router\\routers\\geo.ts", "line_range": "10", "gmt_create": "2025-09-12T22:54:42.0444617+02:00", "gmt_modified": "2025-09-12T22:54:42.0444617+02:00"}], "commits": [{"id": "f3f815e441842cda55b1b0a6975693be99748b74", "message": "chore 120925", "gmt_create": "2025-09-12T22:54:42.1462726+02:00", "gmt_modified": "2025-09-12T22:54:42.1462726+02:00"}], "knowledge_relations": [{"id": 1456, "source_id": "a70fa1cd-534a-4842-ae12-0239116148c1", "target_id": "3ad03cc1-6b8d-4c0d-9ad4-e804e42b7f83", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a70fa1cd-534a-4842-ae12-0239116148c1 -> 3ad03cc1-6b8d-4c0d-9ad4-e804e42b7f83", "gmt_create": "2025-09-12T22:05:19.2207222+02:00", "gmt_modified": "2025-09-12T22:05:19.2207222+02:00"}, {"id": 1457, "source_id": "a70fa1cd-534a-4842-ae12-0239116148c1", "target_id": "cecdc212-b047-4a30-ad74-22c73e0cb95a", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a70fa1cd-534a-4842-ae12-0239116148c1 -> cecdc212-b047-4a30-ad74-22c73e0cb95a", "gmt_create": "2025-09-12T22:05:19.2299987+02:00", "gmt_modified": "2025-09-12T22:05:19.2299987+02:00"}, {"id": 1458, "source_id": "a70fa1cd-534a-4842-ae12-0239116148c1", "target_id": "27e3b6c4-4e2c-436c-a217-4258374894d4", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a70fa1cd-534a-4842-ae12-0239116148c1 -> 27e3b6c4-4e2c-436c-a217-4258374894d4", "gmt_create": "2025-09-12T22:05:19.2383112+02:00", "gmt_modified": "2025-09-12T22:05:19.2383112+02:00"}, {"id": 1459, "source_id": "a70fa1cd-534a-4842-ae12-0239116148c1", "target_id": "575971c6-d66b-47d5-bb3c-76efdacc9cbd", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a70fa1cd-534a-4842-ae12-0239116148c1 -> 575971c6-d66b-47d5-bb3c-76efdacc9cbd", "gmt_create": "2025-09-12T22:05:19.2444318+02:00", "gmt_modified": "2025-09-12T22:05:19.2444318+02:00"}, {"id": 1460, "source_id": "a70fa1cd-534a-4842-ae12-0239116148c1", "target_id": "26589136-2b6f-4b0f-bfab-2bb61d30947a", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a70fa1cd-534a-4842-ae12-0239116148c1 -> 26589136-2b6f-4b0f-bfab-2bb61d30947a", "gmt_create": "2025-09-12T22:05:19.2516247+02:00", "gmt_modified": "2025-09-12T22:05:19.2516247+02:00"}, {"id": 1461, "source_id": "a70fa1cd-534a-4842-ae12-0239116148c1", "target_id": "803d8863-bf92-4f33-919c-b9f4acd42cd3", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a70fa1cd-534a-4842-ae12-0239116148c1 -> 803d8863-bf92-4f33-919c-b9f4acd42cd3", "gmt_create": "2025-09-12T22:05:19.2573672+02:00", "gmt_modified": "2025-09-12T22:05:19.2573672+02:00"}, {"id": 1462, "source_id": "a70fa1cd-534a-4842-ae12-0239116148c1", "target_id": "c5e69ca7-3884-4a19-8a57-fa21c96a6b00", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a70fa1cd-534a-4842-ae12-0239116148c1 -> c5e69ca7-3884-4a19-8a57-fa21c96a6b00", "gmt_create": "2025-09-12T22:05:19.2617255+02:00", "gmt_modified": "2025-09-12T22:05:19.2617255+02:00"}, {"id": 1469, "source_id": "2328b43e-bf3c-4cc3-a44a-2ddf2efff91a", "target_id": "295a436b-85e4-4878-8df4-ba179033fd9e", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 2328b43e-bf3c-4cc3-a44a-2ddf2efff91a -> 295a436b-85e4-4878-8df4-ba179033fd9e", "gmt_create": "2025-09-12T22:05:19.292747+02:00", "gmt_modified": "2025-09-12T22:05:19.292747+02:00"}, {"id": 1470, "source_id": "2328b43e-bf3c-4cc3-a44a-2ddf2efff91a", "target_id": "0d94eee9-9b37-43d6-a3d4-c48ca4ddb652", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 2328b43e-bf3c-4cc3-a44a-2ddf2efff91a -> 0d94eee9-9b37-43d6-a3d4-c48ca4ddb652", "gmt_create": "2025-09-12T22:05:19.2965011+02:00", "gmt_modified": "2025-09-12T22:05:19.2965011+02:00"}, {"id": 1471, "source_id": "2328b43e-bf3c-4cc3-a44a-2ddf2efff91a", "target_id": "4b7a8b2a-0b2e-4ad6-a44d-ac728030a90e", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 2328b43e-bf3c-4cc3-a44a-2ddf2efff91a -> 4b7a8b2a-0b2e-4ad6-a44d-ac728030a90e", "gmt_create": "2025-09-12T22:05:19.3002413+02:00", "gmt_modified": "2025-09-12T22:05:19.3002413+02:00"}, {"id": 1472, "source_id": "2328b43e-bf3c-4cc3-a44a-2ddf2efff91a", "target_id": "efe33477-db16-4324-a28a-1769974a6449", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 2328b43e-bf3c-4cc3-a44a-2ddf2efff91a -> efe33477-db16-4324-a28a-1769974a6449", "gmt_create": "2025-09-12T22:05:19.3040751+02:00", "gmt_modified": "2025-09-12T22:05:19.3040751+02:00"}, {"id": 1473, "source_id": "2328b43e-bf3c-4cc3-a44a-2ddf2efff91a", "target_id": "bc1bd533-ea36-4791-865e-2754d33b9b95", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 2328b43e-bf3c-4cc3-a44a-2ddf2efff91a -> bc1bd533-ea36-4791-865e-2754d33b9b95", "gmt_create": "2025-09-12T22:05:19.307946+02:00", "gmt_modified": "2025-09-12T22:05:19.307946+02:00"}, {"id": 1474, "source_id": "2328b43e-bf3c-4cc3-a44a-2ddf2efff91a", "target_id": "134ab981-2bca-4a2d-9ad3-30e62c470f28", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 2328b43e-bf3c-4cc3-a44a-2ddf2efff91a -> 134ab981-2bca-4a2d-9ad3-30e62c470f28", "gmt_create": "2025-09-12T22:05:19.3117026+02:00", "gmt_modified": "2025-09-12T22:05:19.3117026+02:00"}, {"id": 1475, "source_id": "9e183489-7913-4f65-8344-fb43a4b087b3", "target_id": "fe9e8802-e8ae-4c2c-9aec-79026f93cf09", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9e183489-7913-4f65-8344-fb43a4b087b3 -> fe9e8802-e8ae-4c2c-9aec-79026f93cf09", "gmt_create": "2025-09-12T22:05:19.3155403+02:00", "gmt_modified": "2025-09-12T22:05:19.3155403+02:00"}, {"id": 1476, "source_id": "9e183489-7913-4f65-8344-fb43a4b087b3", "target_id": "7b719390-3e9b-4ded-8b21-208c71ce5591", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9e183489-7913-4f65-8344-fb43a4b087b3 -> 7b719390-3e9b-4ded-8b21-208c71ce5591", "gmt_create": "2025-09-12T22:05:19.3193171+02:00", "gmt_modified": "2025-09-12T22:05:19.3193171+02:00"}, {"id": 1477, "source_id": "9e183489-7913-4f65-8344-fb43a4b087b3", "target_id": "674a957e-7838-4b1b-bb80-b3f4c85cf487", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9e183489-7913-4f65-8344-fb43a4b087b3 -> 674a957e-7838-4b1b-bb80-b3f4c85cf487", "gmt_create": "2025-09-12T22:05:19.3392832+02:00", "gmt_modified": "2025-09-12T22:05:19.3392832+02:00"}, {"id": 1478, "source_id": "9e183489-7913-4f65-8344-fb43a4b087b3", "target_id": "398d7161-49ec-4ede-a631-46d51bdbd334", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9e183489-7913-4f65-8344-fb43a4b087b3 -> 398d7161-49ec-4ede-a631-46d51bdbd334", "gmt_create": "2025-09-12T22:05:19.3430623+02:00", "gmt_modified": "2025-09-12T22:05:19.3430623+02:00"}, {"id": 1479, "source_id": "9e183489-7913-4f65-8344-fb43a4b087b3", "target_id": "72a50e04-7117-469a-bd9e-5e9feabdeed2", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9e183489-7913-4f65-8344-fb43a4b087b3 -> 72a50e04-7117-469a-bd9e-5e9feabdeed2", "gmt_create": "2025-09-12T22:05:19.346798+02:00", "gmt_modified": "2025-09-12T22:05:19.346798+02:00"}, {"id": 1480, "source_id": "9e183489-7913-4f65-8344-fb43a4b087b3", "target_id": "176108c1-db11-42f3-ba96-e070cbdf4a79", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9e183489-7913-4f65-8344-fb43a4b087b3 -> 176108c1-db11-42f3-ba96-e070cbdf4a79", "gmt_create": "2025-09-12T22:05:19.3524101+02:00", "gmt_modified": "2025-09-12T22:05:19.3524101+02:00"}, {"id": 1481, "source_id": "cecdc212-b047-4a30-ad74-22c73e0cb95a", "target_id": "d0a06c7e-ff3c-4dda-a2c0-75b5bfe51611", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: cecdc212-b047-4a30-ad74-22c73e0cb95a -> d0a06c7e-ff3c-4dda-a2c0-75b5bfe51611", "gmt_create": "2025-09-12T22:05:19.3573593+02:00", "gmt_modified": "2025-09-12T22:05:19.3573593+02:00"}, {"id": 1482, "source_id": "cecdc212-b047-4a30-ad74-22c73e0cb95a", "target_id": "bd454387-378c-40b8-829c-28d62f6fd8f3", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: cecdc212-b047-4a30-ad74-22c73e0cb95a -> bd454387-378c-40b8-829c-28d62f6fd8f3", "gmt_create": "2025-09-12T22:05:19.3611652+02:00", "gmt_modified": "2025-09-12T22:05:19.3611652+02:00"}, {"id": 1483, "source_id": "cecdc212-b047-4a30-ad74-22c73e0cb95a", "target_id": "a1c1da46-3fe5-4555-aa81-8d1d91ba8c07", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: cecdc212-b047-4a30-ad74-22c73e0cb95a -> a1c1da46-3fe5-4555-aa81-8d1d91ba8c07", "gmt_create": "2025-09-12T22:05:19.3648325+02:00", "gmt_modified": "2025-09-12T22:05:19.3648325+02:00"}, {"id": 1484, "source_id": "cecdc212-b047-4a30-ad74-22c73e0cb95a", "target_id": "58297470-07ab-446a-9d30-0ac175374d15", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: cecdc212-b047-4a30-ad74-22c73e0cb95a -> 58297470-07ab-446a-9d30-0ac175374d15", "gmt_create": "2025-09-12T22:05:19.3683861+02:00", "gmt_modified": "2025-09-12T22:05:19.3683861+02:00"}, {"id": 1485, "source_id": "cecdc212-b047-4a30-ad74-22c73e0cb95a", "target_id": "803b88bf-f4e5-4e3a-812d-1f3ff8702418", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: cecdc212-b047-4a30-ad74-22c73e0cb95a -> 803b88bf-f4e5-4e3a-812d-1f3ff8702418", "gmt_create": "2025-09-12T22:05:19.3725324+02:00", "gmt_modified": "2025-09-12T22:05:19.3725324+02:00"}, {"id": 1486, "source_id": "27e3b6c4-4e2c-436c-a217-4258374894d4", "target_id": "4784febc-a23b-4681-9bc7-c3411642671d", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 27e3b6c4-4e2c-436c-a217-4258374894d4 -> 4784febc-a23b-4681-9bc7-c3411642671d", "gmt_create": "2025-09-12T22:05:19.3760606+02:00", "gmt_modified": "2025-09-12T22:05:19.3760606+02:00"}, {"id": 1487, "source_id": "27e3b6c4-4e2c-436c-a217-4258374894d4", "target_id": "f943a285-6f98-4798-b209-760f02d4597e", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 27e3b6c4-4e2c-436c-a217-4258374894d4 -> f943a285-6f98-4798-b209-760f02d4597e", "gmt_create": "2025-09-12T22:05:19.3800461+02:00", "gmt_modified": "2025-09-12T22:05:19.3800461+02:00"}, {"id": 1488, "source_id": "27e3b6c4-4e2c-436c-a217-4258374894d4", "target_id": "05af9740-5d06-4b1d-b102-cbc0622d4208", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 27e3b6c4-4e2c-436c-a217-4258374894d4 -> 05af9740-5d06-4b1d-b102-cbc0622d4208", "gmt_create": "2025-09-12T22:05:19.3840524+02:00", "gmt_modified": "2025-09-12T22:05:19.3840524+02:00"}, {"id": 1489, "source_id": "27e3b6c4-4e2c-436c-a217-4258374894d4", "target_id": "fab4406d-6ced-4fb5-a3da-a8f09033e6f2", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 27e3b6c4-4e2c-436c-a217-4258374894d4 -> fab4406d-6ced-4fb5-a3da-a8f09033e6f2", "gmt_create": "2025-09-12T22:05:19.3911043+02:00", "gmt_modified": "2025-09-12T22:05:19.3911043+02:00"}, {"id": 1490, "source_id": "27e3b6c4-4e2c-436c-a217-4258374894d4", "target_id": "6f2e3e0c-68de-4a0b-9307-aca7f28894b8", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 27e3b6c4-4e2c-436c-a217-4258374894d4 -> 6f2e3e0c-68de-4a0b-9307-aca7f28894b8", "gmt_create": "2025-09-12T22:05:19.394359+02:00", "gmt_modified": "2025-09-12T22:05:19.394359+02:00"}, {"id": 1491, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "ed73ae6c9ec7fa6570016763367c0854", "source_type": "WIKI_ITEM", "target_type": "SOURCE_FILE", "relationship_type": "REFERENCED_BY", "extra": "Wiki references source file: d:\\Dev\\takeNpass\\server\\src\\router\\routers\\case.ts", "gmt_create": "2025-09-12T22:54:42.047687+02:00", "gmt_modified": "2025-09-12T22:54:42.047687+02:00"}, {"id": 1492, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "71fa9c8519ac6bf47161ef82015de87e", "source_type": "WIKI_ITEM", "target_type": "SOURCE_FILE", "relationship_type": "REFERENCED_BY", "extra": "Wiki references source file: d:\\Dev\\takeNpass\\server\\src\\router\\routers\\geo.ts", "gmt_create": "2025-09-12T22:54:42.0513001+02:00", "gmt_modified": "2025-09-12T22:54:42.0513001+02:00"}, {"id": 1493, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "518731fbf9954911fa1cf36adeb62d1a", "source_type": "WIKI_ITEM", "target_type": "SOURCE_FILE", "relationship_type": "REFERENCED_BY", "extra": "Wiki references source file: d:\\Dev\\takeNpass\\server\\src\\router\\routers\\message.ts", "gmt_create": "2025-09-12T22:54:42.0550227+02:00", "gmt_modified": "2025-09-12T22:54:42.0550227+02:00"}, {"id": 1494, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "059c1cf083e27ec266be92b48ac6ccf8", "source_type": "WIKI_ITEM", "target_type": "SOURCE_FILE", "relationship_type": "REFERENCED_BY", "extra": "Wiki references source file: d:\\Dev\\takeNpass\\server\\src\\router\\routers\\notification.ts", "gmt_create": "2025-09-12T22:54:42.0590557+02:00", "gmt_modified": "2025-09-12T22:54:42.0590557+02:00"}, {"id": 1495, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "baa05ab8e5184d12d9d8dc46348f22dd", "source_type": "WIKI_ITEM", "target_type": "SOURCE_FILE", "relationship_type": "REFERENCED_BY", "extra": "Wiki references source file: d:\\Dev\\takeNpass\\server\\src\\router\\routers\\sub.ts", "gmt_create": "2025-09-12T22:54:42.065135+02:00", "gmt_modified": "2025-09-12T22:54:42.065135+02:00"}, {"id": 1496, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "3133620622dbb1cddb4b9e2db00872a1", "source_type": "WIKI_ITEM", "target_type": "SOURCE_FILE", "relationship_type": "REFERENCED_BY", "extra": "Wiki references source file: d:\\Dev\\takeNpass\\server\\src\\shemas\\CaseShemas.ts", "gmt_create": "2025-09-12T22:54:42.0700017+02:00", "gmt_modified": "2025-09-12T22:54:42.0700017+02:00"}, {"id": 1497, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "879084681a26d3ebafb7a6584986dc3c", "source_type": "WIKI_ITEM", "target_type": "SOURCE_FILE", "relationship_type": "REFERENCED_BY", "extra": "Wiki references source file: d:\\Dev\\takeNpass\\server\\src\\router\\trpc.ts", "gmt_create": "2025-09-12T22:54:42.0737036+02:00", "gmt_modified": "2025-09-12T22:54:42.0737036+02:00"}, {"id": 1498, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "b4cbfdd04760e3592097cf98b14863ed", "source_type": "WIKI_ITEM", "target_type": "SOURCE_FILE", "relationship_type": "REFERENCED_BY", "extra": "Wiki references source file: d:\\Dev\\takeNpass\\frontend\\src\\trpc.ts", "gmt_create": "2025-09-12T22:54:42.0779888+02:00", "gmt_modified": "2025-09-12T22:54:42.0779888+02:00"}, {"id": 1499, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "3731ac9c59298a76ef93c402ccb225de", "source_type": "WIKI_ITEM", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Wiki contains code snippet: d:\\Dev\\takeNpass\\server\\src\\router\\routers\\case.ts#10-73", "gmt_create": "2025-09-12T22:54:42.0821093+02:00", "gmt_modified": "2025-09-12T22:54:42.0821093+02:00"}, {"id": 1500, "source_id": "ed73ae6c9ec7fa6570016763367c0854", "target_id": "3731ac9c59298a76ef93c402ccb225de", "source_type": "SOURCE_FILE", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Source file contains code snippet: 10-73", "gmt_create": "2025-09-12T22:54:42.0857523+02:00", "gmt_modified": "2025-09-12T22:54:42.0857523+02:00"}, {"id": 1501, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "1b13cad207f31cb3dcfcf83078544d54", "source_type": "WIKI_ITEM", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Wiki contains code snippet: d:\\Dev\\takeNpass\\server\\src\\shemas\\CaseShemas.ts#0-71", "gmt_create": "2025-09-12T22:54:42.0894084+02:00", "gmt_modified": "2025-09-12T22:54:42.0894084+02:00"}, {"id": 1502, "source_id": "3133620622dbb1cddb4b9e2db00872a1", "target_id": "1b13cad207f31cb3dcfcf83078544d54", "source_type": "SOURCE_FILE", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Source file contains code snippet: 0-71", "gmt_create": "2025-09-12T22:54:42.0931385+02:00", "gmt_modified": "2025-09-12T22:54:42.0931385+02:00"}, {"id": 1503, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "48dce13813c07e178e475ce165830135", "source_type": "WIKI_ITEM", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Wiki contains code snippet: d:\\Dev\\takeNpass\\server\\src\\router\\routers\\geo.ts#10-40", "gmt_create": "2025-09-12T22:54:42.0968684+02:00", "gmt_modified": "2025-09-12T22:54:42.0968684+02:00"}, {"id": 1504, "source_id": "71fa9c8519ac6bf47161ef82015de87e", "target_id": "48dce13813c07e178e475ce165830135", "source_type": "SOURCE_FILE", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Source file contains code snippet: 10-40", "gmt_create": "2025-09-12T22:54:42.1031792+02:00", "gmt_modified": "2025-09-12T22:54:42.1031792+02:00"}, {"id": 1505, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "25a64600b2e1237aad9eec1841818694", "source_type": "WIKI_ITEM", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Wiki contains code snippet: d:\\Dev\\takeNpass\\frontend\\src\\trpc.ts#0-3", "gmt_create": "2025-09-12T22:54:42.1092008+02:00", "gmt_modified": "2025-09-12T22:54:42.1092008+02:00"}, {"id": 1506, "source_id": "b4cbfdd04760e3592097cf98b14863ed", "target_id": "25a64600b2e1237aad9eec1841818694", "source_type": "SOURCE_FILE", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Source file contains code snippet: 0-3", "gmt_create": "2025-09-12T22:54:42.1152331+02:00", "gmt_modified": "2025-09-12T22:54:42.1152331+02:00"}, {"id": 1507, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "b8a2ea37d203cadef3d45038eeaaa0d0", "source_type": "WIKI_ITEM", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Wiki contains code snippet: d:\\Dev\\takeNpass\\server\\src\\router\\routers\\case.ts#10", "gmt_create": "2025-09-12T22:54:42.1213937+02:00", "gmt_modified": "2025-09-12T22:54:42.1213937+02:00"}, {"id": 1508, "source_id": "ed73ae6c9ec7fa6570016763367c0854", "target_id": "b8a2ea37d203cadef3d45038eeaaa0d0", "source_type": "SOURCE_FILE", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Source file contains code snippet: 10", "gmt_create": "2025-09-12T22:54:42.1278722+02:00", "gmt_modified": "2025-09-12T22:54:42.1278722+02:00"}, {"id": 1509, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "a345308b40d5619290c6e482ae10f4f3", "source_type": "WIKI_ITEM", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Wiki contains code snippet: d:\\Dev\\takeNpass\\server\\src\\router\\routers\\geo.ts#10", "gmt_create": "2025-09-12T22:54:42.1339202+02:00", "gmt_modified": "2025-09-12T22:54:42.1339202+02:00"}, {"id": 1510, "source_id": "71fa9c8519ac6bf47161ef82015de87e", "target_id": "a345308b40d5619290c6e482ae10f4f3", "source_type": "SOURCE_FILE", "target_type": "CODE_SNIPPET", "relationship_type": "CONTAINS", "extra": "Source file contains code snippet: 10", "gmt_create": "2025-09-12T22:54:42.1401826+02:00", "gmt_modified": "2025-09-12T22:54:42.1401826+02:00"}, {"id": 1511, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "f3f815e441842cda55b1b0a6975693be99748b74", "source_type": "WIKI_ITEM", "target_type": "COMMIT", "relationship_type": "GENERATED_IN", "extra": "Wiki generated in commit: f3f815e441842cda55b1b0a6975693be99748b74", "gmt_create": "2025-09-12T22:54:42.1494684+02:00", "gmt_modified": "2025-09-12T22:54:42.1494684+02:00"}, {"id": 1512, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "1faf7792-84ff-424a-b5d3-a4e914744936", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a13f0f9a-9f89-404e-99fe-9e636ef43211 -> 1faf7792-84ff-424a-b5d3-a4e914744936", "gmt_create": "2025-09-12T22:54:42.2321292+02:00", "gmt_modified": "2025-09-12T22:54:42.2321292+02:00"}, {"id": 1513, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "c2dfcf3c-7f8b-4c04-bfe9-4b1a629cd1c9", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a13f0f9a-9f89-404e-99fe-9e636ef43211 -> c2dfcf3c-7f8b-4c04-bfe9-4b1a629cd1c9", "gmt_create": "2025-09-12T22:54:42.2368297+02:00", "gmt_modified": "2025-09-12T22:54:42.2368297+02:00"}, {"id": 1514, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "888ebf03-df2c-41cd-a1bd-050d957f8112", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a13f0f9a-9f89-404e-99fe-9e636ef43211 -> 888ebf03-df2c-41cd-a1bd-050d957f8112", "gmt_create": "2025-09-12T22:54:42.2415433+02:00", "gmt_modified": "2025-09-12T22:54:42.2415433+02:00"}, {"id": 1515, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "24f24e5b-0019-4723-a135-0d7cc30b9870", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a13f0f9a-9f89-404e-99fe-9e636ef43211 -> 24f24e5b-0019-4723-a135-0d7cc30b9870", "gmt_create": "2025-09-12T22:54:42.2462035+02:00", "gmt_modified": "2025-09-12T22:54:42.2462035+02:00"}, {"id": 1516, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "265c8f43-f35f-4b35-834a-7832f2d98869", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a13f0f9a-9f89-404e-99fe-9e636ef43211 -> 265c8f43-f35f-4b35-834a-7832f2d98869", "gmt_create": "2025-09-12T22:54:42.2521332+02:00", "gmt_modified": "2025-09-12T22:54:42.2521332+02:00"}, {"id": 1517, "source_id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "target_id": "cdccfdd8-8ccf-4a03-b82f-b094c2b4fe8e", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: a13f0f9a-9f89-404e-99fe-9e636ef43211 -> cdccfdd8-8ccf-4a03-b82f-b094c2b4fe8e", "gmt_create": "2025-09-12T22:54:42.2569845+02:00", "gmt_modified": "2025-09-12T22:54:42.2569845+02:00"}], "source_files": [{"id": "ed73ae6c9ec7fa6570016763367c0854", "path": "\\server\\src\\router\\routers\\case.ts", "filename": "case.ts", "gmt_create": "2025-09-12T22:54:41.9977957+02:00", "gmt_modified": "2025-09-12T22:54:41.9977957+02:00"}, {"id": "71fa9c8519ac6bf47161ef82015de87e", "path": "\\server\\src\\router\\routers\\geo.ts", "filename": "geo.ts", "gmt_create": "2025-09-12T22:54:42.0017945+02:00", "gmt_modified": "2025-09-12T22:54:42.0017945+02:00"}, {"id": "518731fbf9954911fa1cf36adeb62d1a", "path": "\\server\\src\\router\\routers\\message.ts", "filename": "message.ts", "gmt_create": "2025-09-12T22:54:42.0050306+02:00", "gmt_modified": "2025-09-12T22:54:42.0050306+02:00"}, {"id": "059c1cf083e27ec266be92b48ac6ccf8", "path": "\\server\\src\\router\\routers\\notification.ts", "filename": "notification.ts", "gmt_create": "2025-09-12T22:54:42.0082245+02:00", "gmt_modified": "2025-09-12T22:54:42.0082245+02:00"}, {"id": "baa05ab8e5184d12d9d8dc46348f22dd", "path": "\\server\\src\\router\\routers\\sub.ts", "filename": "sub.ts", "gmt_create": "2025-09-12T22:54:42.0120724+02:00", "gmt_modified": "2025-09-12T22:54:42.0120724+02:00"}, {"id": "3133620622dbb1cddb4b9e2db00872a1", "path": "\\server\\src\\shemas\\CaseShemas.ts", "filename": "CaseShemas.ts", "gmt_create": "2025-09-12T22:54:42.0153022+02:00", "gmt_modified": "2025-09-12T22:54:42.0153022+02:00"}, {"id": "879084681a26d3ebafb7a6584986dc3c", "path": "\\server\\src\\router\\trpc.ts", "filename": "trpc.ts", "gmt_create": "2025-09-12T22:54:42.0185672+02:00", "gmt_modified": "2025-09-12T22:54:42.0185672+02:00"}, {"id": "b4cbfdd04760e3592097cf98b14863ed", "path": "\\frontend\\src\\trpc.ts", "filename": "trpc.ts", "gmt_create": "2025-09-12T22:54:42.0217596+02:00", "gmt_modified": "2025-09-12T22:54:42.0217596+02:00"}], "wiki_catalogs": [{"id": "e572e11f-54cd-474e-bf00-685bf9a397f8", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "System Overview", "description": "system-overview", "prompt": "Create comprehensive content for this section focused on the takeNpass system. Explain its purpose as a package crowdsourcing platform connecting senders with travelers for delivery. Document the high-level architecture including frontend (React/Vite/Tailwind), backend (Fastify/Prisma/tRPC), and real-time communication via WebSockets. Illustrate the relationship between components using system context diagrams. Include both conceptual overviews for beginners and technical details for experienced developers. Provide practical examples of user workflows such as creating a delivery case, searching routes, and rating completed deliveries. Address the business value proposition and technical innovation of the platform.", "parent_id": "", "order": 0, "progress_status": "completed", "dependent_files": "PROJECT_DESCRIPTION.md,TECH_STACK.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.3652238+02:00", "gmt_modified": "2025-09-12T21:01:03.3845113+02:00", "raw_data": "WikiEncrypted:gWB8HBj+8+/15rQhXgtMjECeCGuLoR2IwruDlfaI6j3yrxjzUYfuy/PkXL4s2fe2Q/+ElQvzrakKLa+r4Z4VhJRBxNuxRHcos6uvxs+Ukc8M7GYzPX5qpc9XYI5Xh5R8+9osEwh0dIa3ffH2i8OqFzm7sAWIpTFoymF7dyv4Smiwb9PHmXjNVrhxW7uQhv0AQyk1JWwzgMomR75Zf+kwKsB6+K2mLIx8nJWDffwA0tocyPMjAxRvZxJvsuVoz7aUXVGk2y0AsN2ZFvl2MNGjlodQQe0UA+qw8vNcwU3D+gm1L2fJvmOrmrCgmw/ju8BX4FaWwoN5412weh7K67UWMb+Q68jKUivlgYA0rUFvtfum9cPZcvZ3mHIlmsTnp0y3+OfsEAkNXxQ4ksD3/lSM404oZ61xUYJM7jhWQj8l3dgvsd45adyvJL7AwFINrbEP+DQdnUMbOMexfhuuzCJ7AkBLrtHaNsNhu6QmdLONjtwUtKVk6PT7CudjGnla/ydba4GKKYIP9BFPtGSMCYpJ+P6Tj7Lth3dSKMMuvEIl9jgRm9HgHDdwlDOfn4pYJsgckVmW60KYIxY4KpB7Xt8XNREmXepFBAHSTtjPGbQ7l6xd0KkpJoSrR49Itvd3m8A+Wu7KsNs+6jHyHpcdaUycaHqQscYirhu1NT4pDnxE01tErxxM7oabdej1/OMTii6pr0B3HHJqKhcYifbJK943KpQZ8a4/kAc5OKWPl9j99HbhZqsidbNNd+qqUUXLGoxqZhk1l/+4LhlU6Zj8zq4qtDbaWfR2YgljLsLnyZIu0cB+7P6lBZTQEG0lq+mrCw7jDD4uAFRCW9vL4MKO7BC/EN9hKDb9anV0Hkx9XZ+rXvtw4cj4GbybgURJngcDzuL7zHpD6WS7NRlj5d9G+ZLbnPgujR3BSiBXVXnYg06NV9zLF9ztOemQ+vlPwpBJEF8x1B9MRMY/X4JD51I2wptLbRK3A29UARt+b2AsuuyYQ2lNuzDi6v6oXRih9nHtluFDzopKhaW383qdbHZDUP4+nVTldeedX516Gut+/0nak+xwyCte8XOzueraT8v+CYYKOXQKxLi6viqePGpLwsJxH+03kmxb4zz0WZCR+7bse1Hn/BE17GlED21n5JTtRWlxLD+sa29Xs8o6/6055whlUOcpxWZgNl44FfozqZctpoaQK08IyIlqDwuohhW4bPdWbfCGtcGfCECCDCNprbhhHGi6rR/6V+8RE0Y3zLLiXaCdzpleUlwejMSRyeMj+dz4ZFFQRbHNbVpnBS0HJbqoeg==", "layer_level": 0}, {"id": "4b4d2362-e3a3-4091-93de-3228c643841e", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Server Initialization", "description": "backend-server-initialization", "prompt": "Create detailed documentation for the server initialization process in takeNpass backend. Explain the bootstrapping sequence in server.ts including Fastify instance creation, plugin registration (tRPC, CORS, logging), and middleware setup. Document the configuration loading mechanism from config.ts and environment variables. Describe the error handling strategy, graceful shutdown implementation, and health check endpoints. Include code examples showing server startup sequence and plugin integration. Address common initialization issues and their troubleshooting methods. Explain the relationship between server.ts and index.ts in the application lifecycle.", "parent_id": "5b381073-14b9-4b74-ba3c-f2ceb3abdd90", "order": 0, "progress_status": "completed", "dependent_files": "server/src/server.ts,server/src/index.ts,server/src/config.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:13.8957258+02:00", "gmt_modified": "2025-09-12T21:18:20.9444057+02:00", "raw_data": "WikiEncrypted:N3UbzwGjCC3xD6BrTM0cq73gJasL7rXSUPWsGZuFSgkEGrY+d1AUwRzdEw5qfYXJ4bYE2SnSpYIt9zZspB3BRoUbGKUT8DSfsbz5ucpWS1wqYTlQ1rq24t74CBOpDdMtuGiQ9IrBylHoKBvsHNpjf9uH5NjHKvPqva5GPpBD/dm/opXuuhcqwPNQRKFi0yF9wPWZ1DUfm9Mp8FCTyfqrt7iZlujmUjvwK6G3Hby7pe9BdiZfBT58w7jG1vjLCNN4zFBaWR8gX7k+wfBZ24EWRkH/95Ywhqb7UBgWwvLXhBfVELC1x3z4Ox3zcyPov9Mw4G3D7bzKMpuXe/s7hDBxC1q/XtAXaPsxq1junQ47lQnIjz0aCkb3sKIDzJHPiRwf8GzVAxHIQc8nnU2tM8pnvCL3g/5hr716s6BqagwUlKYjrvWsdOpoPqvP3MkPNhNWsmt9p3N3K8A9Rh/hLfh+iukDAT4R0MAOJkEgNmqXuOlhTStDusMoKlbx3SlmW0xtW025GuS0vMeM0VnyZkSJugN/oxU6h14nW7cCcigU/q/TZ4cpYepx5VApUtF/PuKJWSV7e3m9WVvYsVbFbvn9hm4ZHrF4Fy2eZIHPXI4d1ohYjTtg5I+C05GVchz9TFwbbjph6oWHe+rfUCFIg1QMm51EVkIiEMM4Y4Ktgf682BO5XiCVAwnALyksFw2dqwAOHXz9uxra3DJ8cIlWHA1il/RS5wNM4vh1zPytoe91IeckqSHKaVs62fUBugENjIyWLCl1DFFSV96/2cKDvXEFnjD6OTFYn29meM5WTXgpnzTaIOwOKFAB4v9AJhc97fwJAm1OW+1uIeCxf9pFxET9ECB3g5k0xN0OLJSIB6LW8BkRq4ttWofChbaZ4DgzfpeZp91T/sdWRXQl5SYCaitRecJ+cYZXbF/F1t8rf8FdHJwP+Y/hN5MGvQlkoTXIS43y2yUIAnFy4zLrHbfkjYXbv+pbDwdmymoLrzOlSTHTYc2fmnMzR2DAEmFb5GArq83Ja1RqLaz8yoz6nuM+domXLs0J2J01Z/kZrHZPm6ntXrxzcTWBVdTcG9ZkEMJKYpCb+J4S5McrrstylqwjY7OHsP4vYWDDprvrQ4bgU5Z/CwfGV7LJP0T+BU6xcVMpy6YS4PJl9mcR53G+Pp3NvEZlAuOWXxiGLrZRlS2sI3UcKdojphtk/SuyqA1cfs3AgjzXVP7ORTw3eFoiDh2obTIVm1/WvFFHUgUGF/01Vwxpl/H5/isw181FBAhAlnN2C81b2qAnhHWEmcoCn9uDi88UaUo2/f9QfuTtK+CmZrXPtXY=", "layer_level": 1}, {"id": "bd49564f-d7a8-4e71-b915-1b85f3bc1ee2", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Authentication API", "description": "authentication-api", "prompt": "Create API documentation for the authentication endpoints in takeNpass. Document the login, registration, session management, and user profile retrieval procedures. Specify the Zod validation schemas for input data, including email validation and password requirements. Detail the authentication flow using better-auth, including JWT token generation and session handling. Document the tRPC procedures for user registration (POST /api/auth/register), login (POST /api/auth/login), and profile retrieval (GET /api/auth/me). Include examples of client-side invocation from frontend components like LoginModal.tsx and RegForm.tsx. Explain the integration between frontend authentication forms and backend authentication services. Address security considerations such as password hashing, rate limiting, and session expiration. Document error codes for common scenarios like invalid credentials, email already in use, and validation failures.", "parent_id": "e29a5c0f-d32f-4756-886a-0c99bdfe8bc1", "order": 0, "progress_status": "completed", "dependent_files": "server/src/router/routers/_user.ts,server/src/providers/auth.ts,server/src/providers/better-auth.ts,frontend/src/trpc.ts,server/src/types/User.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:39.3968491+02:00", "gmt_modified": "2025-09-12T21:19:42.3635058+02:00", "raw_data": "WikiEncrypted:uxAckj1IXK2TQH//kEdeBPmScopsydAsQUjZb8Fs6jKf9uT15ZqUCVtbvNQPJSwvglmYcwCgkLgHH4kAfkbRVB6rSJgQk/x8OvbtSpO/B5qFV6n/XwrPg+O3l7bRWaSPI5LUBIvpL5sjivkRsq/DdkMR666uTzXVSOj6QcZc7uRW9HYHF5f6TJtQ89jL3e8n2iRDKi9j68eFQlU0RVjUVafmZiusxAOYQcjfStpt0h07iqObNOAvjswqDkh/HWvAMrpDwQlt3/6+BMzJ9Ww9cFjrSYbWnroLEMY6OsUa0RizxeN7oCTXg8ITOS6HgQ+9nBmL/1yEcA9FMmeKtEfe2QJN5KM0HPt1iOMZytq3mZPt93UB0crQjIqsbA4ZULX9DUDQvDWFROi4DJFBpLrim5Rtpq9H/Ciz5XfiVEhZckb/H+dcCE41G2Kk0jezwJhsPPhmepw2767DS8vE/3ohvfXXcJkCglTUYlwzifc9SakWEkZBJ8sjKu++gGx0eM0o20eoop6a1tcoXJhkaoTqJPEKSjebkX5NZy/6pT/DMGjqUqQ2voV73t6TqG4TSF7jOyOEVKykl5Il1SH6EXysRlArkcIYnY4Q1CNJFmqE+iDDnCDYhEHBRmoW5eCep6Fk69JH7FsFEkFuqx3AEyhca5MvC7kqfwUO9G7XAXq9wDi1H0xC8DUYAeh8fU9dOQgVjkViN/YHrjmaJgedH/coDHvZqQ2eyJI9FG5Sbe8bv/R7pWnGCmTZfRuOJJ3ZB/kmgKFfX2ugvMUOxZxALf90EavwiA8VPUOPiLfYrObnzO8ZKDAwRx3iU8lQEMxG4VKvu+G7T8GYlTp8vo8/WSBrQHsz/KIak+5YE0RGC292+1qWdj89mwUx7r/8gF9urvyQZCwejaIdvnKEda+p0NxvJ9ZPCqGwTo6rUCQfKZYSfJGf3pTMKTI2mgl2ZLpi/GxZhXNQC2OicKbvUN2o/+u3+HOOqCOCt/fgxgxwUAJaGFIn/6e+RJyueOVjuQeM+r5cn4YA4X1W59w3o4dk1vv5SimNFZ4FkTRkcrPtGTRMi0KE8/h38cNMoJ6iDwTitQt9MlkurmzJAk4M8kpOwJXLhjiENbXuW5tvD8z4mF4Q2q9depsoO26zbbNzLHRYABsQYWFQjL06NLPZT5yxFGaIbh97Uzvon5uQQ5afs0NjCBKeFmV9C0mfMYrV29xfNbtAHLNCFjaXZBDyfUkLjpf0ySLZvhazML6UZ1sd0TJGBXQQENTmZU2Nv98FiddWlYaWXN6XfAXFmHHc5BQRXAFmKha/zrm8mUSCv+5JS2vah14Og05dNaTgsDPLq/05B+Nq6f5hArTWveBYK40wJMhIhWbNhNsXzLyluY1NvQvufB2vlWeLH55mVWZMeVmKvmlxg3/ElE/xaOnKSTZToMmkDMlAhB2o9Gm9b3ZdFO03TkotuBOL5vjPM5XQWvZXf/kJhyCMYLFFNvPy+lr4vobijaBrpeg/nNI2fjCESxW982gqHUwjyvS19GlN8LzRS9zpos3/NeVRZlZsQysw6W4x0rrTDFpmwHodCyeDOZs7E2/cYlI3tliZCa8aT2KChgmHpprclbFfC41CQ/3xKWAK9P6yBoBpBxQIbAvJGQ6uDM2Zswbl+XIcKuh4lRt9MPLH2FBkfHijtSG0A1s0GJmLVXMCK7yFkoIua53PbueqoXH61eAejCUBtMqhl+4+IZgkko3KhtJ1geTN6D1O2sAzZsXNumy0S69xdRJ6TlgCnYgSx1B93kuHAP3OhVbt0f1i", "layer_level": 1}, {"id": "57834b80-b729-4889-9691-ff6d48c0aad7", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "User Model", "description": "user-model", "prompt": "Create comprehensive data model documentation for the User entity in the takeNpass application. Detail all fields including id, email, name, roles, and authentication-related attributes. Document the relationships with Account, Session, Case (as author and client), Message (sent and received), Notification, Favorite, UserRating, and UserAvatar. Explain primary key constraints, unique indexes on email, and foreign key relationships. Describe data validation rules enforced through Zod schemas and Prisma constraints. Include information about user lifecycle management, soft delete patterns if present, and privacy considerations for personal data storage. Provide sample queries for common access patterns like user profile retrieval and authentication validation.", "parent_id": "58a090a5-c63c-4022-9857-99fe4f06094e", "order": 0, "progress_status": "completed", "dependent_files": "server/prisma/generated/zod/modelSchema/UserSchema.ts,server/prisma/schema.prisma,server/src/services/user.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:09.3141223+02:00", "gmt_modified": "2025-09-12T21:20:57.654903+02:00", "raw_data": "WikiEncrypted:piKNT601qxwAxFbFc+bbN3PdKw8MMByUrHSqD5R3EjuLw5SfWLdARr4Ok1oUCIvsOj4i9M8wNxXX78OPxCgTBb3OmEZ1EUk0WGWb8VLcukqVlnL9quGzQmQDYfLolsSnNpa0DWGBlrR9keX2pnobHNEh9kGz+9Tdtd3JXgQHJdyrht185wRaRx99BXRZ9BaNZBheerbqa6oerUWWEYvhWfj2EXxm0bkE1ng/UfO2QpYfc5cWiZHNbUK7F/mPjg4ht6EzfqcKihBSZmhQbWVyi2CUuNCL1Ls7VGjs6RJepTEglGLCMNZK2BnE/O/jILaGZUIJ0hSp/wEYMTy4MqU4ARS6UDv8y1q2oKDQjdO7p0oFieLrwmKniYfg62uBXyITe269QRZNFjnujWj0j6T+mtm4e+BJOzlJY50uWsY5wJBzjrXUq0zEJKzo0xsWVzHDhbK4ZAVK0k7LBuhNCDBse6r6EkPYXeXlhcv46CAD8RmOsy02PkmOP55Y3zogbcpdAksrBt78PBDtYmNZ5kN5OQNYg3EwCWAm9zURrhSJPUw8BOF3XxxZ+LjsUIs+DkP+I+hVum6mJrw4R4Uyqg2p07VVXI+hwJVd0Co9yeP7+4MHCKm9+1GekqFkJ4fppvReOH2tWHDb4GUEhLncsuq5kTE9Jv11H5UFve9rUQJLfKnocrvyAfI2TWidr5tKL9j6N+V9gY5+i1IhOXcoKKJkgjEEbjUiZpSrVViMWpEZoLBA+1+s9W3pVu77v43MMNfAmp5e9PMQjSJDwv8vNXJFrJ7aplbpj4dEPCMvI1UKGU+aKQZJaV/Sx0ORIoPK03/MqVgtkh6IpIqj3q/ljluixbptPHUBfFU9l9SJqwduoXcZvv2MLGdOrF1sNBQcsRFAWfdzRegiJzSBldn5DdumA/1nUjPWDfKBLMRjYHny4C0e6VX0KkWuhX/wAlyudn/HXwHhW7r32Q1MOArfuW/RHw9vlj480Vt4WO7Os7apCsjI23sLvFxu4lqOfaLEYq6f2G4otDyMCddjXS2iPkjb+flUbuU9JjKbPu/TNHTc40uk3GfS/HmcI3J11dbtQCGq66eaXdkohyBt6YdD00RRgY5Xv3stBWr0q3DA3hVhrws+uz9eRVrv6z+B6tonWOID59k0xPPVlfgO9LJo1zGxtQQtSEeGa8rp7MS3b1kjvY6fIC/b2QWmUlR1hIhEbXL5ucKoZIDDlA7wowDG77+QelpzRbHKYrGeAn3E58wscoaXY12ZxskjBG6AqsV9OKOW3QFK6myLp5SuB4dCzhsEQfaTFCh42i2oHY2Zp8xKznM+aHjOgHsTXTfMaqji30J34eLAa8BejXEp6xAprHupMbreGFts0II78M6srlygX/ZIneHOt0pR4VGAekA2r4m0ltayldh6aMXOyysoO5nlzv61QRKNsRJCQpLHfIq89qV7uQDD2dOOvytlLaiA9j4Y", "layer_level": 1}, {"id": "b9e2cbe4-af00-4ca2-b126-1a42dda24098", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Chat Components", "description": "chat-components", "prompt": "Create detailed documentation for the chat interface components in takeNpass. Describe the architecture of the chat module including message rendering, input handling, and partner display. For each component, document props, events, and state management patterns with usage examples from ChatPage.tsx. Explain the integration with WebSocket for real-time updates and message read status tracking via useMessageRead hook. Detail the skeleton loading states and their implementation. Include guidelines for customizing message appearance, handling media attachments, and ensuring accessibility. Address performance optimization for large message threads and responsive behavior across device sizes.", "parent_id": "7e5065b0-32bd-4cb8-a5f5-554b801c8b87", "order": 0, "progress_status": "completed", "dependent_files": "frontend/src/components/chat/ChatHeader.tsx,frontend/src/components/chat/ChatInput.tsx,frontend/src/components/chat/ChatPartners.tsx,frontend/src/components/chat/MainChat.tsx,frontend/src/components/chat/MessageItem.tsx,frontend/src/components/chat/MessageSkeleton.tsx,frontend/src/components/chat/PartnerSkeleton.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:36.3519797+02:00", "gmt_modified": "2025-09-12T21:22:25.2329154+02:00", "raw_data": "WikiEncrypted:/eDf6iRsWmrowcOXSgrU9sX0xmsBpqqy/0AGgE/3Qcgvhxj4KzefGiVQ+1QBP1B8MAhi0ahmEmyTSwMLn/kRaBSzWM7ABrzke8lQtiWpeJ4VvEaz/teas+/KaH3Y8z17mtME0qHdDXcYzqns4kFH2WP2aIHfPaobM2nnuXXozcahdsGUSFp211TqQ40PTg2Arfi1Sy4757SYxE02jNs6WXuW5HWOsNAWi5ZP45/QONbBG+BiG3t5ylI4Tcziet9zBzWDMoMjGplEDHrWu7/0LKibNRHeNQ2YPArssOtS18LPbYPxpBKTBboX9KlDD8R/6AuzfNTlnFCRNQmBAqKFLctjUlPlH3j8so7e4EfSNPVhlf4bXEBunF6/RTfJCAADPbOhSNs0RfF/innxeDsOSG/AncdC0knxny/+UIlz0EDZN79e+7l8uvE4vTzupIdR6uAYuIH7EvMOy9oA8i+XxKSqfpnEpIrXL2jWXND5UNDofyQjppP5acAb7l5hq6xSNqhGZOl2ONEbqU4wMreycsjFAJ4/9TbxUmD9KZOIJtgzliqnXDSis1U4hkNqHLxJKGJmsLXrEEzltSdyiIEFx5MuSFoR69NYkJPapYvicUDs6XnDJKkgPo2nFEJf1zG6QepEbrMPxR4Bcn7+NGEvuGChb8b1dU7u8Ezfp3g9KglIy5zxCGziXCVcxbXJ4OYaX8kQXMhZrMOy9wqM4E7o0cHQFvtaBGbnDJaJWIT1ikByysQZ1Ety3r/46Ze/KlbNc59cQr+owWHB4Rjfi+2mnwQopAgSavOh5dN6KzeLhcRDBw4SB/P/xe+lk6sANWLcq3jNzFu5tM6Quw2mJCyB7da6GqoLB6zmXxIiLJpunNCXoXEGBdzBhsum8y9qdVwCnOrnvt4dR1pO9+Tw3VS7NSukekRGLF02dE81az4LTzJEhlggOedAp6sUZeFzSAMuNopB0RrW6zqeQFIEP1xCsRLaiFeQXAOaVTfO8baJ4dDQgyyLI0VzzmOgJSZ5TQ3WKz04zyIjMyHEcpAo220YUyuD/HFwPXMfzaMvoKTwGV8bObiH3nfWQhmpj9tkGwA+ATBT1ZbaTieXFsED8uViouIFDfxEmm64jomOcNgEJe36f3iv1XJOFc57n2K9Z53zZoP9TMvoEi0T7KMF5sDyaJIgYCGnL7qmUGqwOK3Ascwma5W+dGlKki/XppOi9TW0efEMLIS5cNbZ4eXxA+qVfVpRRfUWQtoSqhvleHWMa6y8pX2ZDvqfufz4z2SbswvPzuTSZH88c+BPKcvmNS3qcpBLFNzVd8KPbEFOcylh6qZL/Pkoo2TREjfSl6PEg2GIzMexY5ZXo/4TadJfoevmeEiat5YHtlxWq2Zi1iFfO4gJPzlCbz+5JRzoEmYt6Dtt3hu4nUZl8J9IwRfI0PASWaD+03NjOGEBL2WsyAmXIH/vhTV3QcQZJlRRvwA5jIFgwUG05EOoQt9GRJfdNBBUUcB8RJH6CNRsR9EhAPxfdGDkvo/yhLDp+3ZyxkUa1RynspSUIKAhqSxgemr1ntAqtXftYDATMtYsvEx9s49jsQ2UXMD6x/CQ+m0ALEbMVoC/SopGs8idOvkG03VMifJ8H6AHgicRWjXFQogCRr1H22xmtN+t0GtIoQu6MXLl/Yis862Ch3CzlDzpNRHo4aQQ8A==", "layer_level": 1}, {"id": "33420246-d2f7-44c2-af0d-76a64deebc50", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Case Router", "description": "case-router", "prompt": "Develop detailed content for the Case Router implementation. Thoroughly explain the tRPC procedures for case creation, retrieval, update, and deletion. Document input validation schemas using Zod, particularly for CreateCasePayload and UpdateCasePayload. Include examples of how frontend components like CreateCasePage.tsx and CasePage.tsx interact with these endpoints. Explain the relationship between the case router and CaseService, including business logic delegation. Address authentication requirements, authorization checks for case ownership, and error handling patterns. Document query procedures for searching cases by route, status, and user, including pagination and filtering logic.", "parent_id": "************************************", "order": 0, "progress_status": "completed", "dependent_files": "server/src/router/routers/case.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:59:00.4809991+02:00", "gmt_modified": "2025-09-12T21:53:14.7098886+02:00", "raw_data": "WikiEncrypted:1qhDLyowSyWs2zS1kT1oubvVijNmleXQYKexjykmjSgrVaG6cZWfyHwpcX8m8Wa+zGPLMiGsnbchlqpvaQUQqcd91o8RNM0xbPveO98w+AhfcwE2qViMnLGTAm6cqjX+GR/j6+Fe4rbSZKn4PLmdijncpZ1asY/lBoXu5noeFaA22qpSuy5DxDD2FfEisKobdwNqrbrJ+rzEqrD0zODEROGcUsO9O9+WSRH3xK553YCM+K3VG2rWZ9ZlbWyxXsBW0AFRZQ3wv/kUtnv9LSVHMb2enK6EXypnTPv6ky3GfkDQilNJ0CB24aUC/GVXBzLgrTPrIMrY+JxFsAhBfoa3SSduzLqX2PwtcTQ5Rdi0HPE4m26icd6hKggU9iZRaD1M5F8uDWOcA5qBBSbknBx1WFQki2WnuHmZYRRd8+cE3JDHkjUVYVK8mzUhEuZ+kP2pi/IgKgfuFSahMPN56mnMPYTVp4uwkfAClYENBvp05/4+6svrwlpZ+vZmSZsz5JuWFOgUk/I+5aj8PXo2mRbpmNGnFqeKl++U4c6aZCMVcb8Pa7WlUu92QbQoNJsofzSkfle7z3t4QBLs88EhkK4I9HuhPBcDRMMAxa7hESCgCfG0tYfn0nVw+Je5kgSBW0ZfIWAvDBBInGCLb0+O9L+3zxk5sYu5vcYzUHJUGjfw5qvf9tCtVTSVnQPcg/3KBMhqOY9YOg3uBnU3Cib8z95Zeaee+CODIhvfEm57oxGYGDEjGzmxlf23HgLkT1ta3DH1+qFNS68ArihW+Sv0sYg4lXVmn+3ZTgeVEkKZ951AAJhwfohRKvvRMt+TNIxLgoiOeCon48XqKV8iMeYgH7CDPEpRUCT68btYboJRk588rYdK4LrowEVWqTX8HFGaYSyWx8fauPZXaevulSQdONMACwNtD1PIe9fjU/Ad7exDgoF1Rh9sDtbHHAg2c6KmMSAixiKzkdpGG+E5HMbzBM4wl/KcGvHFB/ekurWBd3iWZWB33UclimDc/I1/TPEmpBkLrffMcJu2ePeXmw0kG3mNmPsBepW44onyA+cHsLtmkb38nqc2q/p+JZyqOk0MvsCr24bTpCJ+S58SFh6CCNBmo09TEcqhywpKES7I7zTAKGpu9HzVYQk1pzZb59w26kVQXz3NmiV8rkWO4F0UoQzPkxw9nnHP/5G3uOSpEB8lufJ3OEmO4p5P9ZATc6MtOL6NZ2FtkchhI19NzgzUG1d+6Whkn1GtJANJ5xCRIaNInjZ9dUtXWXdKGKZj8oQ4i0Fb", "layer_level": 2}, {"id": "ae7eba85-2b0e-47da-8ffd-2f140b1aba38", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Case Service", "description": "service-case", "prompt": "Develop detailed content for the Case Service in takeNpass backend. Thoroughly explain implementation details of case creation, search, update, and management operations. Document the domain model including CaseStatus, waypoints (WayFrom, WayMiddle, WayTo), and relationships with users and favorites. Include concrete examples from case.ts showing business logic for validation, geolocation integration, and status transitions. Explain service interfaces, parameters, and return values for all public methods. Address relationships with Prisma ORM and tRPC routers. Cover common issues such as concurrent case modifications and their solutions. Make content accessible to beginners while providing sufficient technical depth for experienced developers.", "parent_id": "d12200c7-81fc-4058-be66-aafb1e37368f", "order": 0, "progress_status": "completed", "dependent_files": "server/src/services/case.ts,server/prisma/generated/zod/modelSchema/CaseSchema.ts,server/src/types/CreateCasePayload.ts,server/src/types/UpdateCasePayload.ts,server/src/router/routers/case.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:59:38.8956933+02:00", "gmt_modified": "2025-09-12T21:54:32.3112039+02:00", "raw_data": "WikiEncrypted:vMsgMax61wW2sTWhXd8DH4vbdb/SHzgHhVPwqe2QEET1imFHEyJikozTZMTwSqJ5qatHmwTWE8mbGoTQeMdjrwxkIk0XB3iq8zaChopdXnDVgAiA4U4iP29Da2aNeYZR55phQOo257FX648YJHmMuO16LAY2fg6mBLoys0zOF3fwOwqaQVlpSQP2iazz3nkvAn9JtVk7hm6Cv+52nnB0lWArvdHI+U9g0gZSL281vL92yKogOdl1rVFxwf3S78nrYKZ0ASgXSq4CQ59VQCIhnjC7bvlrap1koyBGf7Etu0uWiN481oC/TjymU7lCXFyPMbTRHvtEWus7enka6SNrcoFxHORrTLP1hofikVBuNLS/x04WKl5pZfHRHU62zYWFajlMnM/ndz7P8Bun7nTuXWCpZ1sjVvGZKBwR6UXC31tTBPpkGY3grbNStUh2XVT2woXyRmX5mdgv2VYYugjJxTwOSYl++CEJWZJvVs8q0jN1R/EC0nvXgSHd79e5+eUSIK0AISm6+B1J04McZR8YtKTyT621Ab1yQ1w8MYaVLM9eEcwTRXM8Q+TQBZWw5Ndx6Y3STRcD1IyYnYCsbsIiqoCOGJmSqSWY3Kr3nUgl6ETgpGm9NMIK3u3HATmMcYNmtP6/ipLTtmWHnKWwPwu3G2kLrMQ951L7JUy3gGSyA1Gzi4l2egfMsdRmzLDODNa1Y74H+lNT2it64/vmKtBPKH4AoIvG54yFrpczBUs2fBr8i5j8TMO2G4ZIR8cPBTf0ZFKle02VM37DkoHw11FIlsuFKDGdQPFGvb/FfAge7NTYvG5qVpzgLgzH37dEXI4zZduD1OOw/Mm9n96CdloTtrxOihQDO8Y/INkfx86xeyD8s+0W0oHDQf/afJBpi/ebXZ6hvf+7FBmYF95Iy7T8XIwQ164otw9K0GxMJivBcxJK3dMczRrQDT0vY+v2mi+b9i5vWPUXQApVzgHydw1L/pYPNXnijIdW7O1zFUOvHHAbaLcbl3dmOrJK7o7Fsc2DvwiXTE2DS6qwoezhd/pCiSi8iZIPEdqMDAbcsShrYtQi9Vq/EEZubNpizX+riDITsgXueZfAuzR5lPXnA+f51rEpLs5o60FdUHp6RxWmeE112TGA3Rm8vCslrj3ecpm5pK5dDiEMvR8T0RvRocrCK6DKA+LVCiWFW/z5I4ChWvD+US7dOA5U8QskR0bc/GJcENO8/Qs37++D+yLslQeOOsJ9MJWP5/zpndLe2dMxJITbRdRLTnLOV0oyXqjS+v7jxJUEphf8S+J5YX2L/mz6GYJjubD5LP5B6N2j8jXe40PI0U3L5F1xlZUAvitMxFQKlLwqRB96yVZ0DOo5MNpwvgJuPhL+BfkQOrDNZKe0jyzp4rMEtIvHCUA+G3xScg+omkfXNFNXN1xz5KYmuReqDWGQM/oEIKE1lOnqH6MDKW+8YdU+eTUTJp9ALWSJn8HVEsF8lqsUVe82ZuMmE5P0wxy1mp+PhpXufQAL0g/gWzoudOHsvERQfpIjpFAntktUEqoRMk99yy/QWCpWFUYR4jU/ncz8c+YDl47xISNv8EU=", "layer_level": 2}, {"id": "2733cc7a-6f67-46e5-b229-fc0084ac3bcc", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Technology Stack", "description": "technology-stack", "prompt": "Create comprehensive content for this section focused on the technology stack of takeNpass. Explain the rationale behind choosing React 18 with TypeScript and Vite for the frontend, highlighting benefits like fast refresh, optimized builds, and type safety. Document the backend stack with Fastify for high-performance API serving and Prisma ORM for type-safe database access. Detail the integration of tRPC for end-to-end typesafety between frontend and backend, and Zod for runtime validation. Cover the UI framework choices including Tailwind CSS for utility-first styling and @heroui/react components. Include information about testing tools (vitest, @playwright/test), WebSocket implementation (Socket.IO), and authentication (better-auth). Provide version compatibility information and dependency management strategies.", "parent_id": "", "order": 1, "progress_status": "completed", "dependent_files": "TECH_STACK.md,frontend/package.json,server/package.json,frontend/vite.config.ts,server/tsconfig.json", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.372497+02:00", "gmt_modified": "2025-09-12T21:02:08.9156692+02:00", "raw_data": "WikiEncrypted:w0CkdCdnXCcvlN5xOpiEhEX3U68GI0Ngj6azFMJo9xSXD5adKnu6Qo1t8CLnFs+ZP+IW+8SVWDLlYtdgYFwxhH71pkcOW+hdMLvwfFeRNfOoVeAMpI7sQ3ZOaqXkj/Wefg5kWk3Xi8t9p1me7zlQsYH/nn7gXC0efynYDDT2FkHEZVxNp2iSNSMG6j4+hSZ2j6kUKCgbCQVZvnFBDW93lh5fIQQwcVXrg0bYhV1KRKhZ5W5qivS7g5upaZnArWrTnASgdoeYe/W3T0lxjM3dS6z2iY2OkYXOwBpbvvdDmldMGHHmrJiUibUyG0FAAK2CvobOUiwT0qlFXTxFp7DwNd+uIpImfia5CtQ6g7HT7CMYWEVr2ZjvNPe2bc6JNIRsu7TcNcsJmr9TYELZ/q3ObfQxS5cQbIeOu50MFcFDo1BCnK43lHSG/cRQsMwiGpzULl6P9hBTvyBNhgCM21GeBLhE3Igw/W/nx9Z6pLHa/WSWqTrIMhM8pWuG7aapLglDqNMkXoX7M5N2sbbYfvnhe4fKwV4rQOuXe1/0GyLVqM/4ACHObYb11X7n1IMs9dZXCxNUiGC84jJu6RamttsaPIZLaLFfUUJUEATTCnsrnP0HthKQ7DS8lKgyvx5AxnvUVw8AcV8Mp4hU2d05aKLYIWSWg6C/dfyi7GfwC3wOE+ZAtv8bFFakQVIsHtY/WyE0HJT6BRPaxDx/DvXMyHyfWToavjUD74QdwyHctklOdgUgSOpxtfHWjvLvErx2kuTLl7s0iLOptnegtxcp0yKmwFO8OqHqKXkK/fVmW3sL2T2xKTAtwusgOA0pORJuTckKKaS4gk+HPD3KAGy3t9ehsm8hw3ImskuJLHci+8jyfRd1y3fdpjS5tpcfQ+Ghzqx7YTBvfDfyaC9GLONr5SbeDqVuAC4EiAY5V+xd2Jf3KSjnld3CzJ4SiW2gAshLmTJH44PzYO6ksOJMdZlGkCuW0945JNQQYuDM2GrquU/T2KCfh+c+EY46o8Zzxrv0/UGu/6yAKCVoBMrUG/MINyFaCkf1gRu4So0k93YfVmIiYE4BvEsdHrPjbm57PVFHRaURRTetr06vsmJFZdIFhXiWbKikJDRMoDynxTTjfqt55vHlGWip2VGSnNQRfkD4VJn3WHNDAkFD563pEdQY8dtDT+mzvuU6iyUiO4i8gxO9m32cwAW/8IzOORE2aQdH0OKhdS/OJhiGsPKd3qu/r7cpMCoMKuvgdWGk/WXww5Z9Rr90Ycvk5kOW4d6SpnZOO6ZEbihlsR702ncj2XB1vbQeFcNdUdSGRR6SP0YDw8yDZcfX6ecOWoCHF9d0rqQULjtJ6Rn0OchIZSmTaM4gMee6aSXpttll+jhXDMZBcU20GYYTRN5fQCZISJwBGqN0xrkd6eANf5CXrQx7gkUFi+lO6rAB3t9uUeRVWcuW65vl6tK938RRDG2ZzZgKwtQWBEKvkYb1Xybiahn7kjQqTIr6hxGJiLCVwAL/cQhfx4rqbRdTTC+1Lyd9RhrBtzu7INhi10CEUnGiEsKj6KSFGg8H9NjbgTvL9IQ/L6Kq/gb7AnoL+m3eIiah8gtTheVahKDq", "layer_level": 0}, {"id": "************************************", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Routing Architecture", "description": "backend-routing-architecture", "prompt": "Develop comprehensive documentation for the tRPC-based routing architecture in takeNpass. Explain the tRPC router setup in trpc.ts and context management in context.ts. Detail the organization of route handlers in the routers directory by domain (case, user, geo, message, notification). Document the procedure definition pattern, input validation using Zod, and error formatting. Include examples of defining new tRPC routes and extending existing ones. Explain the relationship between routers and services layer. Address authentication integration in route context and request lifecycle hooks.", "parent_id": "5b381073-14b9-4b74-ba3c-f2ceb3abdd90", "order": 1, "progress_status": "completed", "dependent_files": "server/src/router/trpc.ts,server/src/router/context.ts,server/src/router/routers/case.ts,server/src/router/routers/user.ts,server/src/router/routers/geo.ts,server/src/router/routers/message.ts,server/src/router/routers/notification.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:13.9034474+02:00", "gmt_modified": "2025-09-12T21:23:32.0312208+02:00", "raw_data": "WikiEncrypted:N3UbzwGjCC3xD6BrTM0cq50E8+wYTSTWa+Wt61CnWuycZ9idgcq25CkllNKYoAH388yOo5lBp30VFRcM/Tkze/nXUcOlOg1gIODF0kPN3FLsW4CosYMLN78udTs8tLTc1dWseFN7sqDmaDX7uvuWGw8pHAxNI03frUWIm7ZmThR6n7ZC4jnXaO/bNVdrbZZWEM+NbHaXev++8ZjxWgF05Yz5syxmE77gfYctad8gQhbkPlTo3/TT3sXtcEedrqhPZgZ86aRgiF7NufWBcmHtlLJU9wGTOQGrpwPmOCKGvrMQOYvp8FdfLQv00Y2vpdqn/1PsyYsj6axQVoGH5cwVAV9joOxwe+Zk3f5qWDURG9fcAYPviepSvlL4qSRE3+Kn0iM8wNCupYe2WA+8W2hyjrHhDrGWROnTeMY7NeRgVuMIGTFJhzCQmiC0CSrpIUP9k5onmCud2qKFLD4GlW9Be4vZtxAH3He/Y10a+CJjUiYfnYwFA+8Vud8kis2A0gM9unWSP4GiUIV2/pRy6AUtFASoQLZvZ1YGPVIA4GcjYJTQxbc5/C0+CRhWnCOS/0PUM/R85ZfVLa+xEHZEhwHf1aUSl7qTq0Ds0ERR9Ick7293HH40LGOcH6gXfupo+94vVdRr/pAPkY5oywLFgpr+ibAgx5zXA1GFiUtgw1bvz5W71qhzRA94VqaFnc8UR6DCNWWINjrqpmY/0lcUdj1xlQSplNGocf9LBzzhsF+BJ4sObLGoHRyfFYbyP5B5FE2dhonOfH43mY3jibl24gWFkD0iWtCJop+0l0lb/msr2y5Fsz9aIp8AQzn7xOkSXAQ0a1l2Nhq7CtvKuJjajYvMQiW55QlroaQo5kEXD2nWxYTD2D7lTSWtyNh5DD7bF8dkwxk4F7KERbqqvwZgE1DORTscuUPNN5H9+bVtUKe2Bd6/d9n5RDEzvV7nk4YW0GXm0NA0ER+izc9QWGEYgliFdmN6QyWOfFiZ8To4hH2MTEYc6X9XLLDPtCAKYG+g1QztSCQyE6FZ1XhIg7H1W15VMo+d0wiFxcbFsPGKkZAW6h7QnxK+w96k1FLqE0Huhvgh/3mKPoS/qen4BI4ykO2EIAeklBRt52KKU0l0x4b3oh6Sli5YwUGWNGpRyWSISe6JoY/PNpUDfsxODdYjFzM40d/JCn+fzQJhVSo4j/wP4tunmwmcwhqUu/30a4kSmoMy2hAzuhuqNMckFYdTSHnSIHHscZQRkQM/IsqNE5H4j4ux628bjgHZDBNOXQL5qITVz0bQ2QtnIho+OVQKjMDEGb7VFElbgv5o1jur4j4PT5dvEvt7MDPwZSbtOLPQLA5I0xeHZRJIDKY1bzJGBI4lFOXiYL8/YJCJQbAUhKKNnsWtdfM03qnfpcUiOxeahgcC1gr0JYPScs5ISVwWsSYk6WXR5Yy0muIX7O/2wTo1znOVtlBX+Hf6aJGTBZebjFwBqmPiVigWIPlCUO7La7IfB03ZR6dG/J38HavIKsPnnKs=", "layer_level": 1}, {"id": "934ea81e-5efb-420c-9f8b-9aff2dd9ec2c", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Case Management API", "description": "case-management-api", "prompt": "Create API documentation for the case management endpoints in takeNpass. Document the procedures for creating, reading, updating, and deleting delivery cases. Specify the Zod validation schemas for case creation, including origin/destination validation, date constraints, and package details. Detail the tRPC procedures for case operations: createCase, getCase, updateCase, deleteCase, and searchCases. Include the input validation rules and business logic enforced in the service layer. Document the relationship between cases and users (author, clients, favorites). Provide examples of client-side invocation from CreateCasePage.tsx and CasePage.tsx. Explain the data flow from frontend form submission to database persistence via Prisma. Address edge cases such as attempting to modify cases that are already in progress or completed. Document error codes for unauthorized access, validation failures, and resource not found scenarios.", "parent_id": "e29a5c0f-d32f-4756-886a-0c99bdfe8bc1", "order": 1, "progress_status": "completed", "dependent_files": "server/src/router/routers/case.ts,server/src/services/case.ts,server/src/types/CreateCasePayload.ts,server/src/types/UpdateCasePayload.ts,frontend/src/trpc.ts,frontend/src/pages/CreateCasePage.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:39.4012329+02:00", "gmt_modified": "2025-09-12T21:24:51.4584953+02:00", "raw_data": "WikiEncrypted:tQGx8+s81Ws0DK6i+DDeV66J5f2RmTf+UCSJEdoNyG8Xlisb4s4Qdwnz9cwhkfpUSJwCyONUVTinxX2jmO+NtA7ClnG/9jF2/31YKQR9FWPWtFFWtn0MtBHkvXR7LUp0r/syv1XkorEd0NtW7oCCfpbfT/LV70+bN2YHGTeSZT51kNYD3gIZ2edDFlFaI0TQpN6XxH1nFZJkjslVPJuzZUBDpzdJ8Bc/is9buiMb7mY1Y7azq1aZtmEyYC0Xu25ra5WwPUZZjcknKYtxN3hj3wNV1ksxMWfHuGH5oqtrl+3aAMg6KoHdGBXxU91Nz9TzVoyww/LtozQdR11nXrPgEGcqCfYb0CCKbJKbbrIbWUFwfKHAo/h/d/JawXJffoN4fjPWHys1a/N7Kuwv1wPRoqNJOMgFRTpZTJcOgCPKX45YctiejL6S9oVP1Q+ewnuP8BlyCVR39nn37rXr9EYsmkRpH1UJrUDZp7/45EM4np1BiX9fJIOCGc1UrY6ABuUlHmFbvfVhFTrJE5U3hvAor577HQhoIcsly7du5KTPNtqpe3544+4fxQFyGzBEDIS1jEieBFUxvUBubxFSt9d7hbs6QTc45uvWBjZFW1JP760WJDb6PXtSREqKY5HA2AJknPg9bERN24rvhaSReYGk7t2V5o1IFBXXaIjzFY5m2NItF7zHyqdUj6uIURa2P3LXLxl5j+Ijc8EsK1475pnx+LQkC2g8t3r5hiM5nO6MWmzImjjDKlwNQ2Rat63Enn/jb24+w5/FNX1p7BjQCHT7wawJBiJml6QjN5ZegsjfGP3DZv4CAihqzxAvR49FRYLXAPY/AzijZCN+EPr6sYu4/edzdFAM51NKrnXjm8Y8aPKhRDTARUR/onH2iIJJRXx7e4k0V+3OPF7FisxD8qtsLScV+/cujIVXjdYHpBMvpo3Cd65YNmRGLcG8cRu8UFARTENQz6a+rgV08scxCuHi8yt9Am3a4Dlw+UScQRA+GdhGWatYVzVL539mhM70urIkeZCUbMeKkOMaf45yOcrF6MGwR3EXtjE6zwpvU6w9maeCY5wmFcZBHMvGcwrZymgaXG87kZvZzl8FYKjH2Fwk0pbiEgiEkz0GjO3DLzZ7pOVMEoxymxkoaMRu+HipHKwf+SyQ2Y1/mYLfJQ9uRC9k3x1wHfCNcuh6Rj8tPCm0qYY89zi3RmQiphow1A7WhNQa9tiCp29V21gt0St/rGClEz+HD8Gb3cavRdY44K7unsuqqFo1xz9droI4f52ns7rAlCKO5wWbb/f6suUlsZR5NODL04/k2wxzsot3uSAdTD88JgWs1yc2DoKT71b0KMLqc/bGgiGd2GTD/xbTH2B0lXATa1Bc0A6Ro9dkYLVogaTF7OOCXHzf/U0n/vA813d3wrW0HLRkKqPhTVpOzu/5DlF5YITFqpOwX22FQ2zQr5sXKu4RASA3kAc/OxTrqFifw5CdFtovQFLBDIo1KboeVLCa6enLZFqLJg0mumlPaMyrxLWNm6FgPs9kymB3QLjzkMXdGaS7Jt4ijZ4WfpeDrwngqyvewLVnQpaPJPD3kemHUaS5LwPBQoQfuHY6I6NY4/s74NDEc/ZWjZJXRtVhkqjchbEi7xVDCLn58dZ221n48IQhA950AwD2HFXhOpxrVMPQsi2J8Yo99BteTUgFxXoHuBlIPt0cj2a2YVjOdgNtWpmE1BeIP+3dbMG+SsoQ8qhEFWMUobyVNXd/CccYjP8Nne5v216AfrCbGXEUJnRgQsfLJTk7LH/RpIphmKxSajCKw5rGM7mEg0RAuHopHPwoOtyAOScMC1Z3Q9KGCyrclCMtegRInKF1GC67qa2D", "layer_level": 1}, {"id": "0254afa8-3eda-438d-b049-a16e58e39e0c", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Case Model", "description": "case-model", "prompt": "Create comprehensive data model documentation for the Case entity which represents a package delivery request in the takeNpass system. Detail all fields including id, title, description, status, price, and timestamps. Document the complex relationships with User (author and clients), WayFrom, WayTo, WayMiddle, Message, Notification, Favorite, and UserRating. Explain the spatial data modeling for delivery routes and how multi-point routing is implemented through related entities. Describe status transition constraints, pricing validation rules, and business logic enforced at the database level. Include indexing strategy for search optimization by location and status. Provide sample queries for case discovery and user-specific case retrieval.", "parent_id": "58a090a5-c63c-4022-9857-99fe4f06094e", "order": 1, "progress_status": "completed", "dependent_files": "server/prisma/generated/zod/modelSchema/CaseSchema.ts,server/prisma/schema.prisma,server/src/services/case.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:09.31809+02:00", "gmt_modified": "2025-09-12T21:26:12.1935558+02:00", "raw_data": "WikiEncrypted:tQGx8+s81Ws0DK6i+DDeV1DBEYpn8DnbZrl/JOBwT9zNID8GeZCMLdWDV+0TWUZEBV3e20Gg3wgh8N5g0AbJsSmEiDEqwNpHCaBPLG76jCCgg9MOEJ4FBOkTHYIWmW8xmfvDBr2L7RXAUdZSqCgcZhAguhQENeko381421dQv/zJ7f30PeeBRsGAyMWvJhRCG7M6S7C+rox92QwHz5L3GlIa3dKmkCgtpXdxD16/X3L1GuEHDZFAHWEZKGYAdRCzEHFbeli2YlSja2LCz0tSvbjvY7yMCgekexFhXy62ScGGaJXP/Dznp6sN2dzTQeSI5ezSQe1jPU5Cieto0bUmNe6g21oYX+WtU9l7zc3WVhG9lJTRh1XSyXSGLJXfrX+UD22nBlSUBU3tzOSsXITjTFMnvwlNooCL6tw+BsAPgyFyOgCClMxOvZjUUW0xz2jeLyUMKIBR2U5lMiPPEy4d0AqJB2NWJh0Kaw8A0W/njTfmhzQJAbylswfqwc8pJVkBRWNSAYiv6TX76hPyhlVjOKMYaVbajIkjpsgwiPZ77vypJimV17xSQbA9RlcR9lH6mPfwDb+c5DZzd2nqhcNQQrXNRDdCKndGSXYjqnUXgIGmfeGvZrq++sqCL/ro91Y/OWRs87Fz2WYZFHSoIUNWG9GaZFzaOyK9HSvg/Qc9QejK57rijUGjMZSJzqUzSkAJWbF5EctOEiU3l8NBFkubFlsa7wf7puYSgesN2zCMwsq+y052BSZVjSHJi4AOnNjL7ik8lk/ZkeWH2OQMA8GZHZApoMf0LYMAtUJaUdumX9rRUEBmbKLAlUlGVlfWnPEbHjJ50OGVu3iSlLMCJRDGbfOcCIqHpjSob1UOaxsd+fJrHdalFq4h2keKOuWzT5GiFZCTAbbvEtYNRcg3LUggp+0FVof76Fras9IivnnIFGdq7BTq2MFv7xb63OX/qp1x4D8eAiI3okIcYGoILlzuYOAe4+lz5VQWpYrIqyJ4R4D1rcSFRDQRE9N+xOjpUOGd6DXwfYsVshoFopo3iP6itEFc+h8Y35oXxtp4isZ/yymIsXhmrqNpe5i1a/u2ugKZm2a7Tm7QPIvONM2kzd5JmrGNTvmZWmRAscRJV/U2TRnKRm1M7Su5JOZyQUoZNZ5pLvq9LCoZJORPtJmcfLszchpQi/+ev23lhKh/QKzOnw+RctKwTNVM917SK/wvBDnr94yZTZzbiERghbCw0CrwTDzErfTLeTN05QNsVL3w9twXPEzi34gC2BT4TtRLvO01/OXmVaJCQzvieQerhBfOVd7jPkg0TDy3FZzs/cCLuiB1pKmRYPweGgZ3e+GMiBqqjnQPi+SLN0L7QmF1WcjZgpjX/w4yeqcezhKBqwTewYOAA4iobaEWStQR2mzdsxmD6fMrihfQJK0H3zRviGKtBgU6WGnYbm79LIta080v3Ls=", "layer_level": 1}, {"id": "5a15574c-f3ee-4acd-8a76-59875c33ef2c", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Map Components", "description": "map-components", "prompt": "Develop detailed content for the map visualization components in takeNpass. Thoroughly explain the implementation of MainMap.tsx using Leaflet, including integration with Geoapify for geocoding and reverse geocoding. Document the component's props for center, zoom, and waypoints, and explain how FromPointDot and ToPointDot components render route markers. Include usage patterns from IndexPage.tsx and CreateCasePage.tsx. Detail the world-map.tsx component used in the landing page and its animation effects. Explain CSS customization through map.css and responsive behavior. Address common issues like map initialization timing and performance optimization for multiple markers.", "parent_id": "7e5065b0-32bd-4cb8-a5f5-554b801c8b87", "order": 1, "progress_status": "completed", "dependent_files": "frontend/src/components/map/MainMap.tsx,frontend/src/components/map/maps/leaflet.css,frontend/src/components/map/map.css,frontend/src/components/ui/world-map.tsx,frontend/src/components/landing/WorldMapWrapper.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:36.3561804+02:00", "gmt_modified": "2025-09-12T21:27:44.6108204+02:00", "raw_data": "WikiEncrypted:uz9lX+j6LrYIyKe6mIzR7nEB55ykKtZW7HdBDZje+0nJ85EmADzdcgJRLcvcgzCgy34PffauEYn6IorLjHuurhZiyl8Sk1wLz4+iT0ot0Kwk1IzPIcgGgZmrnsRXAvICGWtGNGu8UZwYjzl8nz55Dg3sxXA6xUutSTvj2fqrTxpCIa9qqWR95wkte1PlXqJ42zIg4nOckI7QKFblDwqAK3KKfXEm42MIHtyacHaaftFG8TVgsIvrs/5kmiMAEx1P6N+3LEloTm5uYv5IxrN8sGr1jNq7P1OdRBUjGBqx4WA3iHwBzifmJFYfUhPFR4swIHfh4zaBtQ40EGj/qtZX3rTc1A1GwWBwPIx09ZFn+xCF6vYasFE0gXrkKKnaRhAnOtR4KsvhmPsUdhSCjZ1u86tqhwtmcpuG8wmc/vTg8LUPNcrwvQIrmyiDjacRa503/sDid9SG1YBeROFHlCRhBOh31Z7bgDi5k936rKYuozGpkdYBMgXwBWAZGZjdaLM47/RzIYUmB5UgtIFO+oC7JcEzN0RBCuSsy+g+SVDFaLaZ7igOUqpnY+bn18MJsctSPv+SZcZq6lHvRSV+nzywxcscpbd6iYcjxhZkkDItvQXaTaLt3ZnYWBuOq+0rqzVRbFRB7Qiu8YR2m7zifj0rCXU/tBjsKMtdPb9Uw2euAKrFa62XuXJ1Ejb0Lo0guBVlxRiNAVzDemfOKCE94CnV81UNXJPyt9UqCkILH+G3iNWonZLKsJexlziIXxz2UcqERhp+Kx9gLehYoZUs0jPDIZi16LuXv4vQ8XId5p0Z1x3Yh+JA3ynkLWLo2nfxNU5Pr/w8bccO2oGBGnvXDircNXfDto2xBJCLSImEwB1lJ+Sxekcps+Cj9r7aQoI5ZaEJDPcqMGfEmFJiDbw/44SLvLQiOtL52+E3uV6tlhG6hEom/NvoxV8EH+HVzgpPdpGsa9zKZR63IieC/Z+pi/J7JTD4xiUyjcjddkYSZNwdV3ju6Gf0fTTLTPjmclN4NDNSicb7HtHXbeonjDGx0i9Un0CdhkTlnK4QWDC9vvTdMD/1ukG29NgzjRS32Dz20JoyliPvlOquJ1MeP+jzu7+pmlo607HxhMoUtjTwEDQ5SP0cqGIzQU4VeEoFk+BLGqNijuTIKo3woeW0y0+JRTBkjP3TAJ8+1pA7KoYcYDaFovq4hJJnvcrMHDc6zg4DJC1bMQw04aMeXDzO7BKZo0l2xp4CKj2xYHHKB8g0Er+5uC8vTtc1J60sIulvT+xw+XOhEa7OPPcgax7hQmwAiXYSnCGy7gavDuWg3TekTlStQpB5/aVMVS+7Wb1Q3Ln2sEe/E3twucAk9laDDxrHcBP40Wu/64btghR5/kVFgvH3DH6p96lDEZmGFBTuR9iYz8ZW+KZX4kyhpnjCsanPL88ASD437cmk2OpscIF5t+KIMUAyMCDWd1351tEKQR3SKVlN9AeQGumGi2dzOyQoImmJLtwINVriVeMGlzBGorDDNRw=", "layer_level": 1}, {"id": "e68b91f2-1bf3-4b79-9679-ac5edd82492a", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "User Router", "description": "user-router", "prompt": "Develop detailed content for the User Router implementation. Explain the tRPC procedures for user profile management, authentication state handling, and user-specific operations. Document the separation between public user data and authenticated endpoints. Include details on input validation for user updates and security considerations for sensitive operations. Explain how the _user.ts utilities support the main user router. Provide examples of how ProfilePage.tsx and SettingsPage.tsx consume these endpoints. Detail the integration with authentication context and session management via better-auth. Document procedures for fetching user ratings, cases, and notifications.", "parent_id": "************************************", "order": 1, "progress_status": "completed", "dependent_files": "server/src/router/routers/user.ts,server/src/router/routers/_user.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:59:00.4870829+02:00", "gmt_modified": "2025-09-12T21:55:43.9752569+02:00", "raw_data": "WikiEncrypted:Pe7ymNK0TAICzgtfhJtjWTtdKwBPRo8UG2moS58RdhQAOEbMJTmYM3ImY/b9Mgop9QvfftIOlqKRNdQdrvFic2t6MWBjEQRw5+68r5J/vtgvWAl58jAEn3ojKbgxt+M8uphMg6o0TkR/QVwiSqs1AkaBbhmEE2CBIEzN844sknS2MxsVus3qI6bnwvf2V1IAN0pUQfpBBZdh0Qkd63lg7l3n4UAUqtMAkRa+/ooLFhkWXPo1cXUVgQ1N8HTn8uJmG1KuhWsbaKmYFBpj2ohhXkMXGn7SBEa/+dz+6kwaFbINzR750kF6rdGSFIRO/8qQAiUpteWIUPkvPQUsxdosYO1kBaN+d13xCF6NxBEb/KjLSjDW6nPw7rQphd+gNativms8rxPAZ2Jcbwj032bF/712OIBcEribnwSOmMv7WMZ7dIfn7v9E1f6wzknYj0NpHbbQ3xzJiDMOk5bODhd4S6dLvkUfxzb6yvJ5y7lw0oYkxd0j1G8KScnqN6sYIcrND9tETRDaOZhIYXdi8iJrqiZTJVJQ7acQOlgOMMKt5OEWChRO2mpsZU7z19sMrEnmVvfsLrp2+ve85BTl9tPi1YYHITxTGBNdyeQkMZV4nFfHJ5ByVZJkDKYnQXHp2gKw5hw9C5AXuI0daNqAC4ihQXqBw2gvVtzLFhpVtjZBuvsGK+9TyLDXsXzzaCD17+CPfTm1Jox0lBIY+vjD79bD6lftdVNIaG198t8FXzqFQta0d6DSoG4qinC6ehVsM3i7OEY8FoC+TsWn5c6YNC6wWlr1FwfoL5Rq/BJYO6cDxj0rx8/ohwWvWjUJLGgYFGMIATLILFJ2xo54Zh8j6Z9ZcpvBpb/+k8RCxdlvChvOmPy21+N28sqSyMMqquHKz6xQTZbV6tevqrSQgQETrAzg5M3sCiJJURdVM04HE9NW7f68DdTJjy/kd+ToN+ag2YuMk9q0NbZOW6RLDMuUqih9ck6r3MjF7k41qfCuV4mdLI0sHcPcooI5RaK2W3tMbaeUd7LU3Cy238YY5bz2SlvVp7kCppuT5dpNv0PkFxqIQ8xuMqrHMzwS4q6K6aFYHh2kaMHYlkWA51222b2gr5bsn/6PAcMJO7uJwIbFSxCLCAU8OcVyBvyKnE7B5rPd4+pLK3NJgofPtTAQoHQ4OedVfH/hQXgMsT9a6z4dA41XcL1t5VP8bxWANFy/K8Tkg7MRuFZSlAXMD/cJVxq2chO+QUmU+MUI2CY+2z1JxeColksWW+9KUSCWNjFzLJ2Hu9TyN27UtGKYy7LH/o4UWTyeFQ==", "layer_level": 2}, {"id": "133430b6-ec51-40bc-a3be-ff10342a70bb", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "User Service", "description": "service-user", "prompt": "Develop detailed content for the User Service in takeNpass backend. Thoroughly explain implementation details of user authentication, profile management, and session handling. Document the domain model including roles, authentication logs, and avatar management. Include concrete examples from user.ts showing business logic for registration, login, and profile updates. Explain service interfaces, parameters, and return values for all public methods such as createUser, updateUser, and authenticateUser. Address relationships with better-auth provider, Prisma ORM, and security considerations like password hashing. Cover common issues such as session expiration and their solutions. Make content accessible to beginners while providing sufficient technical depth for experienced developers.", "parent_id": "d12200c7-81fc-4058-be66-aafb1e37368f", "order": 1, "progress_status": "completed", "dependent_files": "server/src/services/user.ts,server/prisma/generated/zod/modelSchema/UserSchema.ts,server/src/providers/auth.ts,server/src/router/routers/user.ts,server/src/types/UserWithRelations.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:59:38.9027225+02:00", "gmt_modified": "2025-09-12T21:57:15.5478751+02:00", "raw_data": "WikiEncrypted:vMsgMax61wW2sTWhXd8DH7M/pIvlJPBb+acUbTmvng9M0V7H8DizJSr5tydv1mcpS9e3HfHE1cbq1+FNNWPE+but/p2cGDQK57Q/M0mNV3JxwE6OGOqtWdEdUnXzaE72tK7ozB73wEd4FD3Wipf0KyV547HTcHGKQcFnUjBUpLCoccgumZFb/SqykKUt5I0vcjZurahRcdQuP3omyE0X89KOBYaoTobHz1xya0sjYEaQ+4+hkAzE7BJFO6zRqXDVu20loFoIlGZ3EfRTashRkvkXCSsmnIRe4dmv+cyci3yHYdWlenVqRlblXClg7MdC1PS1bVHUCj7XHhsCwSs3w/bnnRDZLTOLTtRWX/0jDGPYv/rcYFCTvfyOdKA/iYPA2JqdC22E6YEiLk02/q38euTfBl7N+IxsMh83oPaPcaEw0fF3JxbUk0v/dhq0Kh3e5N92SH4RqMOqJ1lQphX5aMGt3LEdUfB3fXa7LOr7uF7GccRt9bAnjdcBsYsC4MwAdjFXhM/2/2oYlPm7i3EsCKkLzY4c1DK0PwNnAkscw6rMyCkHMp64GLKsVuBJomRGax8IAH4nOo5WPrtq26pUCX1dL9g3fFQuTGzKmTzzKfyXGw2KBeUAamihx/S+hrucsK/D0//xb14/bNZxmpPs4yjFDF/ISp4elq50HJI90cX+e1pIJSPwSNhT3DgC1rl9Zf9nXLAq3zISjcSoDIHoSUocURTxBZ+7GOLxcYc9l8BXzpNY1yi49uO9AmTXPhDTi+I6XoU3ByD/dW+Q0ljJPJSjJD+l3lR4KMGTZRhhv1q/zke8o4QTanaOW2SPsljupKf0ZmCZFAnsK65ms86nBY0uZIpee0LIS+oLYGcxLEEVTdC18MlJsitWFeHYsGCLPPdjelL5wm6Ln39gxDZOXd54F7uyoIix2JeEtnwFq9e9o95TbelHK9Z6i8vJcr6BtOHv625QVInIaXlRXncW1wzwIk+DKA/cbvOGT4wFVugd1eTQfmYe45SbrUOlHLepuQ4PnL+rtSmEOK5vG4zBqJAhiHpCMrx6enWXgx/qY5XgtKCvwVfkQeqiReNxw/QN559scUJTqzbdIvJpN45fyoa0JlpXocWPTkvf0iwLG6M6pJFR6mbNp53hifApONb0LPw/OKgF0nVEhM2O7h14XRqoe1GYIKyCcvXo2zdp1HC+GMRX1TwiAGQk0IXCqShNBHlsPiBffeIEs+pnh67tBWaQ1+2nVnZmtuWkktt72Cbk+w0VMKXQ+EMWn72YJXwPs1UYnbAvfQ2B4hR5hWQTsIyC51pWJ9xbngGk8MtkylH8rCmmoORQZYhW+bsqSj1Tzpa6HXyVFHnQGBsBgI3/jZjfmQwfTvv1LQVuK24fiQIQkIALgUqnZDkbEeEUafErd+WXXrPLfX1vrRhwl/uIDQFb2ZChAOmGfPwMGQRatnFX6Gxl8SdpDs4hSO4nPlONd6t9P+kcnrxijr6u4W7ca/SO/YO+fc0ZCHQJrZqg1vpuhnAZa8wKOOEGmF9WfkVUL7zV8/ljSbW+fuKUZW5vNiNhFoLwVPu51g6g8y34ZH1JgyLeyYzXr5uHw0SMAF4kYHjWLWg4oQrgQ8rQiCtEag==", "layer_level": 2}, {"id": "2cd6167b-0ea6-4255-866c-f1cc532b2756", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Getting Started", "description": "getting-started", "prompt": "Create comprehensive content for this section focused on developer onboarding for takeNpass. Provide step-by-step installation instructions for both frontend and backend, including prerequisites (Node.js, npm, MySQL). Document environment setup with .env configuration variables and database initialization through Prisma migrations. Include instructions for running development servers, building production assets, and executing tests. Provide troubleshooting guidance for common setup issues like database connection errors, port conflicts, and dependency installation problems. Offer tips for IDE configuration with TypeScript support and debugging setup. Include verification steps to confirm successful installation with sample API calls and UI interactions.", "parent_id": "", "order": 2, "progress_status": "completed", "dependent_files": "frontend/README.md,server/package.json,frontend/package.json,server/.env.example", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.3769043+02:00", "gmt_modified": "2025-09-12T21:03:22.5668209+02:00", "raw_data": "WikiEncrypted:qfgbutC7oyxR6nMxrwk1ODnNMBEQ3/sG78fQT1yXWjspqw7wFsgcSykO0UmGZY8QTKN7XNoRj5gLcQItLpCOKZS0zg70mX3xbSbPO9sNmQG9763jOK//x2UAW5p7WNHj6qkFl3V3wVI6RUoxNXNO3MeVA7KtXqw0NknsUpPuAsCt+xC8CiaS/wFN6nrVDhG+B5ba9Aw+L8gQd6obSDxKfLm72eii7XRP5QYbjdpQyB3Z8q99OOZ1w2OARY8wYzChrvEmOC2VT3MPtMGNcdvCGw6wDFyb47JjTMaCSZ4VdQAW1B3fdf2ncbc6DjXYM8pGwc8+SzP4YW1zMCzJ6S4T4sbMeSDRLjMuSGCFvV60NAO5R2Xlk/enCZW5eIHfsVIO3H9p25iXW3SJBNak0qdI/+VI2cX1VxNR5y5WFzjVy8I8ms6Rdfxln94Hut6M2mAs/CG6WTWzlFbn5HU6qnbnuS/pyneBFX4Jol20dqpPtN3b9Hmb3AGqjK+G5XJdgmnjJjsqFMa4+Ng6jE9O1VNMQ/mCy2jFYMBlJrUdB2fZtazYTRGY8JJH7bCokdNHVe4bZJ2SXb5GntdFile5H/iqk88wwT85r4nvjVsvCoxpsEySJh+ckOtuhhPKz9hxXegZW9x9A+Am90SDfPduzefu/r6h1tO3PcoImuSUCz+XfT+yzF8VO2VpOlGHuuIvFr/Dd5KjIJu2jYVDPz/ETqqN7Q8CZ6l4+QJeJEj6FsjkTARKCDR99xlNZIIWAyCodeAVGgrZir+tYoTDiEbcJNh2H/54k43JvMSXo3pDx6uufbvMCkh8MCpnqDVZWBctY62vmToCrUQOfh1F93DEjKcOdlBH1CE20OPNOFT3fvtU4YPMu0L7UiTkd2A5kQ0lF0fHzTmxqjLbQ7SKE/R10x5jHwqVchgADtmi+/UYzSC12zuvF5ykQRmmXjllGiysb5NdBjmTnQC6xO76yZamZ6mcSV15UxjzXLndgiXc9+5/gJkVTJj9ZAry7GeEziUP7SyT7elzQMZOk6vRfz7CQyn3P9RMQGCUcf54A1o1Z4uM8nL++QqcpJIux3rH13W1GW9duLCyFP5NyctEDmFjwPyFXsdeOkUyaH/kf6haeNC6jc9DYeDZB1CF5Hfsn5ijLtamX7cS2a71J698j8q3P7/rv7DRMp6Etez7Yr+wOWvLBlX0x4q7eUq13Ho486O01DeSsuyu2KimZR1IHej1kF4NxSUxLF3V4bOSyLQ9SW1P+dEhcXJppZint1dgBQ3wlnJUosytvVP7Wt4tzIIyARupIyvxAg5j/e0qwvDC5UlqK/btF/0O590AOYEG280YiSCZFfzig3+j2iCR/BwY1OPhXvrGJAqludAKgO64zQkXptKgD4UsfobDB9++bo5hu+tFqMIJWGEbYujamjjUfT4EPIPzfSOUZoPiW9zvYaNt+La/0phADdwnQdaKlU3Z6KK1kFPFSWYlcJw5mksupltyKg==", "layer_level": 0}, {"id": "d12200c7-81fc-4058-be66-aafb1e37368f", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Service Layer", "description": "backend-service-layer", "prompt": "Create detailed documentation for the service layer architecture in takeNpass backend. Explain the separation of business logic from routing concerns in services/*.ts files. Document the case.ts service including delivery case creation, search, and management operations. Describe user.ts service with authentication and profile management logic. Cover geo.ts service for location processing and external API integration. Explain message.ts for chat functionality and notification.ts for alert management. Include examples of service method implementation and error handling patterns. Address the relationship between services and Prisma ORM.", "parent_id": "5b381073-14b9-4b74-ba3c-f2ceb3abdd90", "order": 2, "progress_status": "completed", "dependent_files": "server/src/services/case.ts,server/src/services/user.ts,server/src/services/geo.ts,server/src/services/message.ts,server/src/services/notification.ts,server/src/services/depr_ws.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:13.9061658+02:00", "gmt_modified": "2025-09-12T21:29:25.7727733+02:00", "raw_data": "WikiEncrypted:N3UbzwGjCC3xD6BrTM0cqwo4EkjH/sTBOuv+nr85NnxOZY1rDNprLrhtiCzMP397zYOwZhssvFMsGPihQR98PTbwr8OzjBvGJapl7V5vXC2tAe6mCC2hTKVekLgrSvDKCLGdMY0LGkvYIWlhT67IB5nYIg7U88Ufs68EfxhsQ/WYUfJBIIwKwmKvckDTBaxxEGIZfYDp6OKK5P3XkW8PCLye0o0/QXFLoIiCYTuCmFB7bqeGGn9vX1OutWF69nRD13WQl3cTmhhm6Cdg0KIUPrfWTQdelqG94IB3d4D3Q1umidJ5E6ueelyw9okfPTYXEpn/WispLcVgjn3d0pzSwB3md/1vUlI6BoZQamHz1N74wZsoM0kkj95hCknuBe/gfIIPX4ptc7aWVX/RkOpUynwpWw085+BGQSvYZkYd/7+/EawS81uSgvh0S1zIkY4goEgnpymGpdWQ+TNnRcGtKvbjDzqfNnXguYTwv3Y89TEmv9QB6YvpOFUs3ChvTzkG7QJVHBt6MPfaSiWIlz7mzk4waE27VWtYB7o4y9hx4HWqkX7oAS6Hfaq92oe2StT+I6Km5w5Qey6Oo4/3BUm+PubX5uZftvtEc54fHVb0hSjx31j6eJbHUkoPVGKySIqzE9YgGwaboKIXhtwLCF6PF58VQcCqkwQ1M6EBFYKOY5JWXGfCgcZm9pFECbMrjUVs9N5iXS9Ozba0UTx8saCiyxZcohLoQrS8OOVe4BRWRrcV+RKIo6N335b8OWK7yv5pNdiG9zZbD2ulIHk5GOOoqEORcJwZnNz1dyC8l3AyxWN1FSJ4Y+VrYvLXUaydB8gA8B8yNJINT8mD3EltmchQHnaiiLY/fs/eWxePXuJvK9TNf8RKm3OU9e0Fq5VkOzUI2nE+Faa0HYpEk53rXwXB9S/EQexBhEpsA5LjmvTyUhNRoCposT5WioCAoBmstwN2i0kYb9rBlmklyY+zZ6eQTtfa/v8435CiyVSC34M8+ZM14FBEQUNN36//TKtz63Z+MnABX4rXOzz/l/NmhzoqvtBze/7knq+b1NCKewbSfpT0AFrPijaru41FpBZCGAebmA+hg2vei5cBvy0wSY0j1zJm1xV/o9xyVn5ESx2p3K6h0o4LqWG7QVTgpUwFv5lCkp9+ywMeFLV/ywMyJOtfjRVgd8qHzJfeq7p7yP+SkKfYkf3aIKhgfouF9ZRTmdKrilhg0P6f8fNkkUxE/wSpW2reZNTRL3pUWfmyK+TMKxAwZ8rImi+2CnithHRpAUIy7UyAEHF/VbPo8MdZnmVhWMRxbzP0F6b//YpEx4QexXoyQfZpvFGSV0y0a65sJo7Y42D9yrT1DI7abCEn6OtoRMPGZwoTWYlSJNAIXzSmbebCeDQ8ns79ohNmgHmxAxPvOkJz6JEh9gCDyNlhUdkfYe0NDaK79oYAvu2jT3bnOCPhCk7W8edGkn6ho24AHF0Lrj5V8eN9spavhCMvlPspBw==", "layer_level": 1}, {"id": "4354b674-35c9-459c-8acc-12075ff9ac8a", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Messaging API", "description": "messaging-api", "prompt": "Create API documentation for the messaging endpoints in takeNpass. Document the procedures for sending, receiving, and retrieving message history between users. Specify the Zod validation schemas for message content, recipient validation, and thread management. Detail the tRPC procedures for sendMessage, getMessages, and markAsRead. Explain the real-time messaging flow that combines tRPC requests with WebSocket push notifications via the ws provider. Document the integration between the messaging API and the chat UI components, including message status indicators and read receipts. Provide examples of client-side invocation from useChat.ts hook and ChatInput.tsx component. Address message persistence, delivery guarantees, and performance considerations for large message histories. Document error codes for blocked users, invalid recipients, and message content violations.", "parent_id": "e29a5c0f-d32f-4756-886a-0c99bdfe8bc1", "order": 2, "progress_status": "completed", "dependent_files": "server/src/router/routers/message.ts,server/src/services/message.ts,frontend/src/trpc.ts,frontend/src/hooks/useChat.ts,frontend/src/components/chat/ChatInput.tsx,frontend/src/pages/ChatPage.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:39.4044188+02:00", "gmt_modified": "2025-09-12T21:30:53.691667+02:00", "raw_data": "WikiEncrypted:IaaHBhg4aKVK2Z27STIpfzXqrcyCjyc8KdZnZnC+/FFck4Tjt6xUYfSdcB+l/xw+cK0qAzHzmm6fi5ymDPIjP0ngF2cShP0UfQij65XtxPcwcAu+psGRVG+cP5Dq5HsB/w7ZeLE64+VbhaHjDVzUcWdGgOB5aUGWk+f0w++KpcdS8UzA887tcZ0xu2bc+HMI7hmHThaH/jOqGWaukWoV/tL+rKAkQyilWthnwGQPXe6AhnDitNDhmB3CkzPbhRqzH7D4mqaVpv2iylLGgGEEn9rr9ua8kvMblO4dtUlcq/pXQ0qDTu1wyv4y/BFsfwyduVAlCNqrmwp2yJnA+qsBeSCkNuAjcYD78arKDF3Ejj86ZyuRqzGq486TGue0n7nc6dLU9VyIxYTwVmVaeCq1C1SjuGKY37r/hL/VwtaMNd4bA7HWq26nuSmyQmLwVeYn5n9btwbtPTz2365h0faPzoIqiHltIqk2kwZLdsSSHSGPz2O6VlQAQrBTokYx+FqJFNrw/QgGOLdiB057BRXiOAx4MxANovEM9MjPKJrQFxZWq0Y2aPf+0NAIpn/lZ5N36a5u/lthWSOIiPF7NjwsjPW8ej1u1z65MPL5J9Xc9AeSopbH8WJiLFTNILpIjnyS++aRaRMQ3gZMPx0lOb4T2jjaUJqkSjDcSOWLt3KJiM+Mw+hC0dyF8g5PodkqNGiE0u5bV6YphUa3bajeCJSs1wZdzR6KhOqoyL72UZ+TTyFw5ajtTxuFOBlyTXuNQqMgCdYnKGoyX8x9E8oYy3cbXdCuABH03eLPAYdyDKE4zEanpA26DujiQRNeO+5uRRmjdSaxO8z048QpAtbZ7OdbEsqts8XeppFrJ3PkDYig/5vp+b8I7LInpsr7pGAUQa0x+04OSnUthe31tHOfIAztq1iuPPqB8tcZPAf/dPJ6brYRtGIoIdpabM3wP1x+NL8spdG/iMw9usjOOlYMI4FTakOUyFk3ufXsquQHt/vKK9Q09lpyTbB8C5Qj1Jl1eh3lmMUUUZ5l7zmGY4U9IU3u4Npd3ZYVG2DVWt2expZrNEnU89/AziFIu1/yPpU53tPSf2h7IQwQS/73o32y2efes8ZOWkeSsCBLOQ4uWZ9onpIdWX76DVTJPiq1H1tbfRVXUvnhbOq2u5ZSjLQnw4hZibTga0OmqFNmLBVTaRSTWQaMKFr6CHFy6QeomzSmFdVUrLZGTP5tM4ZqrMVXysBSqf8rX0LWEwUM69LGxNhh2K4Miyq/EpCf4uxeYkjyndtzsXK2s9S35vQgvBQNVpLDwEhKjpUfFfH7iVGxgroB26ozuWVhOHX51zHDApay5kq4kdaWOYGrPUQGm6z5n65mlRtCM6aQoMQXsky1tMvFalAAOCOYeIwrbeVB1bXfGUsl1+y3l16QVWj7IvYlZoeEjhzzFQNRUe71fkJTF4fDqwkEkYs1uUeVtZkM5t11xmvPPu1jndWEdC3FlVBotOlwhlZh7ZPD2xQDEwZC6X85sO7LTnJGHJZTnuaUxtCWMGtCGrUvPHLKhSismaKfBoaShdpOP44kN8EG1kThX7BJ3irTkDcfYw+yYkPdqusvAO1eLszpJx0htGJrUvpNATxTZbUwJppYTmnu0NApeMYb2jjYBujVdkhfssppta6LGFzOvUqbigQ2Se/6jwHBO+SYJtKfr78l7g27vRMLI5Odd+0ZDlvYKZkOZFWipYNfyS/AwwEzrot35wQSqKI2/+mQLHAKRvylfK0jqFW4zs3GdK8=", "layer_level": 1}, {"id": "66c84904-51bb-4d31-8717-b727c0c29102", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Message Model", "description": "message-model", "prompt": "Create comprehensive data model documentation for the Message entity that powers the real-time chat system in takeNpass. Detail all fields including id, content, read status, senderId, receiverId, and timestamps. Document the relationships with User (sender and receiver) and Case (contextual association). Explain foreign key constraints, indexing strategy for message retrieval by user pairs and case context, and data retention policies. Describe message status tracking implementation and how read receipts are managed at the database level. Include information about content validation rules and security measures for message integrity. Provide sample queries for conversation thread retrieval and unread message counting.", "parent_id": "58a090a5-c63c-4022-9857-99fe4f06094e", "order": 2, "progress_status": "completed", "dependent_files": "server/prisma/generated/zod/modelSchema/MessageSchema.ts,server/prisma/schema.prisma,server/src/services/message.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:09.3219566+02:00", "gmt_modified": "2025-09-12T21:32:03.2768305+02:00", "raw_data": "WikiEncrypted:IaaHBhg4aKVK2Z27STIpfz7dWWT+c+Fpbt+b8byEBB/RJBH8RbLZcvbd6jLRvBMOvuHWAtFaSGdstYmEytUrtAYN8d3jin74XkshgGnvXGf1YQ3SJB40BfipKbXj0SA6SLtyuylj97wHA9k9I9l+bjAiQO4Ap7BekBrguNhsux/dxWWDdkyElYRHYFeM1vPVSr72TkmG6+9l+ujEWk/NG8/DXKXOkcmUlFGsZ0/g8szCuuS5Z489Am5/SwfrAXwinAjRbE7vnPjyDsQohVByxmq0b9knq54AEmo4sP/lhFEZ9O1FYGW4QEz35npc7HOi2QILEyN2E93tojbjhnGT+IXHVKlAsjyIdNJYtr6Qt7YcTcxU/Cb0hwg2e7tfJ/Gz8IVS/YKN8En7OFLnU3fKqhx7fTsOwJEExm4gemnWE+hrfBnA0rZD0+cdn/BCR2uplEAGNzjMYhug+92smXhfijJtt90B63MmD2nZIBe0+B8yNmWn8KYznYFGM9Ku8abmhGNyNhrLFQhNjV8ltlcKtiKjm8wrLsEc6FHKcbI0/N2nGzvZ/0MYfQLzieuHmse4Sqm8TsLXgcvie6zA9g31xwL74jK4Z5PjUATkniMg6XUijjef1NnqXYSoX6YZ8IQk65glcOMJJwdRitkro50uoCyoa81zQj+G3uhxLnHc+g196z9a3LkebACf/4QqYpNXj46hBWLt097bVrK8vGazFaknNN7cW05bdREj3JnpJvM8HtS4gR0ewd19wwPYfgvWMk77BtBpwv1+vo4vqZru/5RnPqzKgqG9ixJMf9gEWt2ZD3poQTZP5t8TFw25PI150fR1R16P2yFcQdr2aGvaD1lOyz3rrCokc0O6Mqu+wtzIwSafZ46OsdfMnxx4wyKnmrNlB89MMjMk8s/yOW9DC8wEuaZXE2tJkY5M1KKj2vlyZJQfk+/47bXomDMR6ZU/XFhUWBgC3fgPF+8QT3xPgUgMbf6LkUlbZco3S00KsTtlNc8SSGwYExeOz6OCC1QCU8wDkSSmAc2b3SkpsAYNqjIztdTHyudzaCB16DwJozb+MlF18E0ZP0Ht74a8bIxwV94B5vCr+2NUwQzpVi8mZyorAWe5+Yi40DoICkF+hh4GnBPPfuTzCARLDXUFyUI5HtyMaGDu8Vj9QLk5VIeiSDwYzW3LtvvKu7UDkmRt6uH0uQaW4qXipBjElUhI+6XEXs1r8slvA8FIP3HCj1zxII5AODiIDfF3CdqkfX1qWcpAt2tPsXVG26KdoJcYFS9ONh493z1U3MB1Aw2oEtMdg9jhtLnOaj7jSDJV+u/YTBzQtTM5/vteUSGrL2YRz5qnklqbpRsGN2ZBl24kEqc7tsW19HgwiG5/rv+BgmDifonnzApEkiQ8PBwqmpULY9JO6/rpKEyYX7XtWlMywmdkPa3XM41lI00iFQIMX/bJFlc=", "layer_level": 1}, {"id": "0280b906-f4d7-4414-b184-f4f31457c19d", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Form Components", "description": "form-components", "prompt": "Create comprehensive documentation for form components in takeNpass. Detail each input component including DatePicker, TimePicker, PhoneInput, and SortSelect, documenting their props, validation patterns, and integration with form state. Explain the implementation of RegForm and Login components with their validation logic and error handling. Describe the RatingForm component used in RatingsPage.tsx and its star rating interaction. Include examples of form composition patterns from actual pages. Address accessibility compliance, keyboard navigation, and mobile responsiveness. Document integration with the global state management and error display patterns.", "parent_id": "7e5065b0-32bd-4cb8-a5f5-554b801c8b87", "order": 2, "progress_status": "completed", "dependent_files": "frontend/src/components/DatePicker.tsx,frontend/src/components/PhoneInput.tsx,frontend/src/components/RegForm.tsx,frontend/src/components/Login.tsx,frontend/src/components/RatingForm.tsx,frontend/src/components/TimePicker.tsx,frontend/src/components/SortSelect.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:36.3593694+02:00", "gmt_modified": "2025-09-12T21:33:32.7230897+02:00", "raw_data": "WikiEncrypted:JMq2xgPQDMbLJ+LAP4f11gZM5lyXj22MjY8A6EPA4s/H3yF5vgGv5H2Rfvt3NlLxcFxI+NhNtiRHmSK1oPhrWBjPfdFzFCDYFBwdb1AVVXC3+qwlzSDk8UlaJnaTeN1eIExFK+eGtutB7QjiI2XUr283b/unEY1VepKDTFXTQpcWRs0/tlnPNzNrb6KB902ayNVU9DsygkKFxSk6E7zNpZlSJJpgI33bx+TixY7YTOIUaB79PASgIS50rvJcJYxAkW8dXmvfPuxusCaDaJZK7NLT1ytNbQsT05fv+XTC48XDEpIZWThNyansm7PKZ800o2/XsNw5d5+pp5PUTjcvtVDW6M0rwoPSp7Qzqgt1e/RlZIRwrD9pjdtmB0ydgDureWKe9K4MGuAvHLcETcerWTnPnU4WQemUh1Xk0nqFVrW2omO6yVyptl511n98YTA+eYNUO05hKan/4YL3ylXdB7hSFJ5lZoVcX9zXGwq8QFAQWyMkliC1UbSjZ4nkIB2UBFXgS6Xs9lO0DpfYFQQoXiRmzinsaKl7pdFCIe8z+zpVnLvhsD/vV6MvSRgMvK5kQuZ2EdGiDWHXEIE1qJC4VLUEMyU3dL3QuxZ6D0w4C1FDwIIzsrov1+aoxh53yA19hCRPRxttU9oc7aZwXrh49Rdr8MdprH6vaGILlExP9VKSK+OZjj4MKk3sHoY4/dzWLOHTjARBFaHugPlcrtSkuEdzPfI60qjyFmRF8Y/p8J0XAunHOZgJpznAPDuUfBVEcH+W/QySiEmASfGDWsE0AVJM4ztmzLNFrxT8md2kmcHyjneQ/vVH3VBj0DcrVchCX2/0eFX78LgZ3PTGNbaGYS8KEquiSOSlpVCnKYXsG/iSG+0wAfYvkv6Dl/+HW9T9ShIBgxzgv43YSddhj0TdG5qsL+EuWe4SDFAJARolchd7b9mIeZZoVTVQ6PI63wzFFbybgGvbk3+OSWOW9o6VJPdWsyRYcpRVCKBqLyHlYNKDl8PdV3GGD1D5sOISkVKfoasQ2BhtaO09TqkISl6PfvLy4SkYa8/aO2FSFKTDpNoIpUh86Jea8LQlqg01Xmo/b4+bPjTuCg7mELngbdwv4oDqdF3dGxmVifdYFfaVy6MutfJAsOzTJdv38s1ZJ+WuOr0Y4VZHEXYdYv3qYMTFba+sKQd0uSG2MecDSymvegYRgfOHvPYzLnVuB1BtTqKJOjNy+S5/zIGTqWelpoPiMR5UBp1eV9kLr+qfpmis79cRkhYKetkc6CIveZXJ1Ub/s0WfCSRlBL9oRvAucNUQjK8u9/K6NeZoaWiTG1QVfI4l/Xe4WE4PhWi27xJHi5wuyiOrWEGMeiZ0cLmLJP3lM9E8aAdU5GXm+90xzRJFHcwyN1Z2Rzd4AQH6qYjyWsCryEAbdzns7maLe9l3/t9FGXGuPqjiNaD8A+WOiluWpUdSiiCulRefV0j4CkAd9mJ1eZ/+YTDRhA/jMPZb0K7XLKyVWnk5knY1FYrxu/C/RYokMQRKVGdT2/1RKgv/P+3eAWfnblu1V8jI3JaV+RfWfYCBoUpzMzJ0zafnGuQIuK4=", "layer_level": 1}, {"id": "d3deff2c-ba48-4ddf-9f97-4095ad908937", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Geo Router", "description": "geo-router", "prompt": "Develop detailed content for the Geo Router implementation. Explain the tRPC procedures for geolocation operations including address autocompletion, reverse geocoding, and route point validation. Document the integration with external services like Geoapify and LocationIQ through the GeoService. Include examples of how map components like MainMap.tsx and search functionality use these endpoints. Detail input validation for geographic coordinates and address queries. Explain caching strategies for geolocation results and error handling for external API failures. Document the data transformation between external API responses and internal application models.", "parent_id": "************************************", "order": 2, "progress_status": "completed", "dependent_files": "server/src/router/routers/geo.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:59:00.4905722+02:00", "gmt_modified": "2025-09-12T21:58:33.0443715+02:00", "raw_data": "WikiEncrypted:IQ8oZnTFWwvh3EPHGGE8+GsjOKA2ME5w7K7kBT4dpcGNdx4CDIOG1j6arqyA368grjkc4ld1ocD79KOsJcSDhWfyeEqouvTPav7lubbQElfHHTWnJqCgY3FyTgcWFcgXpoFvExgjzHAMV9xShVC0m6tPXcZnxMkKfDY1r8sOYac0Cfi2q5S69CGYrCaPrZ9vbWB6avaVbXdkTWD7IYnsitZ4o8Ieju7oKBbpa+ZCXg9RC8yur2GQx00ppC0LZ6+DzTE5p9/G+2i+au447PVor83LN8/sSy26AoKIkwmXMJe/QWLrKCNQZAun9xWUrSTf3wqQHnsC69oxHMBHJYT+20JUBtiV4hy8J+ptrUPspdCR5gfT8nnFlDr135Qn23h80E9PzVHFh/FQiryQH3hk3N/mj4SWwQtLbV7AS8BuFyx7Ai6C9as4nkHr77Yje6mKNtbjZUYNTIKd796wmt/Ph4C781WLqUhVZjnswBwV6tgK6WrbNLjSLngRREcvclbOg9k1+v0vOKUZXkupdEdEZjiil/H8BrvJfEEzNqnI5Zm6T709eIxKKKnIUuHzUk1CNCQl5ba9cdRyh0x6uDTaywjpOSNViEQ63R9BFxB2bTyI6tCRqTxr9Ye9ShYzuNUoH0bH42GSrL3Mu6FGI2PjavVf1rP0K0SMCnM08WxmWh875Yj1cc3hx3trLEOVkSBc14Wsf0gO3tbOWxrfTBjs1uwu4ggM7PvxopqdScfcHEk2TqKPIdfZVoLNum/JMpp3eez2sIyMmiqcf5LKV41g/aB52zRNyoOko+agpIfSXD0LX6DPZ/ljqjTzGNUPzpVMRRU+QgN7dZZiMTWOFSgGNFTzvGyildkm2/q6JFlr4OND6ZVxGsRrbbekRbogmB9Fg3ITxItbh4Cjor2bq3cCMzYB5DcNtDFNavTMlwVXKrBUdeZp5OV1nD8ghHpfn0qpJm86j9qNhYnqEx2nYzEGTOsFbIBJVVRrfp2KPLvWRQbyVd/bm0I8yR+zeMW4PcnOO6UEouou9nfRnMtz1wHuIQynYEZrQj9e9g2QhCnPa48R1z6nKXhlsUSajmJiN+cJLnL5iYTfpFOpJONBKqAfJu7/WU8r/naZrFeBbqdJdb5bxuX8+kJSB/DO3CTFcCm8RoBHJfQNlkA2oITLYGB47OQPMgU/OjIn9lE+oLVV83FcGYAZmXAli8in+1uQjlDwB+Uw3lJM3lVCfFBl4wxGrw==", "layer_level": 2}, {"id": "8e8a9334-2d76-44f2-8959-75c0a7ad7093", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Geo Service", "description": "service-geo", "prompt": "Develop detailed content for the Geo Service in takeNpass backend. Thoroughly explain implementation details of location processing, address autocompletion, and geocoding operations. Document integration with external APIs including Geoapify and LocationIQ, including request/response formats and error handling. Include concrete examples from geo.ts showing business logic for caching, rate limiting, and coordinate validation. Explain service interfaces, parameters, and return values for methods like searchLocation, reverseGeocode, and validateCoordinates. Address relationships with frontend map components and performance considerations for API calls. Cover common issues such as API rate limits and inaccurate geolocation results and their solutions. Make content accessible to beginners while providing sufficient technical depth for experienced developers.", "parent_id": "d12200c7-81fc-4058-be66-aafb1e37368f", "order": 2, "progress_status": "completed", "dependent_files": "server/src/services/geo.ts,server/src/types/GeoapifyAutocompete.ts,server/src/types/GeoapifyGeocode.ts,server/src/types/GeoapifyReverse.ts,server/src/types/Locationiq.ts,server/src/router/routers/geo.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:59:38.9071047+02:00", "gmt_modified": "2025-09-12T21:59:57.5425238+02:00", "raw_data": "WikiEncrypted:vMsgMax61wW2sTWhXd8DH9XPSPkNQ/RpLZXMy66jybaXw8atkN1pjRlG997LssGVZXNqTZ/533acR0Y8jwL22JC6g69tsCWhRshO9ELYS4UXFi/9d6jWyNWyPSaUpCpfqywIHidV8MKdsvIBA1yMaU1vxJ0EosPYqjLzx3BZiPH4wbMTAviz/StCpMGoL3/6VxJXl7rcMSZQxID8UJkbIQ03t2mBEtKPSwXDc5mJe8Cg/5fGeQAlMaAjzZPTVXeRLXdVw8s7H8QFxcmBbW3/uJcGUqFI+v1e9gJuRkj/kTG8HBS6eIgOUyXO74WKcicVVwlFA7Ham+KAtc+1GLytncVVZlZNdRBryOzHtZ89HOxXYEMDww6e6OPwoRMJPJbpK99Nwk2q1Tre95If/Mr92T5VOHM3tzaE2qURj3dzT8+E1CAXlZ3W/ZpBRwEVg6xKCBKLPoZTMsooL+re2hi8tbmBSFp/6spYFK1pZmYjsu8nx5TdJ4caT9Ljy3njCnth47JOzV1hnVBibqjbxoxfAJWJrbBE2B5N4nGzEXRFK8xqijOgejAzmZou16fERgukLbOU7fBIbMLx1ue4g+qBjrHSvmoU9pA/SbE7qDLGPd3p3HP6jzjbqn9K7PegrhClzOLA6ihJ3Fb36VR8NcTAYkg0tkQEKhUxEPoQeO/1JonOTYm8Gbi5S06QqqNfw9Dw7KP7t2Jd70no8ltccC7qXLdMC3u/ftbweI2PYFgMZxxyglxr6RlLvq12I9QrioxhgpSS10177kyB9bJyGMQJWhwMIYVYg8b2NI8ntsd4nGdEHqUSXniS1DUsGxZyhOQbwnJu71NfoEL4PZj5el2GCT169lgAXXvsTRpRHS3hyX645EiyPDi5CQs8Ur/tTuTUC+BekP1M19E6Z06srPx3kgfjL1kBUnecnZfJ2V7tv3opnVku64jHo+1AMEPNgofzl/v/rgdsqSWxq1LUL4FlIl0L09SG3Oik3m/V0/qogomYFJUM/+PnAfSLbPbSboG5lGabLta4ej8ifih7gfeZ32kpgdAE5SgJLrMwY3gstZxrEGlXO6bA9Y8pxti3S8JxMdL2fqDwZDx5K1F0N71kCRO7/m5FtNBlY7zbxiqNvNxjJtxLbx9Y3VhrjIKkGT2y0E/bcg3g4xs/NMgf83m9mIOX+LakHKjbQaD9S7J0bEnct45q7TbMEqDJxNIn6pXMlVkRYidsNKkm0+smc1bllvonSzuMtqu28Snscznek26OelE74tzY3G1SRizAuhUuj0Wsp34xxLUVDWb55BlwHfel9QgUNw2dcQ8z/hb2/0VJnf1Q9d3QFQvhjHQd+6eVtK6//CLTYN9simoncjr5PLCQ64jbOXAxz8GASPnl4lw3fSlslTqdCG8AqPK7kCGB8Byey9MXYWlc5YzpBCNSUFS0stcjqw0k8h6JmzxQcCpb+xKaap36WfR/yd7KZzOkzdsj1Ey2D3oM8hwtH0tIl+8YRM5l5k9PpUurDaTDzJhHU2+IYIRRoQx2GbYKcbE9p9pJPuilsujcHus1teduz8/zi52gPEYCtRdJeGpieceoNOlOkXhVEhx3TP901gNq4/mU3BvRNqJLdUqsT9YS8vh3y71YDJZ93VpVM6u4Q/0hjj4MxjLy2ITTkyBMWEfkQuiRY3ylY4D9c9KVviBlK5mZRc4b2DnzCoubBZTns+7JXBdYXpZxPOgjCZfaLqp07G8NkYWZga2kZTrTtk7NYw==", "layer_level": 2}, {"id": "ec581b00-b535-4a6c-a0d0-19bb0ce2fe2e", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Frontend Architecture", "description": "frontend-architecture", "prompt": "Create architectural documentation for the frontend component of takeNpass. Describe the high-level design using React 18 with a component-based architecture and TypeScript for type safety. Document the build system configuration with Vite including module resolution, asset handling, and development server settings. Explain the styling strategy with Tailwind CSS and CSS modules, including custom theme configuration in tailwind.config.ts. Cover the application entry points and bootstrapping process in main.tsx. Address cross-cutting concerns like code splitting, lazy loading, and performance optimization. Include information about development tooling, linting rules, and testing setup. Provide system context diagrams showing component hierarchy and data flow.", "parent_id": "", "order": 3, "progress_status": "completed", "dependent_files": "frontend/src/main.tsx,frontend/src/App.tsx,frontend/vite.config.ts,frontend/tailwind.config.ts,frontend/src/vite-env.d.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.3812667+02:00", "gmt_modified": "2025-09-12T21:04:40.6126261+02:00", "raw_data": "WikiEncrypted:rJ/rIw0gVau8jPGqKFBAsMR2XDSFypkKHZQ6YBOgiECOtpjxeTIDctgQDypcHXj7kWhM29lH4ncuB1WibpCOXkrAesZfcY5g3zI8bvApqqbhH2bfM1Vt4uR3N2ClhlhlDImVt18eupuVlizDVoqubGyMwK6vVBuoVpnEKO2bB6Kh8MOvZegOyGSrvWtHrmbJiIxry8Ybjwo0pCrlCX3Kc16IAool8qrLCzbDJ7l6iC28/ON/uhts5LpkqYXtcJSqkP1SDKIF8YEdHAbgT69Z6n/pYtABFXgmpANj35L4r+83DPCQTb58ej6Wyem+2JmxEofKeLsOENYiH734NeZXzpmXG9dwXhod6DptWIiDYFe1smVo0ODc7PpXuG98C15d1uups4PD60SKF6tJsTi5oO7FZzseiY9O+V6/0HeMSOOfq+7syFY0aG1yoBceVUbdv1AOSieptExGn2rbcX3DPLd/CWmqXkaVRqCy4Ml+SxWpcqfTh3TVK1stuhyuKaCGQ/5xuG5HCecvtJZ08L3rJehpVZTfzG+HurdVSMxEpqrY+dqoIBHP6q7Aipt7ZGZLUcc+VC56FWhU8lxlugN6ZbxIhSQg9mGuShcNjd07MzOnrogrM6j7tl+iiKxkH0w85qRnjTzwOJ844f3g70h5/7or111PJRqk/8K4Hb+9mGt6D++YJZzcKPenr0oeQ2LT08YP8Z2D6E4VSjm9liEJRSn5ek2IzUq40E3jUOCT19TT1SVWR3NCv0RCOajhRoMseaoGj4I0yo7tfInZf+Ajft08sCFHgCanXXwMIhN0bEulLvC8l/1PvAbZkHuoz27Kja8Xk9GZoCqffXaKYKth2akiq7Pj0ZQWeHArhUy/mYS0c0FqcggQCDZmkU0/xEGJfJYW5rHd6U+PueP+sFTjCPEmr1BbcH+4AIumwH4kmnC1uxnYHUbVRpeu4ZCfwMPf/QpsTkH3PV71afCGbj/VDiOhy3p1jWnN7nbKJF/+F1xPZxK09yA2GTiaR86BXGqIevsVQC+ux5KUeVC/HG+W5AVa+AkpHLoKdkjNjDYlVPrh8Ly3CmwQRYvx9bjnjJQNewahC0SEdOxOL5ZlYht2KZejA8TFjrS4kkvKSqk5MDZ6PIZy8juvQMROykWhxtO3Kt1xmw9Gi/hHvjUjJdCIfcwa4VLtZtwLfBeyiOvtZCMT7rSqvCoDZ+evPUlVYxd1vyQn/3cwUjdCVLPWV0ieJE9YIweqT5a3JFjWSp5KkaMAY+lWXsd6TCilPT2DtVM8DJB0afaFOCQus1ZP5u8xr57ZIEzFr0P8o3gK45eqe0d0KiepM338sulNrcXpvUWIEW7T+wKYoN6kSniSBLeioqWg4G8zvl4v0TyrRyxUlKNb895xKEGtyp6TKdRyMdC+I49zVMxaKDw3OUOIEMn5/KLKQB6dvwMyCbBIy2adMpKKH5J7DRng70yDnBMtF+bND9gA1LU8UoJvKsgfM/XlAlFuaM2FUME3usf8lzXCL9nnXcN7LksUfMnD8fvDfjdv/eTBnBrv1teEJL+9VAW+JQ==", "layer_level": 0}, {"id": "899d811e-ec37-411d-b243-ba02460ccafa", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Data Access Layer", "description": "backend-data-access", "prompt": "Create architectural documentation for the data access layer using Prisma ORM. Explain the singleton pattern implementation in prisma.ts for database connection management. Document the Prisma schema organization and generated Zod types in modelSchema directory. Describe the entity relationships between User, Case, Message, and Notification models. Include examples of complex queries and transactions. Address connection pooling, query optimization, and database migration strategies. Explain the integration between Prisma client and service layer methods.", "parent_id": "5b381073-14b9-4b74-ba3c-f2ceb3abdd90", "order": 3, "progress_status": "completed", "dependent_files": "server/src/providers/prisma.ts,server/prisma/generated/zod/modelSchema/UserSchema.ts,server/prisma/generated/zod/modelSchema/CaseSchema.ts,server/prisma/generated/zod/modelSchema/MessageSchema.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:13.9105043+02:00", "gmt_modified": "2025-09-12T21:34:47.6746513+02:00", "raw_data": "WikiEncrypted:N3UbzwGjCC3xD6BrTM0cqzKqbR+9be9CrzTUGCo44eg6VDxB8JaKMGpRWuo2p7nutIpc4C8/bfkai/JJU4fDzNm09n5GoMTJoWO6ZppYZrWogpGMDx5V73Y8ZOYZVtXgTklxOicvxQiocx9J9d0UJyVMdQUp9UNh+Ri3Eh99PhYvDVXpWFzrsjI/cyIe1V9+9aNCb3i9cBZ8YyC2HmO5jb+2ydyKb+uJRrEY0q7uOSqdMt1wExkpJt6EZ379O/RXbjIDWtV+0HImPz7QugKQtM58LiWjg556QV61dzqJJv0qHh+9p14kNPmo40Ts9GAfYjtbt2MkKU6pzwIiKr5ly4v2jBwpnoJGxJNiaDqeaSDEzGzO+xPTSDf64okyf+Qno/S1BavNDoBdXu7QiGKK9Cxt1ZbiYI3o5Nkz1RaC49YjozV2bvsqUAFYG4msv2lBt9V+gvKhikBMXwxNVxbFbmsrfkHju3iCW4n2epzA3Z37QI5dJ2IJcA0/4v+HeqHW+54y+DWODEOTvQRX4kqgXtwl3M7onyADLTLaycN8fcfukN/LQwrlZjKyLA8elzCK5x6u7i63TZlUgmuMK1avgF/wDOZx5KDcAzG+EvFsA/vSIXBgOKvCZC2ZB61MoL4ioRhQwbJ1kDqVoAluSuPG13/6rVSZhoyayBKxM0Us4V6sO5tPGsOBODn/pTOTz52E7hlXTDcUQ3TnCpXNPY2WftvOYqhiRWiAP3KcYcS/JBu/Y0MPet3TFZpPIj2nB/KraiBXdLfSlQyMumj9i25hCGq4uncm0iqb9jE+3MKwqCNqIfKsd4wGg5TFxxMIq+1ISmX5wFw1ElEyePPSmmbuh/R/0AoMjoVuwCxmpRmsJKwk/BfGIwXoRjLpjb6QnpEP+Is1ZSKcNpJ+jKBe9IfQlOppdqSCuvTeJ6WPixZ0xdNOlCm3mAocOAr6H1ZI+9PLLkOsP/3BoJQ/Sa1wgaVkW/xEHdsAM0cehroX3W5+bAcStq+wP2k/db9rHrWLMOqMOJBaJ3GQbgf8bHZlOmaqHkRJjo67rAyjBj/SYOqA1NgkldXPB4SsGy/wBYnvwQaR6CGLk7LfR4MtvB1+rBBmClxjzEpcnUXzm3UYIuIIRKpJJ8KOxQiTFYveEh2/J9u1e+aBQIeoU3svzdZSl5iorsn0NmDGlLkGrsReo9wB+GWmK43bQBrg9bXKKWfaebY5wv0nmnz7NUmAM77D+ZoXqSGjj4JXkutGjgBaIgebRHdYifkEGlf9aQg4RVOKhK+O0WxA7jRFGwPujdKsE1HIakLZb6CK1J1TjfUm+LL0yuIOF+Ie/fBHEzeYFQebKoNd", "layer_level": 1}, {"id": "10ebeb68-9195-4c0c-aa72-11c1c35bfbc4", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Notification API", "description": "notification-api", "prompt": "Create API documentation for the notification endpoints in takeNpass. Document the procedures for retrieving user notifications, marking notifications as read, and managing notification settings. Specify the Zod validation schemas for notification filtering and settings updates. Detail the tRPC procedures for getNotifications, markNotificationAsRead, and updateNotificationSettings. Explain the dual notification system that combines pull-based retrieval via tRPC and push-based delivery via WebSocket. Document the integration between the notification API and the Notifications.tsx UI component. Provide examples of client-side invocation patterns for both polling and real-time updates. Address the notification lifecycle from generation (when a case is created or a message is sent) to delivery and user interaction. Document error codes for invalid notification IDs, unauthorized access, and settings validation failures.", "parent_id": "e29a5c0f-d32f-4756-886a-0c99bdfe8bc1", "order": 3, "progress_status": "completed", "dependent_files": "server/src/router/routers/notification.ts,server/src/services/notification.ts,server/src/types/SocketNotificationType.ts,frontend/src/trpc.ts,frontend/src/components/Notifications.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:39.4081666+02:00", "gmt_modified": "2025-09-12T21:36:03.2028223+02:00", "raw_data": "WikiEncrypted:4+tXcquKioWk6hL5AaAVvTBAM4CVuzwRAVeP7fCRs6q/uP8A1Reej9Ib54B12qd9kyvIM8hHv+7eJeFPtju6mtYmr4IPYfrEnQZBk9zWdghEoh7ZMEU7ceS8Q9YjCx5ajpzpxVJRfGKcvCh3JhHHTuq4nNTgUOp86bqw8XASOtLSD2+fTunFeA8nXSv3adfEl8Z3OD3MUWeo94t5dIbX1iYGBwv67ZEj7KRprNHOoXfBdGinUzRUBlSWH+cNJhdOlwt1A54oHkJ75r0wnrvivkljUU3Ey/sFOwqIf29WJKm6FECW3cYvS0TH3g+04HakGB/fgxCoVvcEHosBs6NZc0MT3nbB1Br7SR6ozMqXNeuEZipYGlscLNtl69rqKhsl9TWmX5clsfq+vS9Uig8yO/c74B2rJZfn2dVxrERhoq4k9ExfYqlRgj+PV3rB4Hw0NnxOp9AltBHG+V8+5U0RNUycKsqHp+e2Z0tH1y4M5rrsiB9/58y6YVPQyVgK45BPWIwWjLA+iERlBouGvt2E6k4xq1RP2cMmKmQhSku179fP+OHXlorPJ3z102BVUjA2iMZS0RP/OuacKssxrFvKw+sKCpi1U0pcnkheRSXVSFhjTzgv5trCR0Dd726WZQQBEtyh61zL+hTPldxz68/+Vp+epFowugBjmYkXUWtCV9bK78mALQ1bRKk+phc7TAj6rfGQqYQWQBY125lvjHYMAbrXveiz/Pf7LP3vHibDkg87LUd3IQi6z+kiOXsvLN65ZVq0wCbmecWkPvuOa53YEqSe5iULz2mye7ExFWEgs0lEueai/vWHCB2VHW7znTfpVkOI00o1YQZbdRszWQ6eZgoJaTpI8Fe9guFpYNCo/nRe18b8sipYwf8qSxxbp19pdStggQLn2hJFaefQy1WnqzTNAIngsIUiJD2syDT6YWhMkK5GRsYNclcOu1AsQFzCAxddZtoL8MJAizqo7TUS0ne6CoPuj0TsUmW06sGxTTGBvfiJBvzQJpwfKBm1H/WXTpM+0FAx6KMDq/0sURGE9l3w3DKd0hzJmQa0GvQnDU2wltfiKaaTMZ5DAz+oqnTuYMX0WKU9rPsBgYaYprp2t3vrgSDLq+j/xQRQQPG904KrIRhDhqoLCWeGpGAaJKg7LEJemnvMdd9O2cbdXEbifXcBviYEr0LTOiVBFLuQ0TR1VA17vuFqIRXTIJU+9ReIMJoJRNiDMk+3VVQXO01M307b7O5MjjSVOYehQuqYflhkenZht4+3ld4dIF+BOcr0bmwaJk94yEuWBAGX1zUq6oFA9ViPymu4qXtCny3M3VvtS24FjMn3zXdSeytOxNwEl648ZmyE/3ek0bGWAYfTExjo7LQEgA9LeQqtJqmMFy1MlDssnIqI0En4FD1pwR3jJSZ2JNBmdH3LarzYI3Y/uSZuKyjideGcVQOGGC4g4nsl76HacnA1VcHl/0hM57/ZC2VzoagyhC0dkH0N4UOa3JD9gvPjcPyV7lmr9+bhN764hls5y835Lu+d1f3/SvsAtwCe1zFzKbVKkYkS4RLQ0aoBQNsF90aZuVFnU4IMx1hKlqoXtA0JUUKyOzo4Q2onaCyuWVEToUCgeLfCCyoB20q52GaWlUqK7W37sYgImwrfYokdCXby20ZoCcCPnh46zfH1UkrsNfBHER22CkrNxowj2o9jZHcp1i58QqQCeIpw1BxoqK8hNh+knafS+SREk5uaHzaWemvCt2Z5ZKTf4uCRBdoWtuy8yDapCNfnEVNdTCsA975vLnLj+URDWtES+2T/+rGrGP5QQHKOVPAgrg==", "layer_level": 1}, {"id": "e9d7f087-512b-4e51-83ce-fa0e941ac793", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Notification Model", "description": "notification-model", "prompt": "Create comprehensive data model documentation for the Notification entity that manages user alerts in the takeNpass system. Detail all fields including id, type, content, method, read status, senderId, userId, and timestamps. Document the relationships with User (sender and recipient), Case (contextual association), and NotificationSettings. Explain the polymorphic nature of notifications, delivery method constraints, and read/unread state management. Describe indexing strategy for efficient notification retrieval by user and status. Include information about notification lifecycle, archival policies, and user preference integration through NotificationSettings. Provide sample queries for unread notification counts and recent activity feeds.", "parent_id": "58a090a5-c63c-4022-9857-99fe4f06094e", "order": 3, "progress_status": "completed", "dependent_files": "server/prisma/generated/zod/modelSchema/NotificationSchema.ts,server/prisma/schema.prisma,server/src/services/notification.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:09.3252876+02:00", "gmt_modified": "2025-09-12T21:37:26.1344478+02:00", "raw_data": "WikiEncrypted:4+tXcquKioWk6hL5AaAVvbJKxvsuvmY50jpXpoj/wJFmLeAp5eNc84ZJeXPVrUUpQ4NObE4+OKRGJ7METUXr0lB6dFEGxsuMENv5LvGmL18Hi2NEqwQF5ZBTP15BsyBdRzruwgR/39UQVGOExIC5TzNxLVu+0K4tMaNGPR91ScJG9eXeNfIhZbnjVdo4c4xmjNd5aAVUX9NPioVOZC0uw+3a9IBtvFlknfFZOcChle3FemBUOwifF6s49C8cvvDtfWB7G++C8WZ1ZHk6muMvyUV5yIQxSDUJ1OSbIZtzPlxdjICsm0ManyluKgY8TYI4d6Vy3fj80qQO1YNomgJnkepwG9wPD78fUtyycd+aL+qiD2R0HcV+KBxj9qvx8mbr8Ts1dFYrPB7xVx0ok3X8UYIZtLg1rxa2XqXAbLwZchwCAC2jG5qaoPi+yCmbEStvs55xvht9PHmtbSL3RXTz4hkLfBj3gmMcta31hFKtk0DRABlV9yCzKYS6zmeYOuS5G60rUAhSayIdu3sh2kFtOijibdN3ZzY8g1i1Q396gOGcOO1JI4Yl/kMxYBkrTO9PN248LvXMiycvzrxnGqnPPzTWZFa+MuATv9330G0/2qbjSef5uktPOi5u56oa5owni+dyoMq/BurhOsMOWiO7ASUZ5qRfOPoOunJiF4DIqXSx9oMyK/5B+gV7j2hWccxkmLd+lQ7Mlir7V3nI/bER4WkvvRzd4xTNXb0jcv6fY4hsUZu2+gxX0fYYX+ONYl11Cb8FGiw4e2xqg7g91p7lY9mXSP4HEuT+PDctU8l2rwGUZfKuTTV0uFPZjWDcKsPtScwPdCO5yWUsBwOyLwBoAr/ue5hDiQuWDqoLbuCY7+bI0V1rH2GePLpENdnB182J+m3EpM+bxmHyA0QIOZf+XD1g2TdP2mZcLKTmWTWQQkajmpxLKl/GklD/bh4gVv6fqYpAyi5CO67QmjXAR/6JIeWX7WJ22gmoN1DKlsEnHvn94rBZz3C0DSumGjTqmx7T/h707e/i6gcRDRqWjxYS5ClZTXVGlNN31iHuzlPM3YZDy/p/kM6kLk2cMSsrXC6QqJTHv0riWadULLEPJqes6qOT2X9DGkQ7bzI5amsYH5E0qLjqiD3zjhCvOI4e7U4wuDxubdZQchfkJxA3aRoXQbMgsBjYNdR42zrsAAz5H2Xqkatj5wusFnS0Nip8t+ZDLqY90eRkK1xMMys7wY9bC6QeB1d1Er9c6WD0Eg3Q9QGQWgF33hfSkVvJx3+JN9nNHy4WFxllTewEXYN8ZV+BD/ZoDmkgEWDscHcQ+b6mdxHniNg0SEgLxFuMIS5dPCcsjdqL7bpGCXfI3uHnv1CHU4oiQuDmzOGoBqScaxJHR8DlmN3CXE28+B9eYq9ckPuznkIUvBqCJkP+63BEbXIw+ETrdwf0r+C5d+SKEy6s4aCsfYH9CdgF4/kIPZWSNq7QXcHM0usJY4RBFZce9FZZUg==", "layer_level": 1}, {"id": "9456511b-20f6-46b5-8e3f-6fb32858e7c1", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Navigation Components", "description": "navigation-components", "prompt": "Develop detailed content for navigation and user interface components in takeNpass. Explain the implementation of Navbar.tsx with its responsive design and mobile menu behavior. Document UserMenu.tsx and its integration with authentication state from auth.ts. Detail LangSwitcher.tsx functionality for multi-language support and its interaction with i18n.ts. Describe ProfileContainer and ProfileSettings components used in ProfilePage.tsx, including their layout and form integration. Include examples of state management through store.ts and responsive breakpoints. Address accessibility features, keyboard navigation, and theming consistency across navigation elements.", "parent_id": "7e5065b0-32bd-4cb8-a5f5-554b801c8b87", "order": 3, "progress_status": "completed", "dependent_files": "frontend/src/components/Navbar.tsx,frontend/src/components/UserMenu.tsx,frontend/src/components/LangSwitcher.tsx,frontend/src/components/ProfileContainer.tsx,frontend/src/components/ProfileSettings.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:36.3631654+02:00", "gmt_modified": "2025-09-12T21:38:44.1055598+02:00", "raw_data": "WikiEncrypted:FJlU4HYVityre4LwWRwkrX/jUcc0MuZc0KGW9vzan/oX3rWqkNOtnY7rjIv9bCWftqxjCEzFdiAuaJKrdlUxdK1MVbsSCRmnokVBQbWP7vmu4KRQ2Q7ZNvP4inXbUmzEq0bBgnWDBGtBH9zaXsFaX8mY2tqX7mG8zK0NR/FOZHUBqNUgLeJ6o3tdseZgvu0LUkhXy0JsHvmwe2exLC/sMcaL3qBLvfqq9crOyfN7QYhJD0oUcmP0XpL8tunepiNeFDd23uvrBrEQfv5tyKFBJnoYSHjuoXXp7Rb6UKxmDrnc0NQFSIApa/JJzm/9Z4DqIWGML37pdGPy7eOrAQNF0Bso3jfZooalMoB1gMeWQLzwOCFZdm3UJZDayrCFMSTTtMzOUlB7arKtLK8b2dJeuI9JOjMy4eKq0sepGhAx2JYs87F8jarIg80rdeVfJZYDJKbzZMtw5crVh2H9+HYlMIhaj0jdK2eigaB7K+OM07BSVhWl71xqUEVGp3l+ouilUU/to4LAG4Yw4/XybNU/fm6U7bSrQgImWNf4jcqQy0r3S3RjTi3f5TPLsWD8jglGWepUihTYgRGhrpQe9U6TWHgCCnWX3OE1V9gjWQOr8Gvb/gFYJFjPlEwzuRk6Z+9MYK/1OBAc5UghcL2Ej4/Z4GZBtBjbtaNGtFIrEvpvaM1Td1hzr++p66EElfih/X2tFBPQRSk8DZUMnXAlXtFlPx75CxJynuUEvVPRGW4mqtPeOf6sEjDJ/5lwyHlhqxZQWQMyd4Oq5rL+VmGRC2W68UT5xsTZLbtqMn45X9Y5kCKuTdt1TyJz6+ZmXtBq5pR0ob51AV+3JoEGXjsmPTEvVq9lhjY3DbzIx9eKFdyiXoVqD1+NKv0y4TfLyT+PpTUjzMCZaSYOZMQ5DcrenL7/RFi9bPCUcVvi2DhIBdigMn/vObeZqOjEDuEqoXBSGrITrsLfrh6sQKBzBzMIaxZQWwmEA9zwntF2dC2RaM6ZY47yUs20MbLSu9B7FMSuguXAKbVKo0jwZkEnpjwwfPGiCvTc1O8RtgqzSuqhSVMglAsqAPatOmh6rD3qbSVGsRnXl6qwVKEQRsEMf4qtlV8dzH4KwDDz3/AFHDeyK9pF7CqXFPVB9N6q7FC84hhzrg522gHHwq5Y2bR6iZU9HZ5QBY/kgY067XEf5dv4VCwgEuh/QgPvmHiHBQCky0zmviBY1qPVMCixgxHz0ZtqLIWPvZQUqz2hbnaIrQNHVg4h3sewpsFZ30LO/GARMPnRiDO8FiUXVSg6GG4PwV2jeNCKeZ4y89Je8DSqYfDi48SPI1sBAo0MoGn7IHBPHL/YRp4ZEEMKLF8J43VzPHEdyuOPmpfPpKcUprBmlCqEm/XBNf6crzQ9Qj+x0qIIPtmmbJAcmSh0s+7cjwcYVSh93bwcX33sbsw/r3HVVf0NFvW07UODC4BiH92CPeyK5vFbUZA9tzJcMsED+U+fVAdigiw03B1F9kT5pmqlSQg/8V8DuMI=", "layer_level": 1}, {"id": "7b7b17a4-9320-4d8f-8d76-35524cbba17e", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Message Router", "description": "message-router", "prompt": "Develop detailed content for the Message Router implementation. Explain the tRPC procedures for sending, retrieving, and managing chat messages between users. Document input validation for message content and recipient verification. Include details on message threading by case and user relationships. Explain the integration with WebSocket providers for real-time delivery status updates. Provide examples of how ChatPage.tsx and MessageItem.tsx interact with these endpoints. Detail message history pagination, read status updates, and attachment handling if applicable. Address security considerations for message privacy and rate limiting.", "parent_id": "************************************", "order": 3, "progress_status": "completed", "dependent_files": "server/src/router/routers/message.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:59:00.4944682+02:00", "gmt_modified": "2025-09-12T22:01:08.6597415+02:00", "raw_data": "WikiEncrypted:IaaHBhg4aKVK2Z27STIpfxXFkVqdqDNEm0mlEhblNTocR1+U9xNO3/Bi77Lo6eL3n+nsFwWqTZHHOFvzTdGFX6oWwafdRcdzFsrSjda3Qc9C+NZbWpczR6HkgwjNY6eTL9e9Yk78h1keQBEsZWBBBwdUZfMMl9D674rCFUgvQHmD4+OUNRaF36qMatCwklPjRqKhrWc7jL9szTPgr7OUq8SMZpSYCxL9+f0ksvYOYAooNwJF4oa8G66uCAgJDD7FsNbV/KpK0ef4dt8cgqQd6F/QYTOFsFiDo/nBfCjQDIJI4KwC87PF/0W5kFS9cRa0QOmD8XrlLo5PqNJ3WK09wcsWq4onoderZtkkb0QIDLu93xo+gcNMvwmqpUj9hqzAA1a1CRO249GLuQ4AwUrSO5dXDFvWfE1M4wl1sTnNCk73V3JYjku44MC2IBgR9SFG7iZqv0WUWCF5focNNx6wrsBsP64K+4lbIGwPTqzSuDlaeOO1ZXJ68AZvOaDLOVTiRSkvzjXsNNDDXtsQL6a7eEsWoVQ9TywDhHArNfHNMC2KSbbBpLuSf/yBLChErkT9/VHAbmzIjSdQ9jjZgIN5C9TFWqwSVaIeK2FFW3589TYSLaE82k1LeCws3X+i+TA4giF+LYv5Fo3n67vUNQdShLNuBCI1VFKmo20kHNalUwx5DanVkwKcvDKCh3a1cH9YDoI/5m63YV5zKjxtRIdx/H6hSgQdxUk+lqii0UGXIOMysvc5lEkZwJegP9T9ymcBo9FthuYMdidFKILcmtNzSv3SRW8n50pVDTGGXVTEcZxOlutxLDz78il3TEbLErjDBbRO8B0pq2tjk1a8TsrPxFND4uwL8Ikqd2f9/tDyoR06KI1ok/u0D2XA+pRsINczvYfgiNa1mpE91EgLL3va7jVmabuisV5WRfxprZZ17R7Q8gqt7+zpcZudskNzNwA33E+WbjSV3tVfKSycXoM/XudVpreKr461/Y/mMKhqskNmMeAr599PksazDHMIr+sEsxMhiJmZJ/7NQXaJkVlclFObZQ3HYii71hcQPTAmh9mCoAR2Bck11vNlsdnGxK3LCY4fMfiFiH1zA5OOVGqf2hX4BjlkefHhSLhHV2MTa65dk4lJFZ1NNDXJenUOyxbYVFnNEYlXCIMQwkFB5PLrPd9R+UtaOUGOFy3dGd5DEuzolbvwZtnBJHLLG2i8uaWf", "layer_level": 2}, {"id": "e572c362-4efc-4929-9677-e0170e63dbde", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Message Service", "description": "service-message", "prompt": "Develop detailed content for the Message Service in takeNpass backend. Thoroughly explain implementation details of real-time chat functionality, message persistence, and read status tracking. Document the domain model including sender/receiver relationships, timestamps, and message types. Include concrete examples from message.ts showing business logic for message validation, delivery confirmation, and conversation management. Explain service interfaces, parameters, and return values for methods like sendMessage, getMessages, and markAsRead. Address relationships with WebSocket provider, Prisma ORM, and frontend chat components. Cover common issues such as message ordering in distributed systems and their solutions. Make content accessible to beginners while providing sufficient technical depth for experienced developers.", "parent_id": "d12200c7-81fc-4058-be66-aafb1e37368f", "order": 3, "progress_status": "completed", "dependent_files": "server/src/services/message.ts,server/prisma/generated/zod/modelSchema/MessageSchema.ts,server/src/router/routers/message.ts,server/src/providers/ws.ts,frontend/src/socket.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:59:38.9107649+02:00", "gmt_modified": "2025-09-12T22:02:41.1617799+02:00", "raw_data": "WikiEncrypted:vMsgMax61wW2sTWhXd8DH6egZvl0pdnw4cQ/kPs8UnJNfyKRVQHHwVGSk0f40iyiAK4COZ4D5M2MrbtwgK+yAWUt7q1GgRelR5kZjkFm6qCbUZlhW6c1lbWD/WcnKkUDa151ziRcJdhA+3FhB4Qy5f0cmkNMybIn3Wc5mQrpvTEQeRxn45GhwqsQQxMiUf2WEJlYKUCUdMlu5aKPL/AvnFltAWImYwMe/j3mAffh0aiygFy0zOAGGcrwJiyuGxACXg+uhDRn8poEtmHxaFFr3epWYE3GEyevm/4ygnAGlDABezrl5EwdW2dYlGa17dRs6VmZftfZNSGwiottVLvGPLVi3yAfgVDuntuQ075f2WyCTQWfED5BzAZ3Kojfp4OxLLHgFUCWsDCoK+GqlV4wAPR5l3YzO4qmFwEOD+9pxxld2qT84q9zR+rP40zW7SIWLcZ2cBUQdiLp/BzU2A+HZDmWTA6UWsBAO5152LlK+nfBo/NMbFcfLHOb0go5w7t5oBmzXQkJFqRU9/lXeMTxFexyFRVpiIha5wZnooiKb5ybUgFwTSNs4NRdekLpL8cERn7fAZPkI0l+Pd8lJR5lVKVwDBwy5Z+Yma6Zm7NeLLm1LRHlYWp/ZX6QMsUhza95762JHbGrtHO3wV3K0bokZdMpNRawPbuWgISG441QZbDbicyqga8akI1e35qsFtDVlYw6+5lqesZ0PrbOdD9QL3yKnO8nUw3x0tgV3RNHZ/nc2yvPcU3sSIT7e7ESz829chma02zJLdAkfE93Kac0XmFc24PmcPR5oQW5fIAzs//2fEJsokyd5FCcph3NUHuWIgUZC9K93G9zCxDixB3lF4ZjQL5rSj7+pETqovazQ1BuxfHwze7LZEE9kemKO3AUKvdy6eCrw652XM4bXGNHJUptFK2UcrBXI/iwEwxbAjhSGQrSXKajzLVdb/SPvZngWsET3veEVJQOvExM67tnd7RzikSchzUqATRAUXYiRLTs6+sEdyL3yF5AuExxbcVo5/B7OhSMtlBNiy7ttX/2XVWGaz+tcDNV59Fay3npofZf9L0RHyvP2Ph4mFinjOfvuWxsMvy8b3zfIaerukzgeKvEN5LrURwV8ffkbKfkQ7Gk/oNUVsI15xXxSiEVhoziAz61ler938B+OTwbXUrpUdeBlIt3UfLH6gp6sKKZXJFJ9fFDp1LVPhEyxSR07lwTrdKQlH2Dh3zms8NlXEJ99lLbjbvublmZ1EsKKA/x309vZAsIjh3wBv/xF3xaGc24POhczwuKfeGwmXjZTo5MgPmvzI3TVjrCxOhq1iCBKvfHu/lFhMZwEzMDDJr9NwP5tjsx+kARJwchy+1HF5xNB5QWgjHapyYieX6Dez5TzTYbJxM1fFC+qXN75Y9ce1eIbWJ77d6AGC6//egAoHP4qv02MPaowf4K0ucAJV2vrPWHVYJvm0P3oQNwejBNfxJOlD2h4GAEvfkmjup8SGAubr3AYHCEAph9+hKgISiop7+kxHGV8kva//dH3um1UGu1IKOtdVRjqT4hpdaTf1U079sQ6fioL7S5XOC+jwyGhLSisUEdN0qSKVBs17N94eFwLI5+6+ZZ1bXwVFSAxYAU5/7vmia/3b+XuNbJyuxtntBIENGXVdW7LCwSa7zr2GEL", "layer_level": 2}, {"id": "5b381073-14b9-4b74-ba3c-f2ceb3abdd90", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Backend Architecture", "description": "backend-architecture", "prompt": "Create architectural documentation for the backend component of takeNpass. Describe the high-level design using Fastify framework with TypeScript for building high-performance APIs. Document the server initialization process in server.ts including plugin registration, middleware setup, and error handling configuration. Explain the module system and code organization principles applied throughout the backend. Cover the process management strategy, logging implementation, and monitoring setup. Address infrastructure requirements, scalability considerations, and deployment topology options. Provide system context diagrams showing API endpoints, service layers, and database interactions. Include information about security measures, rate limiting, and API versioning strategy.", "parent_id": "", "order": 4, "progress_status": "completed", "dependent_files": "server/src/server.ts,server/src/index.ts,server/tsconfig.json,server/package.json", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.3864335+02:00", "gmt_modified": "2025-09-12T21:06:07.2928754+02:00", "raw_data": "WikiEncrypted:N3UbzwGjCC3xD6BrTM0cq8Y9ccVoDb9n1FwE8c3MNy2FQOgK1VCbEwsDvJYoUtQwj8mQ14lxcm0/1ON0LgrajrrtL/Z0a37U8aFUXdQgN1nUwFNvSJwal5jcRaSTgxK/xFSnA5cxxjI307K/LMvHcaa6bVG4b/H1KX8iyncl58Qax6x8nKbRnth7tYhHOqGz0bRI6f2g1ZV4LBeQgd9kqQKxbum43Xs+SZtvmI7Nx6aRdQ89zUuW+YE/nT9oryHfE1hwXpgtaKUjNSGgjpgd8OTlkTzjOxGFXS1NHK897a5fSAPhvzPWcVBlzZzLj22URLB3e4BJxz+7Rl9+6v018soasBBEUjCqKHDtxGVYNqMOdNsnved339YI/Z7R4Guo4pNGWqYSIgh5MRF0tAG9uu6Dcc4Gv4aRCPEc6qVeceIKL43UZhG5M0bwOlZtE3eAsn+dRt6ddxZ3zxLuKM1FvdqfURog4Er57G10g/uwd6HEJU0v2+tBbJ3GlVbDYdSkuQhPbdaV29Rn4YTpeAa8sKTbuwvp6MEbOFqx1tseJFcZJlwPFp4wxz+PagQWi+Vwyn1uz6IB62VI0MBQRTpVv/1PqVYgoSdnPBvFUFljfgqaWvAZTJ/f/u0CO3YIQzv0mLpjIZtzuWMCZkJtTp3+pEoNQDRLjltrrAfcu4+1mDF7nGooxRSgLY5ixtXZcnmOua+iF3hW4FiWlQZpmXf9YxVsaviGmcOUrXrKepVp4hfz4ZZ1BbohQkM727vyAHGg0Fz4sG6QKMfOgtrfL6bOrLC4nhwqmoL12PNuXNN8bL3V+SIa+ltDAlsdzrirY5R6S1xvFhYoFBHIQK/ufLO1+jTMcH/DJw1QJMe6DEmB+7qRxvLVX5miofAES1uJn2r/cCNgUTCtl26irlDb37b57NjebMGvz1zbcZPUKGyBUfn6PoqbB535SqV183464SukOJuyz0iZrsw+P1ABLQNUKNg+jbCUyiEDIugWKAQxwply6VNDXjYq5pIzikgKBVk1AzZNvSTgRv32FM884WVsUcRK3/5ZyDjiFdmPCaWNl8TFFGla61BxOT2EXFVYm35UPRrIlKsZXEVWstDJRCYn3cmDFrJpWduQ9duXez/gT3M19cH9/L/I0o2q1RSc16Yh3WZLxpj2zmJgcg2DU7V14t3yA1vrpwavUA32iPxXrStfCNlp5SI5qFUyeBZ7EQoTYr4L3xwCRNST5Q76/+h9GZ368MYjHr8tHjBPUmBsJbFMiWGJFQ7h9+zeOxMvOwYlZkt/+2g8Hyu3Cb9ucxqDoSeQxt4uY4Qquy3pOYvCtY4lPOkpWpKo8/na3iaDKwAbovbOxNaSa3Sdvrma5JpBrneD/sGxtrt/Ts2cc2N9UOCGT8YQxs80B08XX9xCxQZ+hLX36LJT0dWyYaGbwWJIouEOynxQW5esDY8GzkAu2g8qO8m4Lr8VK8UMYE+x9LAq9wniGeAderQoWwbIeo4mBE7kqOhygTS0wdd2NWGT3J8=", "layer_level": 0}, {"id": "289a5787-d796-46b0-b1e0-cdfaf4546a2c", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Authentication System", "description": "backend-authentication", "prompt": "Create comprehensive documentation for the authentication system in takeNpass. Explain the implementation using better-auth provider and its integration with Fastify. Document the JWT-based session management, password hashing, and secure credential storage. Describe the login, registration, and session validation flows. Include the role-based access control implementation in _user.ts middleware. Address social authentication possibilities and multi-factor authentication considerations. Provide examples of protecting routes and extracting user information from context.", "parent_id": "5b381073-14b9-4b74-ba3c-f2ceb3abdd90", "order": 4, "progress_status": "completed", "dependent_files": "server/src/providers/auth.ts,server/src/providers/better-auth.ts,server/src/router/routers/_user.ts,server/src/services/user.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:13.9138157+02:00", "gmt_modified": "2025-09-12T21:40:08.4642102+02:00", "raw_data": "WikiEncrypted:N3UbzwGjCC3xD6BrTM0cqzG8B3/wa/xfAEEKNvLbx79cIB5+PFtRSHCaqBN1iYvfS7+IYpYg4iAxi5Ue32LOpzfoddW1nWZV3v2s4qWIFOvTCiQ/QFR3tibKr8iItBGnZ+H8gnpjW1RwrPsdzqMP7EzCVbxt/EAG8E+M7HcR5QAIo6FWicnOcFoza2D/DUSD0WujrgdPuh+7YAUowtUrgx9ayRcHbH0ynGTo309/IgnpJ325rgBIxAUHvynx2ST32bnytBWyOzgv6JMo/nOcZPT1YP+p+WM3o46WndnJOMQpaUgfG5Wuef/jlTtpohrIWMPw1Skg0phyJNQ7oBWApIrwPvWIXTzN4rDbgkPvcMTN3l7pEFGpNxuFAxmvschPuoid3LoTQxPU6vIVjEz8KJ3zW23JkgjNUDFZvymJwn1kXbj0OSprUdlMmhcBY/z7JIZa7zgznX5VWgulVYiqDMXoomsrXKnCx4PCFt0aV6q8lDVc4xftBgJdzFauR7P3KKKM0ubM502g5k4yfJKrYY59i9ar/xl8yNs696edCMFzeLKUcbwGGlyXR2vR3Zl8nXp34PFEKVFzwuy/y33/pPmqhIgBbsvl1JeWYIAeKL8MPAvqdBQMtonnTny7XGmQ7Ml/FT/0rL6G1LyUk2i3VSa71eqfcRRji1L5shfgvP4lqKCZqFGnyOEGq+mGSVd53qgGFIG3pBUTvjTaB+MGeH+UiXoRVhIZO1FhtP0gCpO94rQDWAaUkyqfwWGJRt2hCTR5FkAuXPQr63RgQhhY6GksOPWMdSEN4YKUG5yLliRN+aKZxzmWi3ZWM4NQt4qCxEwUnurEn+wMHOk2h+x+89WOVIsYMN8Z7Dxxrmj+eyk2Rm5lDYTn1JlA0OWIo1XGYVZcnTYc/NnX6N3siT9XQLO9FSEn0jxEdyGyGNLkeFz3IoWUayQTSA9dHc7v3odHpdCQ9dUGhF5gWByzc+IBfbxFpXa6YNjzZaXSkzUG+sa/BcgOVy2exK4HxXm4CiaSB3ml03jTMg1rzB5UeV0mNsN2uHpphNaWfLeYm96D1CYbv/twlP7tUsi/OLdSZZEu/01g67Kvanhd+nm5YsnNkRnbHXYQu7k0kkdF9B9A0AarFmiuhJukhZT13y+Ls1n3N1ANgkayyom1TE0AL8fTJ7l06vHNn5ak7uS3BQO/xuvXw4TOPW1FZHZuopD9xF+PP0Rb+KpGiu/fU5eTfS5X8mgdmbD3MYgSkgN49NgV2dqTkOmIGtrcX2X3FmgzeD2O", "layer_level": 1}, {"id": "f53b604e-1402-41b9-adfb-9d1bccae31f0", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Geolocation API", "description": "geolocation-api", "prompt": "Create API documentation for the geolocation endpoints in takeNpass. Document the procedures for address autocompletion, geocoding, and reverse geocoding that power the map interface. Specify the Zod validation schemas for location queries, including input sanitization and rate limiting. Detail the tRPC procedures for autocompleteLocation, geocodeAddress, and reverseGeocode. Explain the integration with external geolocation services like Geoapify and LocationIQ, including API key management and response caching. Document the data flow from user input in the map interface to external API calls and response formatting. Provide examples of client-side invocation from MainMap.tsx and location input components. Address performance considerations such as request debouncing, caching strategies, and fallback mechanisms when external services are unavailable. Document error codes for invalid queries, service timeouts, and rate limiting by external providers.", "parent_id": "e29a5c0f-d32f-4756-886a-0c99bdfe8bc1", "order": 4, "progress_status": "completed", "dependent_files": "server/src/router/routers/geo.ts,server/src/services/geo.ts,server/src/types/GeoapifyAutocompete.ts,server/src/types/GeoapifyGeocode.ts,frontend/src/trpc.ts,frontend/src/components/map/MainMap.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:39.4124591+02:00", "gmt_modified": "2025-09-12T21:41:24.3664988+02:00", "raw_data": "WikiEncrypted:zBkROzF8lhIlYOQqXWsFEMxuhQgpr6qJRVEM8QJ5ePqAw2mkGLG7MkRGwAcSq+O+vcMRuwKVtYqVTZuR4dNJwV8uO2Kk2qARZFcWj8JCF0eOihPLuuZFkonHFTJpRC0cWFfUp9NdC0sealsC/7xhf59xetiJL9Av6jyDvWH6EXPUBxMCGt2YXvJxaKsia5Gt7wE8wiOHKAzCloOst0TYNkHg7r+N5v2eaUz8X8/wx4rvXrtyTZq8nNoRpBrUypSb4YFTXZxgjFeJ5UKpxVhD2zCDShtOxAzr8iUYLJhh5m1wAW+l2cq2PgSL8hu0bQNDI9HGE+7nEpcFRcwnMmA1B3Aiby53v2vrSumrY0WtnEuHjy6kS3DIfbUpcJkEoPPyCfMTiL7hOlAXORKH3D5kF0QPcy4qkC8DO1gKJJStZLAV5KxoWYBYJbc9aYxchHO+iVah5uwZXmkwyHmoItg90UHNUKnSqVLPQ+nR8o1VPvIu9NJzJNUfatEff1E1172JR82qTm1YFemUGEomli8IFyu4t3lts2m6WUNSSQJtE33uUemHi6RSBRNZe38FFlKhy6imzxcNr7sKAmTxBY5naKW1/8CsiUooNumazY0kLb+4VfBeafzrbEO5bLPC7rcd/XVGmiCMb1KQdACyG37RMiKK1dvL3ko870j7jBdgO/iVuIyT9Qbonkga/9cwhlB3G4cB5yH8YCOKZYEMotsoMaTiPxb2Gqj86T3WkvFZzPCpPUOcKgrP38Sr5dJzx8M85Hr0KjlOxPO+mUgPCd2r1GTtyY9c9EKTm+1Hl3hNt0Oj4bmYoR/53tMUOlFxE7x9Sa1oEv3X6GXYO8jgzf8awvu4FweKW18Be4QpFPYoCIy6iAU4OBtlFWOZ+uwKyUXZqMKpmOWtxzbjEgB/iPTWKt7lOcIwT7c+R3C4+IXmMaqCpMiC8GooJeXHh2eDLynHA+GCt7j3UP+zYXyyvpsjmu3/EXHkHKaD5Eg0bb6e/71l8A/pCTrwIi8iiC9d6m+lnYXvqondWMSgecHSP5dCcC3t1AXcteg3e8psQtbVtPsmB6QBkJloBbjqW0nTFRGPDKHrD6FrENd6izUEd/4ynUqrfSamWe4dzskA7CqGU4db9fgyyA8KdWEN95MNxEdGaoahsKfmay+QxazJmi8Teax9EMGKA8M7NG4zuOEh478y3SnZBQ/3601QKGVslnqnuU7F2q86pYmDwPhXRhRMQ3NBikRb0BR4ySy+D8Nk00KZJHQO0m8lrnZ0mpAIB4cYr1gfbSF4ZAsZYiRhqGuEJ2CEO5MIaRkuD76kkSu2R8JubzWJFEIhBTA3t2rqbUVMRJgITu2WjrGEMst3dXW4RWj1K8lhAK849juBlwVZMjG5CI+1W8xQ0vBGWFJLzKBS4Yt8vV6y01cQkx9PkZfcFmSwIRcnbhSD8ZDHiKO7CBnFTX1HFik4M2rHPDw+dccDtpA30Vn/GmxX2bpuAua8e48LbI5dvgzakWUlF8RQAABdq4FO5ySeH5YWmS0yxHc6UnYK02wMJqjii7FlwOTMXp0CeC4fELfbHoXczOBpQEPDWdUA2/2xLWibO3MfPZl16eflE80vYLOI+oFcUh+DdaOepzD2ZHxvcXOgnpTPox+/tRVyqu4x1OTvIKvptSz9pHLD+JjJVJqNK1vUxPW2mcLnqA4nG7Dy0Snr+fb2wnIm62MX9f3bdG4Ne9LW+uJWyQrjYxVtdxDJVbWjBRoYesGzC6xZ5u5U5hG0E8QtBSef597i39uOnGPswK/GDVjMySezSoIyF5NkeYanBnePtACA0SgL+K6jJmu+cLqP+O3u3o1oZNsNoIdU1++NHt57HRNBhPsbZ0E3Umu6GwwMEA==", "layer_level": 1}, {"id": "4056c7dd-1432-4740-b10a-abca2c50d20f", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Rating Model", "description": "rating-model", "prompt": "Create comprehensive data model documentation for the UserRating entity that implements the reputation system in takeNpass. Detail all fields including caseId, userId, senderId, rating, comment, and timestamps. Document the composite primary key structure and relationships with User (rated user, sender, and case author), and Case. Explain constraints ensuring one rating per user per case, rating value validation (typically 1-5 scale), and anti-gaming measures. Describe how ratings are aggregated for user reputation scores and displayed in the UI. Include information about rating moderation policies, edit/delete constraints, and impact on user trust metrics. Provide sample queries for calculating average ratings and retrieving recent feedback.", "parent_id": "58a090a5-c63c-4022-9857-99fe4f06094e", "order": 4, "progress_status": "completed", "dependent_files": "server/prisma/generated/zod/modelSchema/UserRatingSchema.ts,server/prisma/schema.prisma,server/src/services/user.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:09.3286055+02:00", "gmt_modified": "2025-09-12T21:42:55.7662701+02:00", "raw_data": "WikiEncrypted:IYa8N+afFRkwDAuGN1ugsJ3+OvfQvTqbI7ljRu1r9EFwB9rLH8WvU0eOtHSFBwv2AC9gpQpTU1xKhPzRog+4GSgpvnHRelacgWOk9e4CYzzyW2NkeUUoIdN79emXyiQJSOrw0B1vXyt08J5FMPV952DzQS/q/IPXTatK9LIFsw/xQbZzyRJpwOdDjFGos3AM9NAHp5TYZjWHIIZzcrVvRUUigmDC/XRsq07Dd+ownAEnR84ThESd0Xjq0AYLXUJaozEye2Lu0uc5EJE3Tr0++wbHa1DetkVtAjk+jeGvToT40qhlmnLvfBBUG/okPKxoRva4hw0wbvRWkKZJPQNuxBF057E4uIjf65QAa+MyPkbMKtrn5AnyZfsOJDhcWOY0OPXZaWS/QLLTSubL3IqKV/bbQmKJru3gTjuqI+kJK4GLZu2wp0kdybOHkKt46IOy0nXymmM56LopRJpLQQQnC5ILko0zyWWtjZbdJ7fl6XRzN0FA8GgwCP2o2KTcHsjKTnzwIdssp+QgkuNS0sFgAoUK6/UlZWB9wntvW6FmBgEoLyoowqtEiNGE68YhhMXdNhbfg7YNnfHuk1KdgcOmBC77tRuP4jEJirwOx56QnY/DXSsppKWjGgef9uOB04jfeVImrs5KLCmtf6qBQiYvasfIrV6xRktMxGhhCVqYeHlGHHvYDI7uSqJoUJELibunhnAR24UeCFCIGP4zSmQujmIOt4TH0dKrRwv2wNwTHHNYPjcBGBr+OgXpDjW2U8OqAbgrA7so/oR5htV/z1fSyFhYAAAPo8EYSd4lr4u2mKpEoMooi9VePDJAheHj462AnSqPWwkLH4z0P3Vw+zqr+M7d1GqFumRXh6ZWw0YGCAhVFINmdYWCo45c5A/3Z9F2w5xIlshHKJQgyIqVEROAybnFq1QNdbDQc+vOfxMaiG30pSYKqPBshi7idPMQ0Pbr8H67NBHfQ8HAP3bC6KzZaJGmvtFGX9jmdLHo3EKHFr9ge3B5o3HLVaH/tIcl5PHGp119wqfuwEc3i9kbtnFdA0j5RQ58GsayjyTGBVx06CNIl8EBEqmr4lPtQBV3lc0JWO0lDjJm74ulaGO0oxPB0djTVxkccfl6HcoSSq4ONaiq2uzS4Sum6UNmeCA9QNvVi6hmcPIrHuSB2np0e78057uTJFuLwCdjh8Vb8aQTPLTY0t4jZEjUsvY67ppGI3rJ37CihkbzokhIVe4JkWO0YvwDadkXCFtai5PlfxFFqXuT7mpgvNSlvN2nI7DOunErQ3pVlqle1GX4wGxWESCgvT8nZrYQYT6ghpaUsFenfEJRQrO0pkI9i5Sv2uJQo2Qrh5iH3H5TMMWIsbyrbioHDXS2Ab8d8CINQDVgK58wCFFqh89B8EEu9twNJB7eLmf/ZX/+dO78yY9K+kpiXglHaAysxFBhj4+elQKGXPZdOhyh4N9n+GDm4Jno03ZBV3Es", "layer_level": 1}, {"id": "35c4c38e-993d-4695-b114-d699ecca6c6a", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Utility Components", "description": "utility-components", "prompt": "Create detailed documentation for utility and reusable components in takeNpass. Describe CaseCard and CaseCardSkeleton for displaying delivery cases with loading states. Document FavoriteButton functionality for case bookmarking and its visual feedback. Explain MessagesWidget and Notifications components for user engagement. Detail Timeline component for case status visualization and Upload component for file handling. Include WebSocketStatus for connection monitoring. Provide usage examples from IndexPage.tsx and MyCases.tsx. Address performance considerations for rendering multiple cards, accessibility compliance, and responsive design patterns. Document state management and event handling patterns across these utility components.", "parent_id": "7e5065b0-32bd-4cb8-a5f5-554b801c8b87", "order": 4, "progress_status": "completed", "dependent_files": "frontend/src/components/CaseCard.tsx,frontend/src/components/CaseCardSkeleton.tsx,frontend/src/components/FavoriteButton.tsx,frontend/src/components/MessagesWidget.tsx,frontend/src/components/Notifications.tsx,frontend/src/components/Timeline.tsx,frontend/src/components/Upload.tsx,frontend/src/components/WebSocketStatus.tsx,frontend/src/components/HeroBlock.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:36.3669723+02:00", "gmt_modified": "2025-09-12T21:44:35.9422088+02:00", "raw_data": "WikiEncrypted:SvMc2PRoWqDcMfdzcPf97M5M5o6Rta+d4HlK3CDc90im7wmXJ90sa9Yw56jHoKkjFzyBVfBv+ySWpk2TVYsAwuAwPHZvULV0uZyVIjqeduxzVVATOic8U2nHEa6cMecx0W9viubIvAVoVoP7FN9EilpHekwKQk2EC/TPiBqP25xtqJqj9Preb275WftZOZw+ew0zHRu5FTU7Z16CCulNBIVVmqbk6Lp4yjHrRJ7VE4+MV0gfep5a5o1HEB05tgTPvV7vnNam+Y1/7yxK43KdZ/3+lO+ZtcolUR0ALzD48JpEUoNV3+RxGZYDjflwNT8idQzWQ6YRD7bf6APJvUTmBHeLXA+ztSDK5Sx7At7nmCyUdV0VEq8CMiuhzKxJ1VqcVQPEJja2GZICB/Ab0ZYAUAktB9OMvtk9QhjD/35dfHPk7+WpAQo5YMqkoFFYo5CMc4wM3cJAttW5r9CSoA5SV2WyK+EBZvF3t5Hghp+P4tMRk/VSFRugWsWrYpC0zg+piZDE7OVs0DM440+BXsK9ZVyhLIOLnztH0vUjCxD5on8ZyjMd87wYPgbOvu96pwJmtOumCOSfnf8vJYeWLge3yTfVLtzonotNp/0nBQOmRKF06JSbspIIknRtiuV/0Aad6g1OLkwOnWVJeZ3WuxqR8vx4D+Bz0sDHgBHgqjjd9s3TdNNvlwrIgJD/Vg77B8CS+LtNfYyri8T9LtgvM2T49YhVorPXzyWDeGJvjXk702Xc27R7mqOK68vBXGSkbbM+xsr4pz6NPcjHjWB6JyNJSB0sKCRyuzziwxrSzhrCw98bORfaLSkf595CrfqPPExHosF+chx3FtdckgvdZajGYsEwOS6yME6t7LXzZTLCkjENxj3xN8uhIkNXqQJ4lmrCKnFrIgV8+zo5FptyeHARvWykn9H6IyKGX4UJjozzo9aqeUxXioN1MAa6w9xw4xNduoBNUuIRb/0UAkYBMCHKvLvoDEtB8JM5P3VZJhecMjq3666R1B+X1KEha10CvxNoUfY2ENyDgjuLIj1UKLkTckN4VRTdotckKdBBlyH2YjrF8TP+SAqJCXou3KrMPJZxRwzHYHgfqA+KJIYNPMrRMrtVxtpO9vd8i7my2NQaUDHIF96KGthl4n4XSvUN8UvT5ElwRsFQxRfqhcFrgtHcxaQOxyjowyQArg2k6gqpPxf0wYQd7+RAh72W5mdsFN80vt4zmzgh9Bcw0NYcEN8o9r/BOj+DfQSAf154YekWXzeaHlvpM0TfZOw8Hs0qji4BPJWjMxN+xRXVP1UJ5pxqxBoKPEC2PfOpML7s/N9aMsUOEk90kEZklXEcfhXjs1BZiQ0Gbtb+RjYmMcs48pHhsJDqHD/JGFHWqChzPE52tbs4aXKIYgDOjvPt9EOt5v2d84z7SdCtc4atS9/8R6Bs5piZGZfu3KGUb9SCsUvHCNWkm38W/YNVUWm5jhc7kupxJ9bHG1KlbQoOGhhDTYfWupA2t67OpH6GLgN27N7g0aFP9rpNQ+rPIUYsBSmi5DMX8dk8l5lI28S0AUSn5Iyy/vn7goVM86gmKloIen12YYUFzGqEdfgoA9VoC5t3Dme/Hw9JNTyrszrikRBiQd+kmSr2gVcmvtvvkjuOfW995wLxTGMaBAzCmovOWL73wU1NAy4h6StBE9xErsPl2W7K6h6oiVvJ6WbkrwR1Ql9GlSAEIkZ8bsiZ97uiarLOZA1LmGDknsiYrsgigqyt16lbU2ras+le6v1ChqTyBo6vQbCuUX7aC9JcndjHVfO1ynhhyuOp46ATYBpBShM/NVqVDQ==", "layer_level": 1}, {"id": "4c942802-408e-4b02-bccf-359f8f88f0e8", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Notification Router", "description": "notification-router", "prompt": "Develop detailed content for the Notification Router implementation. Explain the tRPC procedures for managing user notifications, including retrieval, marking as read, and configuration. Document the integration with the NotificationService and WebSocket providers for real-time updates. Include details on notification types (case updates, messages, system alerts) and filtering by status and type. Explain the relationship between notifications and user preferences stored in NotificationSettings. Provide examples of how Notifications.tsx and MessagesWidget.tsx consume these endpoints. Detail pagination, unread count calculations, and batch operations.", "parent_id": "************************************", "order": 4, "progress_status": "completed", "dependent_files": "server/src/router/routers/notification.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:59:00.4977773+02:00", "gmt_modified": "2025-09-12T22:04:06.1729019+02:00", "raw_data": "WikiEncrypted:4+tXcquKioWk6hL5AaAVvb6yA4191QSXOd8dgp1mlnxYWSqBlu36jvoT21TP+r9Amn5Ccg5R9NeK4GlfzH3op/S2HFyBwI2zx5grrcEguc4cyS62z3HoPmfhEGuX8eFkjKASXSwdxrZsgEsy5SovYXS1AxJOptv8IwmHB7WKNXWVJer/l4+76Xp7hjOFQVOILMJXLxvRt9CLJhMYhrQ5MiBeto5hi3YQpUHG8KoTaYFvsCwvj1elRQ7DmW4GRqBMTpSMHpWbrfBXmG0Upp1zvql/RH1rA/9bRzfFVj3WHUQEM5XYTxPLRL2parEvY082zNqa9Osylmt+D1SfUtFV+hX3sW84ROqfm41EFu/pcaNRIy1WGaPFEdqX4jpUUNaEKHpu1twap4XTRMh9f9uFilU2ExdT2Ht9v+Ytrdnr3OvPFODhiyhw7SOnL/OxwUvbsXIx7F953vMDGu27M9RvepagdJw79uHSWcKCE0441qMOWEoIHOaQH9idnhz5AqmhB/c2k3wk9PV0NjTFyJ3+g1m7PS5RCBqCHYJ8YUAx4zLjNPX5Udq6CiKbxT+9Xdchv4Xwle+04hd7UPwhqKCUfuxq2EExbb4cWe/gNZZQC5SFOg4CtTQwevghWxvAZdZyIvc6I3cQumidekZZWiSvyWvq62HsQs/LbUprL2yGn8QNCkjq6NhFumfd9gDPgCFkSB4Wz8zvPPVEhSEOD7CUEo0nUCwxN65mtvLDuxikX90eZ1Cif+zxxgYCcH2/979jGNFVboskSOF5Ywb8AKzKjlIVIhlIlcH+bmuazWCASDXxsx8borpWqnIpw6gJa10wana6Csn74eQ5N0XyEi8yQptczz2W3ppjR8q536Nd+InECsf0LDqZ+VRzLcJO4/BQK7xQC+fJj9CTllYgAP3tWg9EaIMEb4Nmb7hN21KuDWxmT2hq4I8dun3anovcSzEQ1DBhwVYFurPwA0EdXoMGpFvzAom2L1YJNG+BNCPlbUS+ROiAvQVfugso3LAzRZnq9OT/6Ehs4DL1HnFcn+U70OnN0KoEGBgKCcvIDRUqpofSZJNDWocjo1Jg3SX7XCXoDM/FjYzCeVkxdPrv0Hf5ohGp+Wa9LxeDyofO4kYmD2y9i8Ub65tBoFbFJpTQ+refb/RK/BkR8kH8xpTHpCEWh/JdQgi3fwgg2Ksc+o7ZaMGWAX3AuBwFUbG1C+wL9bpSBcylJUZ8qKsSM7JA6wNf8f9NQK8w+LdPsCxj7fONRMY=", "layer_level": 2}, {"id": "2c8293f1-86ca-4ff8-a17b-7e97c22d8c84", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Notification Service", "description": "service-notification", "prompt": "Develop detailed content for the Notification Service in takeNpass backend. Thoroughly explain implementation details of alert management, notification types, and user preferences. Document the domain model including notification methods (in-app, email), read status, and settings configuration. Include concrete examples from notification.ts showing business logic for event-driven notifications, batch processing, and preference-based filtering. Explain service interfaces, parameters, and return values for methods like sendNotification, getUserNotifications, and updateNotificationSettings. Address relationships with WebSocket provider, Prisma ORM, and frontend notification components. Cover common issues such as notification deduplication and delivery guarantees and their solutions. Make content accessible to beginners while providing sufficient technical depth for experienced developers.", "parent_id": "d12200c7-81fc-4058-be66-aafb1e37368f", "order": 4, "progress_status": "completed", "dependent_files": "server/src/services/notification.ts,server/prisma/generated/zod/modelSchema/NotificationSchema.ts,server/prisma/generated/zod/modelSchema/NotificationSettingsSchema.ts,server/src/types/SocketNotificationType.ts,server/src/router/routers/notification.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:59:38.9150023+02:00", "gmt_modified": "2025-09-12T22:05:19.1862742+02:00", "raw_data": "WikiEncrypted:vMsgMax61wW2sTWhXd8DH0ONiE2LVXvrVkGtnsNcyd3FxwXDAS3ORmjZfhNVmompCGL9ovd9LS8/oy6Xza49GRRSV3JE0Q/iHZQ13XwzTvGCPdnEVNT9OKu3mQhGssr0hMTZTV3a6iKRONb51o3mVeKyiHECIpEVqsW7VKZnKDl7wVxK8VwxhgTav5Imfe4SLAdsHCqqvdy0a0VxR9F+qAGmvI0xA/0WMaIojdjZR7+HxMoAtu35ypNyPDsfkqwG3InKfJoGj3rQGsKvpMg8JZkfMevEV4bcYUmFxaW1J3D45+PAScRHyMrsYB6Wa8DLbgYBhmX2TQLNHvj6Tr2vw67qWPUj4PijUOInDiFPv9OBRcJzziz9i163BA0WqJpN4z5elQVJ5gj7eV+iOydpi/c1P/pj8bfDtfNRV9c38xtDkg9Q6JO6PnFTMbkXHjEHjo+P9f+/Trx0/ETNH9Yas3BU8RLVPJ7PoS8+CNYGSlsgWQGIUYZ4oLcmm3iXyygBfyiSytIRGg1TVOSqNxsMVR1X2UYRq5vGtP5Yrki2hfdLhCebEde2igOnp90XS1jGo25tWifhHsn3aElRExYH1cnKu8oZbGvqVURnnCSRm8FUTR0k/qV8CQDhPY15DNlGHFiUWub1lsBjM8Gn3divKgkbFuotEm9YD3ati1RMidZ8qo5q8+eZK90khPhZLH/UM2Ey1SqN6RawOBNNJulByHOesn1vTDleL37jjDCKf8C0EC4SPk6WySvGYgtcWE6+cbz9tPLogALyL3rkvH8LvAYSYeFiWGttp2brfHDTC4LIorrEfg0zvR8uUvaOHUw25fSr9obudNnGb6zPV5fTo5IXXUnlyhYt/YUO5kQVbAdUGbOZ6re/BIbeeQSfDkn+pmAf9wt9nkf7V5P8aQsN0zOfBvfzdK6JavjVqExu4ICPPxhhp2+i5EO601JaNXODakm4L0B8FNmgyafATViMPDypGbRgy+dgOX7RmaTMgVYNz6KDG4H8SzEzaWtvNOReGY8O4Bf8N8pgh+9xqVWbZ8u8rk7yLYaA8BLP5Kq5nUQketbD4EfGuTFQ/ABSWch5Ke+W0ylNXLpNT2pPWvc7dVQQlpBfMxrJPVQIt4ZYB/VO2lA+0ukbf/kQmVT/WSPoh+n70gvcS4By4dSoHPM8VCypr2ui+HdSvmV77ycIrcv6mGqg4clCt61YzN8Cdri8vnaUgIdYKoPWQbPgJZDPMOpozmExAibsHKZE6dDF6ZFjwtHiTnByCJsRpmxl6ngXINU0GzkhANZG5oo9VpSf8kU0PlvBP6C1sX2XIyUzn7tdlm/8PIz0KA0N5BU2LX1y9+wLI281qRUHkEpTFln1StlWeCEJ9CdNq6ZwkfTTC2298XBv1tbhw0y/4rMHERUYBjgi1PvAs3GzhtLpwPcNSwT96v0YEUmevurGn3pkPIEa30eaKl+lnce5WMBgBZ7/FtUkhN4ZopRXF2pxBAe8BAPPQrgpsJi2vFxJZ2z7c1mgv8c5/X6pWeaj5W+3t0k3fd9gAHbJYakwZcBN4xYh68FoPnsSr7kNUmEoTsv+CFuNxEXNSdBERUmUiDyMaM/VIM19eQ8rjBAhNEbkjaLEdnMbJ59uThWG52AH3figG0FfabsnEk+yBwOSEMkkd11xUScYwV0p1OnyWPbDf7x05NoOwGigFkGheB39tCLQu7i+a9kuWnTaX2lcIxAZzR2ynNvhD3UCjnP5F1yWN3LRpRZWtIDCVHuoRXt4Yge1RkZApZhxbRyMF/MukxVolzOtcLzD4eKydwxk3ruA67i90NpS9u1ES2uFg1ivhBIHJNUX5pYfOuKNdknNZpDqu9xSJivqkAjVLPRq7Ivupww19g==", "layer_level": 2}, {"id": "e29a5c0f-d32f-4756-886a-0c99bdfe8bc1", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "API Reference", "description": "api-reference", "prompt": "Create API documentation for the tRPC-based API endpoints in takeNpass. Document all available procedures including authentication, case management, messaging, notifications, and geolocation services. For each endpoint, specify the input validation schema (using Zod), request/response types, authentication requirements, and error codes. Include examples of client-side invocation patterns from the frontend using the tRPC hooks. Document the type-safe end-to-end flow from frontend to backend with TypeScript interfaces. Address real-time API interactions through WebSocket connections and subscription patterns. Provide guidance on error handling, rate limiting, and performance optimization for API consumers. Include migration guides for any deprecated endpoints.", "parent_id": "", "order": 5, "progress_status": "completed", "dependent_files": "server/src/router/trpc.ts,server/src/router/routers/,frontend/src/trpc.ts,server/src/types/", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.3909043+02:00", "gmt_modified": "2025-09-12T22:54:42.1538237+02:00", "raw_data": "WikiEncrypted:RZNXu19SJ/2tmZCrVS63Y22apEi7GzLCNEfRMnzWirpXt13SoDaP1qe23OLxeGSWild/EjV6axUq+K62cfQVmzFXW+E4cFh2mJTJgLUrrcSOHGgZwYHEo1CVC1OEwBxlqSuzCnbLqhBkctjq7bG2JKg/QPVXqaVAF4+l66woXGSPgYZ8E0pZDc5A6Sr6mQKWJnCVmqqvKuw1bQmdp4zCWv3pP8jK0zSyHTCzov1lhTiAMVnyyxiSLJ089kVmR0nFcCiQYdzqorl15+lsbs9j2n11W4il6EKLAmhAjT4cdD6wzxaIupHOxaBVt5K9CLLyKaqlI9dryO8ye6GoPWchJM/I2pof4FSKt2LsxuB14rnmLgtGXeQFIcgN8Qv13+FhZ0hUNp7D0sR3AdlsZZ1KWDkz0jGarzN6zkUIIg9bykeFPCAEIb1FJzvJf/unG2qMWUexswvTp/0q+TWDCvJSRUI/CD1lLPL9H3VyCWKo0I5/cQ4M2u9lhHe9evYSZMDSaaSWpFHwFaBXlLSt1p4BWJP9dajl0PZhPsblSc1mzj+0SwTCpDf4l8l5t6zWlCP2VWvV/9k9KKSKhvH5ZmoP3RHQs1FMsPbdqpTxowD02yAhuXvqhlzSPz/5moM+Hp9De5mb2VGJIflJdNDe0rhz/YQuWFPSPWHczizT7j8jWlassfEdy3Of5SC30g6dAk/gUbUW2AJ0Fh/Gf08s5YvMSVT/c16mqSPY7ZT71GW8zxUIFqd1AOJckndf8OrUdfeoLAZ+qtrjfjgfAyaMgmSWoPNsNfbCU1aBpE0LhrfDD6ux07agPUzOdb9ERDOfLZdu/dZ3CCb1bcOK5xOWDaRrxKyAvAtp84oVDf4VFa6nM8vV4nb13kc967AZxbWlbHXF20rMCFhPN29PTQ2ZFY/Pl+9ZV8CwKtIribLTbMFsoR5kE0C9RaEn7vAIfrHQM8PoFpRDHJ4ghG00FVpO73Hrd7y8usuymuBEz4MiCTvOPljTnOYuWfuLHLhG0T2qO2IEFuRNHgMT4pBg8rOeEHV3y3ZrAUKZ8RSVzEELKTwruQ4uRVIWdRVGJkjktJsp6CVOom4IJD78FZBUNukFFb9navKpzNqzzMkHGhgcAxZvg2LDPFK1fwPlzM/DYHwNFW8Hm22R9GCymVfmuNMv1uhIvCI+rM7bbRA1tl88P3oj88uvEgoBlksGUicz6l+pKJs2drtFqLG1fqfXQgat6i8CEr24uGKH2rytfT1kTCWNfyl/Ib3BxUNvEjAytgis3RnnOBz61TB/Nb8wiWc/qMaqF2Ydf+oMt61mcg57vJPml2iwWEH+060hiP+ZItnSHkYrjM5W60Bp+637x8i2WQqVLeFbTV+1/mCk9m48sw7FrQU+3WP8OiRjd2C608oovKFju/x1hlp7SfokGJvd9E84qGudEwfYqJtlY51hlulFfhfdmfyqAt8AOWYtcF47G7PUuCrCmnnYmZnXKhGX3j82rSYXBO4SAysE9HAWMET8Yith9Yq6SrBv+kzFId0Mzhpg", "layer_level": 0}, {"id": "ecdb5bb8-f8cc-4357-a857-39c88c0fbfa6", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Real-time Communication", "description": "backend-realtime-communication", "prompt": "Develop detailed documentation for the WebSocket-based real-time communication system. Explain the ws.ts provider implementation for WebSocket connection management using Socket.IO. Document the event types defined in SocketNotificationType.ts and their payload structures. Describe the message broadcasting patterns and room-based communication for chat functionality. Include examples of emitting events from services and handling client disconnections. Address connection scaling, message queuing, and fallback strategies. Explain the relationship between WebSocket events and tRPC notifications.", "parent_id": "5b381073-14b9-4b74-ba3c-f2ceb3abdd90", "order": 5, "progress_status": "completed", "dependent_files": "server/src/providers/ws.ts,server/src/services/depr_ws.ts,server/src/types/SocketNotificationType.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:13.9177386+02:00", "gmt_modified": "2025-09-12T21:45:57.7186992+02:00", "raw_data": "WikiEncrypted:N3UbzwGjCC3xD6BrTM0cqxys2N57tTp2paMxLCqAh7nTnHCKn8MuGCWqnYMfxMXTks2l2798oUT0Ls4RXYH1OhFiZJNjbAnvGktHAtClBbuBfG1gK2fFEoCQKa9SJ8houdiv8JFpMUtJa4jgjtLEd2nLmN4f/EoGMcC92nvsjJCXtEYDTbBqTPODRIKmT7tHBRXSPhgPlbrF1137RBn2BRVgzp2y59UDikpwkBlO/csLjnTJrY1xHdSZX4XbFE2IGx/+27GcPiAfX1h0NVZRPBlSW06PiYemiReRMT4V4t9I+RiY7EsLHhODA9Vy2MEYZjR/UzrF7N0h/ghwjHpb1ktwgQauDaA49rT83NTKc1iGyTN4DV55hFuf82fQjwQH2LY+oCBlb9/bT2a2IEqfh3Yk+OXSmjX2PxLofAn1YRn7pCWxRhYOzUWQMAzT8d2W1UzRRin5bH+d9MOuWY0WEabuo8Sr7IC7MkkB+usDlLJ8wGSi1r8/bxOg9XD3lwDWcGyc9X7lL9vlP88Xyh1rcql7ksOxh/QgfvkXnTPnFdLdWZGVYSZ7D2mj0OMnanaXNDddtRWdABdRiIgv92MXf6TiuMNJofqLE3FLpiNv1tCq21tx2NHmqtm3ilsDCjW+MKUNXeTcUqMrqtxzkyUOZxM+iD3+aBgqRbEyKYbRm718QMobUax0ezj5/5uheGlm95N1kUtWD/mj1WCV1BlI7k0SKIbbHMyzTTP1MPZSFTgt4oHwXm+HtF9tLsUP5l24RNdUGA4QdaqSVBR4mxONf2+iF+r10MXXvkQENC4hv8KJPEHSlfLwJcMNdjfDMkK33t0rHyORjsnbKEFdaKuhe4VJjOvlc0ALahN4UOfmK4u0vI/qg5IeaphE5hlHT3UYaOBhLoBukpEKtf0GUPe+SOwj4M7zm5dtnuR3rJdKCIHirnkB4UVkJY67EJXoS7VcppY/xbl0WX5WkwiAb55V8ymCji/wkLAUsi62hfXZsoLtgdNummUrKGlMcoC5yzOiSRAQigDKePfmCSfLuye8SmLXQ65mGfbjBWfLPLEonyZcRrAziNKOTtTGKYuah3XTkWYpWtKwbb1RiLJBopGrFxiGtjQMnJb9mDbL+iWYM2Olsw8v19/6GXWBCqAu+L9nP0UCLsOvOvcwDjFxCH2uxrt+rw5pkE3H0qI5YPPddRqxF+8B+wCFk7c9hnBzn9tTin7+y/M7Qx0ezwjEU4wxm538fdSHj6L1VGMXRing9e3lWcv1JXd30cziWo0nbRQt", "layer_level": 1}, {"id": "1507f131-314e-48fa-8119-b3a3ca22ee60", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "User Management API", "description": "user-management-api", "prompt": "Create API documentation for the user management endpoints in takeNpass. Document the procedures for retrieving user profiles, updating user information, and managing user relationships (favorites, ratings). Specify the Zod validation schemas for profile updates, including name, phone number, and language preferences. Detail the tRPC procedures for getUserProfile, updateUserProfile, getMyCases, and getUserRatings. Explain the integration between user data and other entities like cases, messages, and notifications. Document the privacy considerations and access control rules that govern user data visibility. Provide examples of client-side invocation from ProfilePage.tsx and ProfileSettings.tsx. Address the data synchronization between user profile updates and related entities. Document error codes for invalid profile data, unauthorized access to other users' data, and constraint violations.", "parent_id": "e29a5c0f-d32f-4756-886a-0c99bdfe8bc1", "order": 5, "progress_status": "completed", "dependent_files": "server/src/router/routers/user.ts,server/src/services/user.ts,server/src/types/User.ts,frontend/src/trpc.ts,frontend/src/pages/ProfilePage.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:39.4163661+02:00", "gmt_modified": "2025-09-12T21:46:58.4701862+02:00", "raw_data": "WikiEncrypted:piKNT601qxwAxFbFc+bbN/slW9oXRgJ2J5v1hHOGyO93wrAVbKtbHoZcmHWt6pK54ZvajC7ZP5s3g8E9hcuCum9hiOVotKtLxOQg3cSbbfW5r+D6aSsXxTdVquvb1hgQ9+35W7rYIwpeZAkBZLDnhE29xyBYh5L/pymaj7uylFNgcaEgM3gpnC52RdIfknQ5O9FK0E+sJyeLo9Jg7ubmqHwg/BgV4UGD+i9tPQ2uPVB87Dqrm9m/PNsYP4N4x/UasPSIMiFl4NjOoZpu75d/hrNOPpm+YAghNXyRfWCLCS4g/nXeJ8HPJKMyej+4uKLzYMrp7VSOLfbopZAHltLOMiTqjM/Ope1sDONmJ89iZ0KOpyhvWdpA9eRRL211Eid4yVBFw4ZpQfQCdQsVPN5YImRXTbP+wKNCq90poo5ZnCH3C+Gt3lM/ioMCRdKzJzpza3MbtW6swR7iAZfjwf92MjnCNerQOPeDja0TeKY/RMi3LuiRwqsyuXs0LpjhTByXZaKY89aPzcbFO/If25CYvO5NneyUnXj9HhcUaax/GKOz7zJFzqgZRICJ40ggHEYsfmDOlE1e4aHw86WL9wRS4dCJMiFBaqQ+ysWItRR15ziXfNbSk1vIAL0t7oHOsEIancnXsINw56mu70wM37IRKu+dgKWziJMWFXYzzrmgyeiXHNYW8GLcOGMBgSvfsLRzaIE/gpZgc49OqVGkt7icbgiQajGCGiN14629Iogfds8k84n/0H5JMXkC6ql6TzBu+2mUQloP2/B+Yc9mBnzuA2lbPk+6wewylsqZ/ZOjiJN2BqBmtYrlleFmp0OveH/C53fcky+GdIDo/PKi70U+GhBGwNkQZKbvYcmLBliP9xlTO1xwon6AmFQhGyQVp1NvgNkucrWIWYpCt6xnDnS2C2VtKIyUPPUWhODbfjWIAB1Q+R3kyK+U4nTgYK4D5xjP22NAgc6xvV+ThGZ2tfy3qLh97IYFHPSCYzI55NKfnNHV/D58MqWE2/6iVifEN3Mvzep2whzzeMyYTyIHAqmhc3QH67MHYo1v/hL0QteXF0c+p8z3YfCwu7Nhd4APOYdlple7Y3GpAER5RnrGZ7l2x+Qq02Lc6BBaPeO0H9jwn3aCV4yONQN5nnMyx7LkPcRWhuYaH1R6KMrn2u0UVr4gIC/DB5AIuQuWkzHLoGd+KGpRAI5h8i3ThK7Cs1LGT+hEj4p82pVBMukAJVBiBzLwbGCXWxrg2gllw5DjnH3CBbCc/iomVAVzvHstyElAB4FtLASsDVDPQrEqNkRQEGKsCzqwR/d5VBlHxuAf1DV25poSli7ZbomYb5uqJ2pacaI3tsb8IOCDI4rV5E2H0piP4902Eg+Aeqf33isSxULCYQ5MMbWHLpFdwjEuh6T0v+8DnLID/uCuT/VFuzKXcs+4Ej/rpSKc92Pg9xZGkskjKR2RYG3Zx6GQgN9KgXD1aW8QGKG1FDxdKzp4sAU6LERl2dgNF6MS5LdsR6P4+lSWUMZQBsjOdz/tPVeajhDpTkSgsBbXWBkhLuTO2kbcbMHTh0TAqD3L1VTpNUcQyHp1H977tZzqKEjVv7OIaoSe9ZT5ITQs0cRoMNsCAGg+LW/ndcqIdR3bgjEb3UFxq1ygsWgZ6T+jzX+iUc15zCMqefU86Pf3yC54sRWANFlH4AJmO5DUwIxYfIBVixWTaMw62nN49qyb2Q2fXjxzd6x5z8ry", "layer_level": 1}, {"id": "3cde283d-52e0-4463-8c48-3a91eca21fcb", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Waypoints Model", "description": "waypoints-model", "prompt": "Create comprehensive data model documentation for the Waypoint entities (WayFrom, WayTo, WayMiddle) that define delivery routes in the takeNpass system. Detail fields for each waypoint type including location coordinates, address components, arrival/departure times, and metadata. Document the one-to-one relationship between Case and its origin (WayFrom) and destination (WayTo), and the one-to-many relationship with intermediate waypoints (WayMiddle). Explain spatial indexing strategies, geolocation data validation, and address normalization processes. Describe how waypoints enable route matching algorithms and search functionality. Include information about time window constraints, waypoint ordering, and their role in the delivery matching logic. Provide sample queries for route validation and proximity-based case discovery.", "parent_id": "58a090a5-c63c-4022-9857-99fe4f06094e", "order": 5, "progress_status": "completed", "dependent_files": "server/prisma/generated/zod/modelSchema/WayFromSchema.ts,server/prisma/generated/zod/modelSchema/WayToSchema.ts,server/prisma/generated/zod/modelSchema/WayMiddleSchema.ts,server/prisma/schema.prisma", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:09.3319719+02:00", "gmt_modified": "2025-09-12T21:48:44.7331717+02:00", "raw_data": "WikiEncrypted:/4c1vj1R8jMzqAhxqZlj0sOQwGoknJsrFcUUSlU3DHXXlng3w3JfiCpwFhP6vAsYW0EDC0BcHJiQ9ko77F+fQ+rW0e3qF13CI0qvWOHdaNyPv9kszWt7nOUFmLSiBaTG4HpLRMmtghXYQPHHz1OxQrSURD/RQwaWXFFA12+uMIgJtCRQbirMywqW36q6LZGNkoRFEpnL7Qi1Vn6fYyICrLCEOdg877wBXkK1lxtlPCJeB+A/i7Te/TVHrnzDaxEV85HR6S1a6sfPJqe4Bq5hDUT+pO7jQZqS8LpY+zd0FVHPyEmeq/BWwV56GdZ7lFV4yn52QX5rM9AdskU43A2a2UOdgAsqPfiPgHW0Y5Jagnm3/tifQpkf9DLvjB5b0puE/59FgATQz/0ifLrTQov9K9AZVduCsOTg03F7RaULQw8izlN2iRDQWYA3znykjkjNnvLtfLmmdOtA3GOCEUWyrPeBuwqYVuZaVzT+Ckk3R/RaO48bm5eKOJ2dwDFSuUHsJHmC88iyX8k4ebBAc+7OKBxQTiGA8VGzOaF/4wCvJ+ZZQ5jwduVxcmG4xaYm3xsV24iXHjzMr8HhcW12T9yC/cr4ZDHl8G49TTTtl0sDplHXB0vZYlTzOtSWCOCBXKEsSqlS8k27qDLoYWEUXPL3MBo67aT/FBvbvVuAs1xni0PO22WBK8XSCV9glQH54vFyB2eruuC2ME473JUkSx9jZg0pQon7527k1+YX5h5Ldt69CfjDC88LfsChONrKIIAxZw2FfapJM7ClUIASrynx3RIuT2hV4a3oLE6wxFuy5TkAP0oZyLcSiYmbIlNkhdjuygcQ2RBP4xWMA8799hjLA14ENYvBwCJAr5o5LfgBURU9M3EAgIXK9NGKrZBeotGihQaaejhGB9uLmZW25sHv+KT8EC4Ph9A3x/wps2vZC5/typSXUhCLhuBt+O17HBNxCNwJsehhWFAR22LLmNVuMfjA3uzrpD6kVusHU4WuTpAKrRDLsiMp9Vy5brcgbLGx2TDSBfalawJaIfbiXiNI09mkXuxGXJA+rebuGPc7BC3jeTpyzXgfbSc8lWlF8gKWo2txzHgXweQC2QkHoo3reRDCZhfhAc2LcgCW1Id3hyPJmjRHELGQo5eHm2rvln7mkpCJMuPdivuJWUaSqbKg1PRbIZDB3c+/Y6P3zE4wdqIGziGMkPnKJLUM5pBS4nh2ojyuE4EAhBPtEqT8Ke6OE72N2zNPzVHjwdTB07BIHB50wQ6jmwk++8Hy1a88LjQS9lGvCOAv3CTyLW41A5ndI65OgUoZNeac1CjLZt0MDzQAb4c3Q2QFo6mJ8mzhv7oxjsHd5VwYifj08w8KXalYxatnx2MyCSUaWSWzp8oUMxeoUFihgInEqPR4Ihnl2AEDFESArhL1MldwtRouHzcvHNTymRx6zdSnLyI5avY+Mg3sQuxUCBULl3tneAmMXRGUJhwIy1dUgjOSfJbAshXTRaMFvE0fFuuMb/HiaIV6ne8IVSr2rAU+m5kbchHRz8rSYXKR5hquGqawu5sDQ6PnCsYAjdttm0iBKMzokXb7dAMFXbu1VQrMO6DKdbuL46tE3UF9LaB+OAiAUtvSzi2Orn5v/qhIKssEIoBE7E1MoCAxEFl5kutTMO7Q6soPWwNEr+ZqP+o99q4rF1UB8wqbdGq+7959c8QujfAQPj2zj8Q=", "layer_level": 1}, {"id": "d46f999c-b613-4f44-9c86-147eab21d8c4", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "UI Primitives", "description": "ui-primitives", "prompt": "Create comprehensive documentation for UI primitive components in takeNpass. Detail each component from the ui and magicui directories including bento-grid for layout, glass-card for visual styling, and animated components like sparkles and text-generate-effect. Explain the implementation of cover.tsx for hero sections and flickering-grid.tsx for background effects. Document props, customization options, and CSS variables for theming through Tailwind. Include usage examples from landing page and marketing sections. Address performance implications of animations, accessibility considerations for motion effects, and responsive behavior. Provide guidelines for composing these primitives into complex interfaces while maintaining performance.", "parent_id": "7e5065b0-32bd-4cb8-a5f5-554b801c8b87", "order": 5, "progress_status": "completed", "dependent_files": "frontend/src/components/ui/bento-grid.tsx,frontend/src/components/ui/cover.tsx,frontend/src/components/ui/glass-card.tsx,frontend/src/components/ui/sparkles.tsx,frontend/src/components/ui/text-generate-effect.tsx,frontend/src/components/magicui/flickering-grid.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:58:36.370219+02:00", "gmt_modified": "2025-09-12T21:50:25.1523101+02:00", "raw_data": "WikiEncrypted:i6Z7x5HBrOxGa7Zft7kWr9EuWdJWtZUNdArq9/4oP0fUJsJSSVRuyf+UBmznyWiJesSNitj1ZXwWj9Cexf0b7IhoFn+SB0+VJIOKILSD7t8ytQ7ymkZLnN4Dn3SnC9lsveRYgq2+DvJL+HSjHoVg27jRFnkqiZI+szQI5ZXcO2cAqT+icUzV1bjvl6KguN1HGRQjL33S/YVCd0A8LC5eQSwf0HUbiHc0dfkonmZ62ov5NXBwV98DHKGjYBPNejrm1mzmgdhL+XKrq0CbIKoiC/oMPC7RZk8B0tORaeQEjGpxTZpYbaGkWXz1WeCGyUHsGAzmfXJV7KlyEi0W6cxJ8t/5clwNO0aYCRlfO0IrG8KYAp8oZ6Ijkg0/YWfcNcWtsqHjah93ZlyIlTHpLxUIQfQhHH8jBy2tzP760NEEg9NLbLBNwwxxo0orAyjnt8CUN24fVFa10d+uHWhtvvgGemUgN3kizDdrqET8Mpb3MFx1GglEwil8F92TWIcZMLhOMgtHr3tyIXX5tzw29lBDbk6M/zxDqttgFzpVNuWVp4WEC7LkrS2Xk17Lk8gMxS0Qoj0rJl63YjNCyeVohE8IbevAsMuClFxFFfj7LaC1Q2STio3R+h38eSjoHe0M4Q9ri2qjX8BN/fI7ONDsJylUADblSHk/Yyp+sWWMhnm/msFb6GKpm2gMa8exvovKAC7Nf6SyaKIONTsmc755XKtzrDafFFI45AzJncS6m9bE2B7sY+IPhrEiO9S+c54T+EvjddIds3Atwyppk4sJN/240NKo3ulD+VyY3ZaoSZLjvp4vbSeHGKshOQjB/Cs0vDO6HNgjUCQa6r5xKroBVDUEHq0nWMNO9ke4s99D45issCbPgcZfEHJM9JDdaI/oBSUFOUbqUKUWwRwnpFTZgU0FTf9BTlytMnbjM7FZnQA0r6unwL/NzsiIUGhtVLbU6zjjGMKlGASkrLiHxT/5/KLAJTabDSDUfQokFNulECP9jc9TBeLwYORPm6CeXnpMeZxaoI7XLZv4R7Op0i4wmPNnGDydGRkj3qWBKFxnipQd2XnTUKNO2GpgRByQOUnXnGvvEEcrsuItYTLkvGAWMNGIO5e06WM36Bw73v7qaPc9oLZ13RljkLC6zRtpAmUGF/7cW4KCpbwJtxwMyykbkphb/mk9zonx0BzJAgsz/fw5ISDmWZt4lXxOuoKy0CwiFYLz8HP0nKzs49AvztKkv5gLN1yCgRmDytjUPY9MWXRZ3VZZE5oozM39nugmpuFZxYoxE2fQCTDZBG0UVvFPuLbuYnjpdJnCIU58QWX8adXzywMUUTnWaGY+2yiFZqg6T/j0uFgmK+VB4uW1QB4EEYYgdDLHIYstxeV99ovdp/hY56OH6Hj6Rn0rjd/wJiCeLsTBdhnq8H6dxVizfFnV25dMxrVq5oXGJTCzp6ZOJOEkTOkIwpV2lk79C/OJR2q72BgegJwf3ymVzmdRtfPtmqOiY+nPdFDNkj5qf0Kz3xOljjyl+jEQfTRTqTtErluxtkW3hGIurLcTx7Erv+eIbdnz2tl/qyzNFbEbrGjAhcgzFVFIbUVYDZk7ZV5onfI1UY1FMi0vCOuuKrFpPI2QfSJUmNhyDdk9U9JEhJkTi9ucFzt0CErq9tEAnj6ZHmXpAog4hECo5Sd0WKWKkQwKWfqLhA==", "layer_level": 1}, {"id": "58a090a5-c63c-4022-9857-99fe4f06094e", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Database Schema", "description": "database-schema", "prompt": "Create comprehensive data model documentation for the takeNpass database schema. Detail entity relationships between core models including User, Case, Message, Notification, and Rating. Document field definitions, data types, primary/foreign keys, indexes, and constraints for each table. Explain data validation rules and business logic enforced at the database level. Include database schema diagrams showing table relationships and cardinality. Provide sample data for each model to illustrate usage patterns. Document data access patterns, query optimization strategies, and performance considerations. Specify data lifecycle management, retention policies, and archival rules. Address data security, privacy requirements, and access control mechanisms.", "parent_id": "", "order": 6, "progress_status": "completed", "dependent_files": "server/prisma/schema.prisma,server/prisma/generated/zod/modelSchema/,server/prisma/seeds/", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.3952726+02:00", "gmt_modified": "2025-09-12T21:09:21.0384134+02:00", "raw_data": "WikiEncrypted:veTYwq2y4io5qXerCTrkkKY19u2QVJ1rg3+MIy4jIV2G7991jW6cwRmNYXwYpuW5ArhlxyDWCj1MQluATkttNfwkx2UFQ5nN2d6h/B0AgsI75/txfabRW/DwVa+oB0V7V6rtu1Iaf7PQKOWDrV4IneZGwP8ibJp+1ye97nbtMZ6VoUfxqBqXiuRHRL4OsekgU+Y2fcrViyRcGun/MI36LT0BQT7uzX8jrRZSaGkDMkWp4GL9tmyihnt21x+HQ/vN1LBpoXpXLa/zNaz5amNvQrruFPgAxnm8rY+3joGHYJg36FB2ehyb3/w+sWTv+v9tFiMDFe3cOioC6uI8rTnm5qgVwiATXhdDkplkhfmDFwxR+9yMrOam1qchk2/eGhIGeFWOoLi8//v3ePTU1B76CS+KiLbXCUIoB9Mu0Dv0yqwBB9IR55wIkMHkGwNWqCxfyyU4S9YJccZ22EIlhVjGGu626vN9gnUbkCo2xwj62gPmbl2iUeVPbn/cdPaID2XAitSOva5kgEwO6444284c86PLtAj0V///ZEOEzRamU44/bylJeGEKvVCNE+tpptXAm7cx7kuzu9vYfHbtPnj0EAIrsNeKCoABxhNXhkoDLLal/JSEHvcqxiz0ZZIXcTnAJSkEOhKeBKQKUjXJVHgVFPVObwM6fNow6aTSPD/No2E5pjgIFECSS+F2ow6ktBd7LAgsIwZpStg33jciVmGtzZ0Hbq/f7gdzSYYQVoLQVq5YlVJh2rwtarCjNqc0spHO9lr/GohTbBb4BweeWV6Ngg7cdBcMYBPPs+TPk/c6i2dQlySZ0r8m7QNzqAXPNVGEUFXksEjpINMcIknZR2+rTgFpKo1lKkp9+J+KeJhEOIffZdJhBDuIXyhyQvJOte72uWvTUF/acVmZg8f6Tw2P51KTRDdJb3hM4x+wO8xN7XRAJxtpn9Lq9dQBDw9tziQQPGvSZXdrgO2ErcGkpd+GCgmwamgGeCKhxasJe3SaxVmABfMaKqcjcLJZXm0xV94nNOwIla6mROocyLeqDjNiBX4FDOQckiB9mTdXFtJkOYuvWp+wVTioJS2H+/ibN9jXX+a36ahkAxz2Czq2BMJs3ouq69OO9r6Mpr98/fcFkUXKkxS+XSnxp42fpZHG3sxF1BNC0+F9wVwZOaQEeUlX2sMLBMx2QTb1/KJHcIl31hUmX1D+m30KWwW2aIAK+m0u6R9Lez9EEIQ/7lZ+Yw1Du+LFnl9U4oPearLoBgevMLrv6dsGThXp1+DrtVjrCywEm4ptZ93D+Kri6bPpn8Owp714SrKs8hCA41Hc9FxRGvs8OzMOpKU8SX6E50bVca0OSwLF60NjMSeJm3DNjpxihiyhr0gnO4m9ryo4QSMjgytLoG3axDxZoPpd33zP/R9w9fG/jgT8gvcjpl+qw1icanD0ryGYU8ge7eGoztkph00zVV21jdnh+axgUAz7rFsQZcTSF/agupCVV4VytCb1LSDzegUlHkOPTAETbbnz1tg=", "layer_level": 0}, {"id": "e5a9ef46-0aec-4192-aa57-8d797f5b3064", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Utilities and Helpers", "description": "backend-utilities", "prompt": "Create documentation for the utility functions and helper modules in takeNpass backend. Explain the email validation implementation in isValidEmail.ts and its usage across services. Document the rate limiting mechanism using rateLimit.ts for API protection. Describe the Zod schemas in CaseShemas.ts for input validation and type safety. Cover the custom types in types/ directory including Geoapify APIs and payload interfaces. Include examples of extending utility functions and creating new validation schemas.", "parent_id": "5b381073-14b9-4b74-ba3c-f2ceb3abdd90", "order": 6, "progress_status": "completed", "dependent_files": "server/src/utils/isValidEmail.ts,server/src/utils/rateLimit.ts,server/src/shemas/CaseShemas.ts,server/src/types/", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:57:13.9211246+02:00", "gmt_modified": "2025-09-12T21:51:44.180628+02:00", "raw_data": "WikiEncrypted:N3UbzwGjCC3xD6BrTM0cq2bF+FxgAFm8urlLnVP3oCErnV/uj3Sb6yNDyvn92iGVHOqFomFVAfk3qgTE+t4QTUnUxpcec9+Vj6EXnUC41Nig2lG4YKvgjG5Mzh2WkBCU3yZ9vfJqPG9yPsX4NS/6KQz+yQ1/KUOmTr/gt96eiNJ4N/bzhKpha8yhKD6HC1FroNMPiBl9IcPw1oy8n/0Uv7jtkk6ZtufUrnZFLZDYha1Fa8Xq9JH8zS7L/xZanzW5BgjoRdVMzeATLGUIUdwKDsLx+DSh96nAZUU4jzqRVIvuxPlzJFx+pNuipPuXKO0NWc/6lXUp5jThVZf8bS1FkMSfIIWDjvSGmxos8LEinJX5ijB3PLczLKXHCmEWD7Xcm2m3kMFRhNnWChXNBnKqrnzFqsiuXtTSCwwqm3IVFLhlpUepBxa7NoNoYkYf+W74LgCGp0DBqSoaB0ud5jYctFuTwyghEp+OwIP+/dQ9SqrzhrEH5Ierpajnv83ckTfBsoMj3LJJPVo36O9FFM+7l27YkHjK/0pb/Ug/J+R+H2XtJI3VVWTiqrxf1Pt5U/WY/m1BhQ2doqO1Ex4TcHUO3q3dCNrghJUA0ONYKqoKofrilzappeCfXhR3D/NAMeys1HDY5PLw1hAbpn7YXxpAy/4jU+MPUg3EwY7t5pYZpk63K2xQJGFYDzEWc0Cd/uSDraiNkb+i29V0j+w+LbRzjupASQCpcFGfZHzVFdfhFDsr1mYsaw4KTsXScVaLgf9ONIsyZqX5yLNeK+4FL4nfFeb8TCa/NvNVDeOlOkh180bgErlGwiIboHWRySpBuBD0lbTab9P0WuEwjSN2PPJabEMY9O3OJeDLKNI+UhucD9iET9qQrGxwsoM2+Gv6kjamSZ7LbcZMsm4osAw++0Iwwkq+fS0A/QUCAaLrIZx7sXWfiTBvMmL5IqQucuZAWn9ZCAFjnr10QkyicMpJqPUEUXmL9ERjxh1ZyDlZgQIttzg8bFI057o7rld6Z6TXTv9ZezsDQhUieVjd/aNZfY73fy67kM+e3hQ4Pt3lqOGAsG5ThEyWjLzMcCKnRdVrDRjg9X3z7zvU8hUu8YbmCiAf7A77XlEa5piLRJY7x5AqyASNTaWKbGdBPHnZNSu9yJGnh5RrrQKl5+YhwK8nOeZsfA==", "layer_level": 1}, {"id": "fb9e0ea4-1745-4556-ba9f-b3f0483e904b", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Authentication System", "description": "authentication-system", "prompt": "Develop detailed content for the authentication system in takeNpass. Thoroughly explain the implementation using better-auth with credential and social login strategies. Document the complete authentication flow from user registration to session management, including password hashing, token generation, and cookie handling. Include interface details for login, registration, and profile management components. Explain the domain model with User, Session, and VerificationToken entities. Provide concrete examples from the actual codebase showing form validation, error handling, and security measures. Address common issues like forgotten passwords, account verification, and session expiration. Make content accessible to beginners while providing sufficient technical depth for experienced developers regarding security best practices.", "parent_id": "", "order": 7, "progress_status": "completed", "dependent_files": "server/src/providers/auth.ts,server/src/providers/better-auth.ts,frontend/src/lib/auth.ts,frontend/src/pages/LoginPage.tsx,frontend/src/pages/RegPage.tsx", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.4001771+02:00", "gmt_modified": "2025-09-12T21:10:51.4299206+02:00", "raw_data": "WikiEncrypted:uxAckj1IXK2TQH//kEdeBLXlO/kfAAqZjShpoDF/Lwy2VscQdYqR6q88sG6OmGkHJT0JBWifMfILW0uPvzceLhXC2aXGWGYRlT1LGa3Ofj5UXCxWKEUmritAAm7Pxr4JnJlXw2sFjjNfHmcejeX68IuTb7gk2bCLahYR/ZRVlXIkfhiwdwkYUVThyezEbq9S+rRjyoNx/z/ArtiuVlSL4MCbM5tv4tj/Yzp2rgd/G/r2c8JtUzx2KADJ+ozzOTbMe4daZSUCY6MthFH09aQqVz8n5ccZa+2CalBLHybodK2qTwItNGS58WISQDlgKoHkLCyzH6ZG4uY1eEoveEC3rnJ8NF8K0mkZNo/l48Dp0xi031Ro/WyfsxQXI+8XdVGYNE9ImkY6mGcGJh5hJIo1N6h5gvY+YDh+IDMqE+Zowr6h8EUtRY3grAswnEzfFRbuib+eP+3wVANJvVPvu9uK8HtL+33IG16c85R7h3k2Ua3yfLk4GoQvTHB7qmS7gECi8A7iuzKCTrEEwLhiP6oMAzmVhAHkxbZYXONVVypdQ4POukOXSmc4bo256Vn8nF/nXXtZa5ykp9H0d0XjIQfLOy5GndWi/blR6OW76dQMeW/Wq6Cs4poeBUzhi0UweBKGPW/uUBI/YwwParI+/CjJTHxJwNeWioey8/Ao/xzAwdQ/BkmeoOx1wAqtb13fkM4gknV2FFh1EUQn3238p2B7+c3t99ZvCFGAfmm1lOo6Opd2N1NUoK0MovgmNg9dwhqzpqbT1/Y8LEYYZxrsUAwNkDUcR2RmOM7Yf39T9bdG/2GIRzas+FGky4WbIm1XhTn1sfYuq2RBqXsr7llK0oROhK3oYhBPO74kQ1TS2XjKd7qLIBuEbB7WQ1V1uTLfniUBzvCKkCTnQzmTsUV55utBKdzw8awk2VgVxR+0w0KWHU7XR0Ckn8WB0uiYQbbP20I5eGYuHT7ExzRsUemjwuI039h2QGOCvAVhcVE1o+UtAP52YOWW0evq9fQY9swtL5pMxHOPWQtMaXAai2gC/vrnktPNnlh4OyoIEzN2tQfZ1GKGMj6HiQvirle3sVMVSFvKoYqIcBwruYJCE6JqdGsTVpC6DDGEWU7wGoBW8KVSKJaGc/SlW1KdLHjB79UsCqfRnU4stScmxZAnBNT6Hp4GTa453cBRTzl7s4RmuvhjGjV72Jafn4Nbqjyc7uV8Q5DSMZHg5Z9hDZNqA3tu4tLW6aNxjLbtr3G4WUyrT6jypHi+4SvJsZ1MkKQ/0K/HMWqqxi812CypCfNIa+LQL53YPoKS6IAv+IAoG8+1qrElb+lAS8LwgilS20JfvzSudm5+aIfVu4z575uaNcdWApveRXY5XBSRoRjkywQrr1WWOTVs4q/vpf2KPr3iLG/IGcSw1pqp36xOYi/jiDnRdh+rAjnTayVP0/2qcR3A1m0sZzZgtKKJoaP5TEK+MNVww8X0YwrKxLDolo1I5d0qyPTBnnio8jXJ31SrwPpd766e8h2C0OgcGDWPOqQYdkiwcYGqcOB+7P0fjcnJPm2HiimdGaVL6FfL/2gyPv7X187g0KKSk4VAxX2JhYT87hOBf2+zuXWSPALrOi1Xjp31EB6MvzYPKTM6+fr1XcTyxs1wq44DZn3sWb4sjFrNElc/8QUk", "layer_level": 0}, {"id": "e369a098-8b76-4668-b68d-d974b8fae621", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Real-time Communication", "description": "real-time-communication", "prompt": "Create comprehensive content for the real-time communication system in takeNpass. Explain the WebSocket implementation using Socket.IO for bidirectional communication between clients and server. Document connection handling, message formats, event types, and real-time interaction patterns for chat functionality. Detail the server-side WebSocket provider in ws.ts and legacy implementation in depr_ws.ts. Cover the client-side socket management in socket.ts and chat state handling in useChat.ts. Include protocol-specific examples showing message sending, delivery confirmation, and read receipts. Address error handling strategies, reconnection logic, and security considerations like message validation and rate limiting. Provide client implementation guidelines and performance optimization tips for handling high message volumes.", "parent_id": "", "order": 8, "progress_status": "completed", "dependent_files": "server/src/providers/ws.ts,server/src/services/depr_ws.ts,frontend/src/socket.ts,frontend/src/components/chat/,frontend/src/hooks/useChat.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.4041249+02:00", "gmt_modified": "2025-09-12T21:12:31.5798328+02:00", "raw_data": "WikiEncrypted:a46miLh4B5C16RNAKII6IPYqtJXasRNc0CfJFQI8VOq0c9XIKuooE/XL6GZnuQkiH3ik0ouY6AKZ9H6ta8AynssDcJpUzIDh4ZK9mWFDsZ5LSaw3rm4RinXSFLUGwwQrfstXIa7aeHSIlbTJyXCOLYrcB7sfVdeyxq69gldGf0o7UWhG9CwS01OtI5f0o404jXAUCRYp31W3RUf5mquqNM1SLt5iXHLP2Yfy1tM/Pr+EpTWwGFJ9j7zgPrGFQW+DyvMvtBu5xEyKa+3JeKqBNbzUBphX6DWpqMJH4Bx6YraIKyv0DdT/noz8FphKzIGBZCnqx6njMWvf35tUM34NW3KHkz+xv6EMpCyK3QwPpjMGBGTQDJKVjngxjB762baGIvSLh7Dhj7ZC4EsnZ9Rhn8Lrivvbb0TyKs4Z3KKIhTz/2n2a3nq+igkZCe8DRpB0iY0q3e6h8lAu5bSqSsQQO/PCpsVjgrixSOIKKCitWks+ls1OkVgoHV1R3UJm52GtxErnB10W+fPVGPtADGDx7aPHaFt5gZPh6z243UDRWIYxhAKO9X567ytrK7Pb3uGRVy92IjP7sw/J/5CJ5wSZHSDIiUBklqRccff6KnoGrQU17WMuGqtYbsltNwy8VbshMenVAwT+XPyJlcuQwanSCKTSQXn47C6EhAZWJdf8PJwezbOBsP01DtwbMFZYuqWPtzr20t29dvlFTfyOeBliO/cTCkof3aUXdYxlXwzyt1AOWg/2RUrNRCZ4v5g/8SjXm4wO9tKiuY6NAokduGJ7iVKbu/yy/hhpgK0pLxGeI4ztvuCZ9nnKG9lpbiHHVnpdQOGyAdsDzNxDH+2IUt+pil86Eb9JWLK6zZxGEEfQA/6y83yfVqsZ+bPRrcdKjDv+XICKotCSTOPDb5VeKPwzYGg9OlQbtxNwqgaYP0DN/hwzRIqa/oiHu6VJhnkIx5KV6wgbk+HspvjWiCKajSnxvaQrUiuv54WN2bj9yFAT36UAiNfyPZkUfS1jM1zcu+IZVKUgwTCLDuXRs8cI6Prx+CoXFNqqaflU5/TWfy9tw9sfNhtl2K3vElLo5j8vlGn/Mq9B6Lg6xgBDnWbzEAoSVi+/2vMeKmAwgDOsyGDZyUx0OxS75A0/V97NYtd4pt4UIKq1XLKktNFCjJAbL7jpbMFQb03x2Clfi2JRH6t2GYwEieSnvi5KYzjuac6dM087Mjc1+8egZIWkUVxu8CeXD6ChpmE5Oq7iVNB8ZZ4m7TQQFTIqCuUrSJJwwjBzJzuDGfgGh+H2ZU5+svc2VcTJxS382H0l/GLtsAKDfN57PSFJS1q+U/V5tG+o3iyiI1s0q3oE8h5fiKqz8cb9Wd/q0cjyKDo7wdvUCT6it2mvncVRh2xYrQEwvisjLsbZjgKnyYZmWxqW9Qi79rPwq73jLtYgonuAD435em7Ffex8AiCaW/5XO+i4Q5q01qPo/iQCvX6SzW9DN6MrU6+dbHmHhwJJr3F8w4hNyz1xsGCsUrW5vdFKFgBcjwy+uEPUAIysJeSRSUoWE2XEJaoX9HzpbZob/+LwUEE96SqdgoaKlv0Sfmf0YEYs/MV0WZd/2ICj1D8h3cIc73NSS88MikXW0R2nF9YW4xZva2P2K7JerRw=", "layer_level": 0}, {"id": "7e5065b0-32bd-4cb8-a5f5-554b801c8b87", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "UI Components", "description": "ui-components", "prompt": "Create detailed documentation for the UI component library in takeNpass. Describe the component architecture with feature-based organization in the components directory. Document each major component category including chat interfaces, map visualizations, forms, and navigation elements. For each significant component, detail props/attributes, events, and customization options with usage examples from actual pages. Include information about responsive design implementation and accessibility compliance. Provide guidelines for component composition patterns and integration with the overall application. Address theming support through Tailwind CSS and style customization options. Document component states, animations, and transitions. Include best practices for performance optimization and cross-browser compatibility.", "parent_id": "", "order": 9, "progress_status": "completed", "dependent_files": "frontend/src/components/,frontend/src/pages/,frontend/components.json", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.4085031+02:00", "gmt_modified": "2025-09-12T21:14:29.1647957+02:00", "raw_data": "WikiEncrypted:olAPfnTddGwpj/UgpBjR2+Msft6uTT20LZDm0DpzpfV/IXQpcdAgXAB3LuQaQ+CpAFqr1gzojGi/Hho2VmgQlPvOobYgO/gVRnGcGgxpsZX+24sl4GRZ2PcS36AV+E1/K2TCgBm/wJKZXw0o3YpZUn66a6thq4D+t3OOjtiQ/p/B1JAs2drGud7pYhZx1xirevXV4oEOAjelTXHW094eS+Wu35iARLSmsFPbu3uCn9Sd3u/oDSmuL8JHIk/VeJxL1kEh9zPf9IY2zR//+Adqkp8ZiL/YMSVXryFxJp06T1wf10YsoRmzncoUR4RVBSDW45nnH6DksGYFVS3Hh5qIHNwT4pMImqKKgnMGsV28P9QWFV+ftrA8r9Lggx2xaeMlLE27WwWtQObeKj4NkSGEE9G7Cx57eCclc+++40hE+Sr9Y9bBlbjOgD4Ha5qJLvC55Rw2DTngY8MTUS0GSke71/GjIKSDLZ+nqRZLvLPmxWVMOaipwk2U+7MF9vEUhxAb7jtjyRAuhRvOVR9yoBqQaYFyya6YJ0zu9VmIclwScV19qiYw8Qo+wnoY25pm/OKjbbG+DFdRlPltc96X4uzaKqVLVFls19P70aBU9+bp4+yWR5BFnwaHm09gH0tR2sFeABpAlqvM8mP9W3mqj5hEZ1llzw/KeyAkI8rhf99WP/kf4RC8gbMSF7g7zNS3/DGecn/g/nZEBxeiyOtjmsn7UQePhJLYN1r5j3TlKaXZQNkWcstZb1Yhpy19STnnUix2gw9vByYwjlSsF/eokXxPKeHwMT5STWswU/6wwXJBLxlgIYuPpQRtYhLx//odiWHl2HtoPPtJp1Il3U5vr/ZqbF3730CfKy2a8hM1C0aENweaqW6Of5Lnx36ETOvNgLasgiBbt7EFvNV5EzNsf3e2R47jo1tr4+JsfeZZnGT8BX5RI4/+xOarGBA7CY3McGWHdrzrMOFbXWJ5omcF0lTctIGhwOGPf+PgdwerS0U/OPlb65kZc0arwUqggHRnsA8ELvxuuOui1kvGMcayQiTWvpRSAJBGpBoMNGRMr4Aq33f45UkHAQcNjHf0XtKuqOxCsv1EAV4LkTTVR7P+zE67SIhaVmTg1GbhleWHIMNNUuCiN7itWIWnKxW0uER2g0d9BYp2EEZ0Ih47AAcsTpKF1DRPb7+SQKElcREE2x8WcxGcVvTsUFu1Nxqz8hcQAGiKDoSGPdPdrZiNLG4k7wyKU2Qd5iCeYu/cL7OmZEvHrziEf2bK4Y1ngF70hozBNGxGAKYHwnBtg3yWm+7hXLkYBBaUQYgDptOknLA/kNUbwVHqXYig7OgDCaURFts+JFkokQfaOxvAMmvnrB7jRpUF7mPAyyeV6iVg9D16gcYySRUeqc4OvmPLSQisvRDgNhDY2JapYfHzu0I+0ZRCx1RkJemhN+UxPKKrYvSiqim/aD56tCXNJgrqQNAgqPYiVB8+AEyb/3/1SrIItURgh/teB5/8SKDLUwuCrRwOAM161bn6oLINt5JUvxmPfwgX02N4wJbSMxJKkfJkWhaSvuly8A==", "layer_level": 0}, {"id": "f0053d46-b028-454e-b683-8c580f1ec5ee", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Testing Strategy", "description": "testing-strategy", "prompt": "Create comprehensive content for the testing strategy in takeNpass. Document the testing pyramid implementation with unit, integration, and end-to-end tests. Explain the backend testing approach using vitest for service layer tests and API integration tests, with examples from auth.test.ts and caseService.test.js. Cover the testing utilities and mock implementations used for database and external service interactions. Address frontend testing considerations and available tools. Include information about test coverage requirements, continuous integration setup, and performance testing. Provide guidance on writing effective tests for tRPC procedures, WebSocket interactions, and complex business logic. Document common testing patterns and anti-patterns specific to the codebase.", "parent_id": "", "order": 10, "progress_status": "completed", "dependent_files": "server/test/auth.test.ts,server/test/caseService.test.js,server/test/geoService.test.js,frontend/package.json", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.413244+02:00", "gmt_modified": "2025-09-12T21:16:04.5579623+02:00", "raw_data": "WikiEncrypted:a51tw9+B5Xez88YuZqi4cPV0J1CJauHTZselAZQ9sG40Lb0MosJIfbxZqj+gJA5m8musMdI2GJsw80+O72ORSbcip4yUj67CNUfZbk6l7/MiUJySP/2WplgaAW7Ni/y6zPchPXJE/ccZnbVTeEW70/rRWyUuxSABizdxebn4ThNh5afqfYNjb0bLlPvVQ80bVI8pNLatOU7uVHnOOCLcNDaZKMTEcaxMwHsdykaYE5lwpyur6FZo1JOOFFUhK14/C3QbsxCTeTX82olSkmdgNpiWvnhcQEnMoiiNXhYPzl+1Xi9+gsZmlBFIV7H5hwQkJ9TLb7Xn5V2UA2pWMB3ePAWBCFTAjgjMSgQ5AOCVA0v3k3ohKB8Qtm1IkcGq8z2WMinsPzelXpLfO6zhYkMAZB+RwnudnzhFUFNl6ARWYiUu1ne6b8pe3Og0EwYb6TG1mFG8JjuNFssZlrOfa/5I7IyrPb0of35p88Sdo7jlAqLxiPyS3Z4gYG2lvN9OzkGr0jhQeTph0x/5+ExB1HBxfqR9c5R+wVViw5haLlmuL5nTCUWH1YaE8f7dwq0gTXx5YWHGfIb6V2IwjQQ7QHk58BpHI6PmXtN7C9qY4veEQd+666EF2x8ConbmDKBfJ5f7PjwJ7Jjlu9Pv4u6Rj4Js3AcECTAiJY5A3VZuJRRbLAX7pPxFCZnSpocp5S6Y9kup9LUBcgBE1MS2CnVzBu4j/KUzjfasZgikSJbuJe8b5zw3+3Xssd2n3SBSrstvocXaBNyvJnfnZDyDB2a7BT8HYVYWOEiTF+6lBnSTCsYKXEcMGf374McDcmwjdTqUE2bvrITE1mQ+5gWnzTEi5APh0+fNzM9nubPGJXZJozttQkw/dgw7loy1knblRQEwncgN4OKp/LmA1G0oBwuvaIgxRacozjiqVd7E9ZUp5RzZ/+g6+yOqcKRLb6UXOMNO5252s6qcafsbc6gTHIOclubl8MpUkmiLg3wVdeZMfpU3Yd1ha2biIy/7WeYn6tulyKYk/Fj7AGbrpawqhOiwJTEMTbZ3wD92LBW9C+e8tOjW6lNWcxymxUlG9dxG6Kypd+/Q9dN2zI0TuxNHHC58mrQTUFss23xkdlYwPjlM3e6EZAXVdwUtisYX97OQll5V7CjYfJ6tDgrCC/VDUYlDlRACgvKMF0FRMMitYrxDcBbj6ZLb6q2p1tDfNruA2knGUt9sXRWpwqh6fd5qVe+AbUbj1jq9/HJfBP2DWRV2U0fLDG1TR2zanmf+NXtXKFjMf8em6UBxQHmJM/Y2pLHNe+ndbbGxI4rcY5kFoD3xpG25Emv6dedHQiey/mwF29aRHSHN2f6c9H+plCYlXpEr1eoYcvtNrIHlBHfkeIAP0y1KdUemu5CVlLsk0jp9b7xzDiYPSwXAIlVuYc+PDPLtZ9OBKr8U4H+0zjWKxB+S5pcju3OCbBhuY1vK/Mej7COcYL8ElCIbNsvYlQFpTp6fbnNmug==", "layer_level": 0}, {"id": "1578be00-7a30-46c8-97c5-8b683e5b8a89", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "name": "Deployment Architecture", "description": "deployment-architecture", "prompt": "Create architectural documentation for the deployment infrastructure of takeNpass. Describe the production environment topology including frontend serving, backend API, database, and optional containerization with Docker. Document the build and deployment process for both frontend and backend, including asset optimization, environment-specific configuration, and versioning strategy. Explain the reverse proxy setup, SSL termination, and load balancing considerations. Address database migration strategies in production and backup procedures. Include monitoring, logging, and alerting setup for production systems. Provide scaling recommendations based on expected load patterns and performance characteristics of the application components.", "parent_id": "", "order": 11, "progress_status": "completed", "dependent_files": "server/package.json,frontend/package.json,<PERSON><PERSON><PERSON><PERSON>,docker-compose.yml", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-12T20:56:07.4179792+02:00", "gmt_modified": "2025-09-12T21:17:12.8975896+02:00", "raw_data": "WikiEncrypted:0IKfLNOWe9mZfG1jVts3w6OOPEh83WqH1NPDRxzSZ+4bRz56+VshUpAE4oimgBuG/RKi3jYIBAnCCUy41BjcAkAzw7zCRZNZ8n0KGn+GJNRLtr3Ru6negU7v1+yd/O1WgjL6XWOyd2c69TyWAwi4f6FpSOP2ODKGS8KyeIMf7a/+KdpzDKAnApyEwCnY3g5a7y4e7YoywXf2RS7+uSy0Mg3HUqMHazsrnHRCRfarMf48/DqZo895iHk/QcoUIbaQvm0PZR/bx383tjcRaAxzOgha2SDCHPxbOR0C/Mlv3GRuRpB5LidWM+Qa0b92bu+zfc6KnK46wpGyOHP3n3cNGki27At4ECsZ/Y0A3I03+J2FZ3nyfpLeGVompUB24/Q7RzGiw6qp+TkChWFrXGU6YGQopHGvTI4Vo2EF78RCR9yQXl+j8qVyaMdIoKmUDjY1M1D4MKcAaSUMPC7WYIWFllQIhF7J34qo/rXhFKnxosr+Q9Ly1IKEQEhNpNykc/kodLjmX/945PuAODUCikcy0l6L4prfvOOOluc6t7lleQ1V/rxSEA7REohY98P9x4jEjeKmowp5lh14WcQ5WE/q2CSWDAezCpRuzMzLkbJUkrg+KHioLrehSR1IjV/txKN2HeSpSpWf7dJrVyvKfnla5boquCTXaq9LSQHlmFKTLrNM8Lq1Biim5BddLxcQACV+/90GZ7KpAccIv+0LfFX+hqd6iNBuQfPoiFp2igdd34HA97tcbkAyw+tBnLTfU6Y6tr8XlzWCizJt8V50BFNkpvHfXzLyL1j1GbHe768+nyfmqfaD79wc5sysKcukaD4Hp2CYOZNn7xfnBKHb3hAD5ODXiXeeSK90lePDEME7XhUVXyhv+SIJ3PJrsxI6AgQxqC6LXWEGMjGSrkwsGWfuIbiN9LsmchRM58ecNUJcr5rCnRDkWsY0xwipsItqdC8FVnLVZznXRZyVPCxlA8q6nqGIqE5chAQ28LcxAXULN9r8ePrzRgqZ/1XST8zLa6YpNW1WC827AVUvHUpEi1RzOgo2BUlacbiS8dMh+FlCWPf+x8UCGLml3oN3QaR0UdhK5Li5PtLiiMajyd/gHrHxOilaw1QFQoTESBUZtcP0IGi7qe4jJDrnhx/j1OOB4rMGWilsyXAIh0zKliwgTTHTXMLjgWv1wqIRgGPYn1P7lEWq49ifCee24bIhi0WravvGpnd1vXLzXMSVISs/HmM/v/6qYoLuGg/LjsfGG0mz6DR02rVs8+Q9Yh9i+X5KmsqvXtH0qAUhRoe1XvnprPqOhFioREqLWAULlAfckoYtRng9Y3mLxeyz61vXvfB+tKNkV5IIV+++1kXw8S0p/6I1wknx0HrVLg02dYbj5BEqU0fsyohkxTvSdobuyPOtudn8", "layer_level": 0}], "wiki_items": [{"catalog_id": "e572e11f-54cd-474e-bf00-685bf9a397f8", "content": "", "title": "System Overview", "description": "system-overview", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "bc2e26b9-9b2b-4cc6-a662-e816a7ad814a", "gmt_create": "2025-09-12T21:01:03.3741202+02:00", "gmt_modified": "2025-09-12T21:01:03.3850485+02:00"}, {"catalog_id": "2733cc7a-6f67-46e5-b229-fc0084ac3bcc", "content": "", "title": "Technology Stack", "description": "technology-stack", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "36bf0b7f-9011-4876-b226-8e27240d565d", "gmt_create": "2025-09-12T21:02:08.9067681+02:00", "gmt_modified": "2025-09-12T21:02:08.9161932+02:00"}, {"catalog_id": "2cd6167b-0ea6-4255-866c-f1cc532b2756", "content": "", "title": "Getting Started", "description": "getting-started", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "691cff3e-36af-4bbd-80d2-e54fa1220fea", "gmt_create": "2025-09-12T21:03:22.5591636+02:00", "gmt_modified": "2025-09-12T21:03:22.5674279+02:00"}, {"catalog_id": "ec581b00-b535-4a6c-a0d0-19bb0ce2fe2e", "content": "", "title": "Frontend Architecture", "description": "frontend-architecture", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "575050b2-5057-455e-bd06-e3649ce4c73f", "gmt_create": "2025-09-12T21:04:40.6052439+02:00", "gmt_modified": "2025-09-12T21:04:40.6126261+02:00"}, {"catalog_id": "5b381073-14b9-4b74-ba3c-f2ceb3abdd90", "content": "", "title": "Backend Architecture", "description": "backend-architecture", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "a70fa1cd-534a-4842-ae12-0239116148c1", "gmt_create": "2025-09-12T21:06:07.2830125+02:00", "gmt_modified": "2025-09-12T21:06:07.2934183+02:00"}, {"catalog_id": "e29a5c0f-d32f-4756-886a-0c99bdfe8bc1", "content": "", "title": "API Reference", "description": "api-reference", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "a13f0f9a-9f89-404e-99fe-9e636ef43211", "gmt_create": "2025-09-12T21:07:34.5180176+02:00", "gmt_modified": "2025-09-12T22:54:42.1538237+02:00"}, {"catalog_id": "58a090a5-c63c-4022-9857-99fe4f06094e", "content": "", "title": "Database Schema", "description": "database-schema", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "2328b43e-bf3c-4cc3-a44a-2ddf2efff91a", "gmt_create": "2025-09-12T21:09:21.0310947+02:00", "gmt_modified": "2025-09-12T21:09:21.0384134+02:00"}, {"catalog_id": "fb9e0ea4-1745-4556-ba9f-b3f0483e904b", "content": "", "title": "Authentication System", "description": "authentication-system", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "4703d4e9-043a-4d36-b927-909c6ad1642b", "gmt_create": "2025-09-12T21:10:51.4216151+02:00", "gmt_modified": "2025-09-12T21:10:51.4299206+02:00"}, {"catalog_id": "e369a098-8b76-4668-b68d-d974b8fae621", "content": "", "title": "Real-time Communication", "description": "real-time-communication", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "a1f884ad-c0a1-4559-bf2c-6e6eb294591e", "gmt_create": "2025-09-12T21:12:31.5718874+02:00", "gmt_modified": "2025-09-12T21:12:31.580354+02:00"}, {"catalog_id": "7e5065b0-32bd-4cb8-a5f5-554b801c8b87", "content": "", "title": "UI Components", "description": "ui-components", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "9e183489-7913-4f65-8344-fb43a4b087b3", "gmt_create": "2025-09-12T21:14:29.1551848+02:00", "gmt_modified": "2025-09-12T21:14:29.1654312+02:00"}, {"catalog_id": "f0053d46-b028-454e-b683-8c580f1ec5ee", "content": "", "title": "Testing Strategy", "description": "testing-strategy", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "aa439125-d9e4-4492-be4d-1d39b89ffc52", "gmt_create": "2025-09-12T21:16:04.551943+02:00", "gmt_modified": "2025-09-12T21:16:04.558582+02:00"}, {"catalog_id": "1578be00-7a30-46c8-97c5-8b683e5b8a89", "content": "", "title": "Deployment Architecture", "description": "deployment-architecture", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "1cdf0127-a5f9-4a9d-9c0f-16d8b8dd2660", "gmt_create": "2025-09-12T21:17:12.8912999+02:00", "gmt_modified": "2025-09-12T21:17:12.8981276+02:00"}, {"catalog_id": "4b4d2362-e3a3-4091-93de-3228c643841e", "content": "", "title": "Server Initialization", "description": "backend-server-initialization", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "3ad03cc1-6b8d-4c0d-9ad4-e804e42b7f83", "gmt_create": "2025-09-12T21:18:20.9401598+02:00", "gmt_modified": "2025-09-12T21:18:20.9444057+02:00"}, {"catalog_id": "bd49564f-d7a8-4e71-b915-1b85f3bc1ee2", "content": "", "title": "Authentication API", "description": "authentication-api", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "1faf7792-84ff-424a-b5d3-a4e914744936", "gmt_create": "2025-09-12T21:19:42.3562081+02:00", "gmt_modified": "2025-09-12T21:19:42.3640213+02:00"}, {"catalog_id": "57834b80-b729-4889-9691-ff6d48c0aad7", "content": "", "title": "User Model", "description": "user-model", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "295a436b-85e4-4878-8df4-ba179033fd9e", "gmt_create": "2025-09-12T21:20:57.6479312+02:00", "gmt_modified": "2025-09-12T21:20:57.654903+02:00"}, {"catalog_id": "b9e2cbe4-af00-4ca2-b126-1a42dda24098", "content": "", "title": "Chat Components", "description": "chat-components", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "fe9e8802-e8ae-4c2c-9aec-79026f93cf09", "gmt_create": "2025-09-12T21:22:25.2255002+02:00", "gmt_modified": "2025-09-12T21:22:25.2334396+02:00"}, {"catalog_id": "************************************", "content": "", "title": "Routing Architecture", "description": "backend-routing-architecture", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "cecdc212-b047-4a30-ad74-22c73e0cb95a", "gmt_create": "2025-09-12T21:23:32.024238+02:00", "gmt_modified": "2025-09-12T21:23:32.031746+02:00"}, {"catalog_id": "934ea81e-5efb-420c-9f8b-9aff2dd9ec2c", "content": "", "title": "Case Management API", "description": "case-management-api", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "c2dfcf3c-7f8b-4c04-bfe9-4b1a629cd1c9", "gmt_create": "2025-09-12T21:24:51.4505022+02:00", "gmt_modified": "2025-09-12T21:24:51.4590372+02:00"}, {"catalog_id": "0254afa8-3eda-438d-b049-a16e58e39e0c", "content": "", "title": "Case Model", "description": "case-model", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "0d94eee9-9b37-43d6-a3d4-c48ca4ddb652", "gmt_create": "2025-09-12T21:26:12.1848496+02:00", "gmt_modified": "2025-09-12T21:26:12.194073+02:00"}, {"catalog_id": "5a15574c-f3ee-4acd-8a76-59875c33ef2c", "content": "", "title": "Map Components", "description": "map-components", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "7b719390-3e9b-4ded-8b21-208c71ce5591", "gmt_create": "2025-09-12T21:27:44.6037674+02:00", "gmt_modified": "2025-09-12T21:27:44.6113832+02:00"}, {"catalog_id": "d12200c7-81fc-4058-be66-aafb1e37368f", "content": "", "title": "Service Layer", "description": "backend-service-layer", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "27e3b6c4-4e2c-436c-a217-4258374894d4", "gmt_create": "2025-09-12T21:29:25.7656377+02:00", "gmt_modified": "2025-09-12T21:29:25.7733094+02:00"}, {"catalog_id": "4354b674-35c9-459c-8acc-12075ff9ac8a", "content": "", "title": "Messaging API", "description": "messaging-api", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "888ebf03-df2c-41cd-a1bd-050d957f8112", "gmt_create": "2025-09-12T21:30:53.6869806+02:00", "gmt_modified": "2025-09-12T21:30:53.6921814+02:00"}, {"catalog_id": "66c84904-51bb-4d31-8717-b727c0c29102", "content": "", "title": "Message Model", "description": "message-model", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "4b7a8b2a-0b2e-4ad6-a44d-ac728030a90e", "gmt_create": "2025-09-12T21:32:03.2719537+02:00", "gmt_modified": "2025-09-12T21:32:03.2768305+02:00"}, {"catalog_id": "0280b906-f4d7-4414-b184-f4f31457c19d", "content": "", "title": "Form Components", "description": "form-components", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "674a957e-7838-4b1b-bb80-b3f4c85cf487", "gmt_create": "2025-09-12T21:33:32.7133965+02:00", "gmt_modified": "2025-09-12T21:33:32.7237234+02:00"}, {"catalog_id": "899d811e-ec37-411d-b243-ba02460ccafa", "content": "", "title": "Data Access Layer", "description": "backend-data-access", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "575971c6-d66b-47d5-bb3c-76efdacc9cbd", "gmt_create": "2025-09-12T21:34:47.6682149+02:00", "gmt_modified": "2025-09-12T21:34:47.6751751+02:00"}, {"catalog_id": "10ebeb68-9195-4c0c-aa72-11c1c35bfbc4", "content": "", "title": "Notification API", "description": "notification-api", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "24f24e5b-0019-4723-a135-0d7cc30b9870", "gmt_create": "2025-09-12T21:36:03.1968945+02:00", "gmt_modified": "2025-09-12T21:36:03.2033424+02:00"}, {"catalog_id": "e9d7f087-512b-4e51-83ce-fa0e941ac793", "content": "", "title": "Notification Model", "description": "notification-model", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "efe33477-db16-4324-a28a-1769974a6449", "gmt_create": "2025-09-12T21:37:26.1270779+02:00", "gmt_modified": "2025-09-12T21:37:26.1350576+02:00"}, {"catalog_id": "9456511b-20f6-46b5-8e3f-6fb32858e7c1", "content": "", "title": "Navigation Components", "description": "navigation-components", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "398d7161-49ec-4ede-a631-46d51bdbd334", "gmt_create": "2025-09-12T21:38:44.0990793+02:00", "gmt_modified": "2025-09-12T21:38:44.1060685+02:00"}, {"catalog_id": "289a5787-d796-46b0-b1e0-cdfaf4546a2c", "content": "", "title": "Authentication System", "description": "backend-authentication", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "26589136-2b6f-4b0f-bfab-2bb61d30947a", "gmt_create": "2025-09-12T21:40:08.4603623+02:00", "gmt_modified": "2025-09-12T21:40:08.4647271+02:00"}, {"catalog_id": "f53b604e-1402-41b9-adfb-9d1bccae31f0", "content": "", "title": "Geolocation API", "description": "geolocation-api", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "265c8f43-f35f-4b35-834a-7832f2d98869", "gmt_create": "2025-09-12T21:41:24.3596278+02:00", "gmt_modified": "2025-09-12T21:41:24.3664988+02:00"}, {"catalog_id": "4056c7dd-1432-4740-b10a-abca2c50d20f", "content": "", "title": "Rating Model", "description": "rating-model", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "bc1bd533-ea36-4791-865e-2754d33b9b95", "gmt_create": "2025-09-12T21:42:55.7591893+02:00", "gmt_modified": "2025-09-12T21:42:55.7668119+02:00"}, {"catalog_id": "35c4c38e-993d-4695-b114-d699ecca6c6a", "content": "", "title": "Utility Components", "description": "utility-components", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "72a50e04-7117-469a-bd9e-5e9feabdeed2", "gmt_create": "2025-09-12T21:44:35.9377809+02:00", "gmt_modified": "2025-09-12T21:44:35.9422088+02:00"}, {"catalog_id": "ecdb5bb8-f8cc-4357-a857-39c88c0fbfa6", "content": "", "title": "Real-time Communication", "description": "backend-realtime-communication", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "803d8863-bf92-4f33-919c-b9f4acd42cd3", "gmt_create": "2025-09-12T21:45:57.7121799+02:00", "gmt_modified": "2025-09-12T21:45:57.7192285+02:00"}, {"catalog_id": "1507f131-314e-48fa-8119-b3a3ca22ee60", "content": "", "title": "User Management API", "description": "user-management-api", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "cdccfdd8-8ccf-4a03-b82f-b094c2b4fe8e", "gmt_create": "2025-09-12T21:46:58.4658336+02:00", "gmt_modified": "2025-09-12T21:46:58.4707326+02:00"}, {"catalog_id": "3cde283d-52e0-4463-8c48-3a91eca21fcb", "content": "", "title": "Waypoints Model", "description": "waypoints-model", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "134ab981-2bca-4a2d-9ad3-30e62c470f28", "gmt_create": "2025-09-12T21:48:44.7288795+02:00", "gmt_modified": "2025-09-12T21:48:44.7331717+02:00"}, {"catalog_id": "d46f999c-b613-4f44-9c86-147eab21d8c4", "content": "", "title": "UI Primitives", "description": "ui-primitives", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "176108c1-db11-42f3-ba96-e070cbdf4a79", "gmt_create": "2025-09-12T21:50:25.1469687+02:00", "gmt_modified": "2025-09-12T21:50:25.1528374+02:00"}, {"catalog_id": "e5a9ef46-0aec-4192-aa57-8d797f5b3064", "content": "", "title": "Utilities and Helpers", "description": "backend-utilities", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "c5e69ca7-3884-4a19-8a57-fa21c96a6b00", "gmt_create": "2025-09-12T21:51:44.1734693+02:00", "gmt_modified": "2025-09-12T21:51:44.1811646+02:00"}, {"catalog_id": "33420246-d2f7-44c2-af0d-76a64deebc50", "content": "", "title": "Case Router", "description": "case-router", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "d0a06c7e-ff3c-4dda-a2c0-75b5bfe51611", "gmt_create": "2025-09-12T21:53:14.7028439+02:00", "gmt_modified": "2025-09-12T21:53:14.7104218+02:00"}, {"catalog_id": "ae7eba85-2b0e-47da-8ffd-2f140b1aba38", "content": "", "title": "Case Service", "description": "service-case", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "4784febc-a23b-4681-9bc7-c3411642671d", "gmt_create": "2025-09-12T21:54:32.3042351+02:00", "gmt_modified": "2025-09-12T21:54:32.3112039+02:00"}, {"catalog_id": "e68b91f2-1bf3-4b79-9679-ac5edd82492a", "content": "", "title": "User Router", "description": "user-router", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "bd454387-378c-40b8-829c-28d62f6fd8f3", "gmt_create": "2025-09-12T21:55:43.9667815+02:00", "gmt_modified": "2025-09-12T21:55:43.9757825+02:00"}, {"catalog_id": "133430b6-ec51-40bc-a3be-ff10342a70bb", "content": "", "title": "User Service", "description": "service-user", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "f943a285-6f98-4798-b209-760f02d4597e", "gmt_create": "2025-09-12T21:57:15.5407691+02:00", "gmt_modified": "2025-09-12T21:57:15.5478751+02:00"}, {"catalog_id": "d3deff2c-ba48-4ddf-9f97-4095ad908937", "content": "", "title": "Geo Router", "description": "geo-router", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "a1c1da46-3fe5-4555-aa81-8d1d91ba8c07", "gmt_create": "2025-09-12T21:58:33.0322008+02:00", "gmt_modified": "2025-09-12T21:58:33.0449551+02:00"}, {"catalog_id": "8e8a9334-2d76-44f2-8959-75c0a7ad7093", "content": "", "title": "Geo Service", "description": "service-geo", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "05af9740-5d06-4b1d-b102-cbc0622d4208", "gmt_create": "2025-09-12T21:59:57.5380608+02:00", "gmt_modified": "2025-09-12T21:59:57.5425238+02:00"}, {"catalog_id": "7b7b17a4-9320-4d8f-8d76-35524cbba17e", "content": "", "title": "Message Router", "description": "message-router", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "58297470-07ab-446a-9d30-0ac175374d15", "gmt_create": "2025-09-12T22:01:08.6532114+02:00", "gmt_modified": "2025-09-12T22:01:08.6602775+02:00"}, {"catalog_id": "e572c362-4efc-4929-9677-e0170e63dbde", "content": "", "title": "Message Service", "description": "service-message", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "fab4406d-6ced-4fb5-a3da-a8f09033e6f2", "gmt_create": "2025-09-12T22:02:41.1553373+02:00", "gmt_modified": "2025-09-12T22:02:41.1624262+02:00"}, {"catalog_id": "4c942802-408e-4b02-bccf-359f8f88f0e8", "content": "", "title": "Notification Router", "description": "notification-router", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "803b88bf-f4e5-4e3a-812d-1f3ff8702418", "gmt_create": "2025-09-12T22:04:06.1655668+02:00", "gmt_modified": "2025-09-12T22:04:06.1729019+02:00"}, {"catalog_id": "2c8293f1-86ca-4ff8-a17b-7e97c22d8c84", "content": "", "title": "Notification Service", "description": "service-notification", "extend": "{}", "progress_status": "completed", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "id": "6f2e3e0c-68de-4a0b-9307-aca7f28894b8", "gmt_create": "2025-09-12T22:05:19.1759566+02:00", "gmt_modified": "2025-09-12T22:05:19.1868152+02:00"}], "wiki_overview": {"content": "<blog>\n# TakeNPass Project Overview\n\n## 1. Project Introduction\n\nTakeNPass is a modern web platform for package crowdsourcing that connects people who need to deliver packages and documents with travelers willing to transport them. The platform provides a comprehensive solution for organizing small cargo deliveries through a user-friendly interface.\n\n### Purpose Statement\nThe primary purpose of TakeNPass is to create an efficient marketplace for package delivery by leveraging the travel plans of individuals who can transport items along their routes, providing an economical alternative to traditional delivery services.\n\n### Core Goals and Objectives\n- Connect senders with travelers who can transport packages\n- Facilitate real-time communication between parties\n- Provide interactive map visualization for routes\n- Implement a rating system for trust and quality assurance\n- Support multiple languages for international users\n\n### Target Audience\nThe platform serves two primary user groups:\n- **Senders**: Individuals or businesses needing to deliver packages or documents\n- **Carriers**: Travelers who can transport items along their planned routes\n\nAdditionally, users can have a universal role, acting as both senders and carriers.\n\n## 2. Technical Architecture\n\n### Component Breakdown\n\nThe project follows a monorepo structure with two main components:\n\n- **Frontend**: React-based client application with TypeScript\n- **Backend**: Node.js server application using Fastify framework\n\nThe architecture implements a type-safe approach from database to UI using tRPC, Prisma, and Zod for comprehensive type validation and generation.\n\n```mermaid\nflowchart TD\n    A[Client] --> B[tRPC API]\n    B --> C[Business Logic]\n    C --> D[Prisma ORM]\n    D --> E[(MySQL Database)]\n    F[WebSocket] --> G[Real-time Communication]\n    G --> A\n    C --> H[External Geoservices]\n```\n\n### Design Patterns\n\nThe application utilizes several modern design patterns:\n\n- **Type-Safe Architecture**: Full type safety from database to UI using tRPC and Prisma\n- **Component-Based UI**: React components organized by feature and utility\n- **State Management**: Zustand for local state and React Query for server state\n- **Real-time Communication**: WebSocket integration via Socket.IO for chat and notifications\n- **Internationalization**: i18next implementation for multi-language support\n\n### System Relationships\n\nThe frontend and backend are tightly integrated through tRPC, enabling type-safe API calls. The system uses Prisma as an ORM layer between the application logic and MySQL database, with generated Zod schemas ensuring data validation at all levels.\n\n### Data Flow\n\n```mermaid\nflowchart TD\n    A[User Interface] --> B[React Components]\n    B --> C[Tanstack Router]\n    C --> D[tRPC Client]\n    D --> E[Fastify Server]\n    E --> F[Service Layer]\n    F --> G[Prisma ORM]\n    G --> H[(MySQL Database)]\n    I[External Services] --> J[Geocoding APIs]\n    J --> F\n    K[WebSocket] --> L[Real-time Updates]\n    L --> A\n```\n\n## 3. Key Implementation\n\n### Main Entry Points\n- Frontend: `frontend/src/main.tsx` - Application initialization and routing setup\n- Backend: `server/src/server.ts` - Server initialization and configuration\n- Routing: `frontend/src/main.tsx` - Tanstack Router configuration\n\n### Core Modules\n- Authentication: `server/src/providers/auth.ts` and `better-auth` integration\n- State Management: `frontend/src/store.ts` using Zustand\n- API Client: `frontend/src/trpc.ts` for tRPC integration\n- WebSocket: `frontend/src/socket.ts` and `server/src/providers/ws.ts`\n\n### Configuration Approach\n- Environment Configuration: `dotenv` package for environment variables\n- Frontend Build: `vite.config.ts` with Vite optimization\n- Backend Build: `esbuild` configuration in package.json scripts\n- Type Safety: Comprehensive TypeScript configuration with `tsconfig.json` files in both frontend and server\n\n### External Dependencies\n- UI Framework: `@heroui/react` and `Tailwind CSS`\n- Database: `@prisma/client` with MySQL adapter\n- Real-time: `socket.io-client` and `socket.io`\n- Internationalization: `i18next` ecosystem\n- Validation: `zod` and `zod-prisma-types`\n\n### Integration Points\n- Geolocation Services: Integration with LocationIQ and Geoapify APIs\n- Authentication: `better-auth` for modern authentication flows\n- Testing: `vitest` for backend testing and `@playwright/test` for frontend\n- Deployment: ESBuild for production builds\n\n```mermaid\ngraph LR\n    A[Frontend] --> B[tRPC]\n    B --> C[Backend]\n    C --> D[Prisma]\n    D --> E[MySQL]\n    A --> F[Socket.IO]\n    C --> F\n    C --> G[Geoservices]\n    H[Auth] --> A\n    H --> C\n```\n\n## 4. Key Features\n\n### Functionality Overview\nTakeNPass offers a comprehensive set of features for package crowdsourcing:\n\n- Interactive map visualization of delivery routes\n- Real-time chat between senders and carriers\n- Comprehensive user rating and review system\n- Multi-language support (Russian, English, Spanish)\n- Responsive design for all device types\n- Advanced search and filtering capabilities\n\n### Implementation Highlights\n- **Interactive Maps**: Implementation using Leaflet and React-Leaflet with custom map components in `frontend/src/components/map/`\n- **Real-time Communication**: WebSocket integration for chat and notifications using Socket.IO\n- **Type Safety**: End-to-end type safety from database schema to UI components using Prisma, tRPC, and Zod\n- **Authentication**: Modern authentication flow using better-auth with JWT tokens\n- **Internationalization**: Complete i18next implementation with automated translation scanning\n\n```mermaid\nstateDiagram-v2\n    [*] --> Authentication\n    Authentication --> Home: Successful login\n    Home --> CreateDelivery: Create new delivery\n    Home --> SearchDeliveries: Search existing\n    SearchDeliveries --> DeliveryDetails: Select delivery\n    DeliveryDetails --> Chat: Communicate\n    Chat --> DeliveryProcess: Coordinate delivery\n    DeliveryProcess --> Rating: Complete delivery\n    Rating --> Home: Return to home\n```\n\nThe platform demonstrates a modern approach to web application development, combining type safety, real-time functionality, and user-centric design to create a robust solution for package crowdsourcing.\n</blog>", "gmt_create": "2025-09-12T20:53:55.3940661+02:00", "gmt_modified": "2025-09-12T20:53:55.3940661+02:00", "id": "81762ef2-7175-4e40-a052-0cb763a796ef", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": ""}, "wiki_readme": {"content": "No readme file", "gmt_create": "2025-09-12T20:52:48.7142473+02:00", "gmt_modified": "2025-09-12T20:52:48.7142473+02:00", "id": "c5be6c0b-3bfd-4900-a769-b625b22d3e6f", "repo_id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": ""}, "wiki_repo": {"id": "0978b983-ebac-4e2d-a8e4-7a43407eb09b", "workspace_path": "", "name": "takeNpass", "progress_status": "completed", "wiki_present_status": "COMPLETED", "optimized_catalog": "\".\\n├── frontend\\n│   ├── app\\n│   │   └── globals.css\\n│   ├── src\\n│   │   ├── components\\n│   │   │   ├── chat\\n│   │   │   │   ├── ChatHeader.tsx\\n│   │   │   │   ├── ChatInput.tsx\\n│   │   │   │   ├── ChatPartners.tsx\\n│   │   │   │   ├── MainChat.tsx\\n│   │   │   │   ├── MessageItem.tsx\\n│   │   │   │   ├── MessageSkeleton.tsx\\n│   │   │   │   └── PartnerSkeleton.tsx\\n│   │   │   ├── landing\\n│   │   │   │   ├── WorldMapWrapper.tsx\\n│   │   │   │   ├── index.tsx\\n│   │   │   │   └── landing.css\\n│   │   │   ├── magicui\\n│   │   │   │   └── flickering-grid.tsx\\n│   │   │   ├── map\\n│   │   │   │   ├── maps\\n│   │   │   │   ├── MainMap.tsx\\n│   │   │   │   ├── leaflet.css\\n│   │   │   │   └── map.css\\n│   │   │   ├── ui\\n│   │   │   │   ├── bento-grid.tsx\\n│   │   │   │   ├── cover.tsx\\n│   │   │   │   ├── glass-card.tsx\\n│   │   │   │   ├── sparkles.tsx\\n│   │   │   │   ├── text-generate-effect.tsx\\n│   │   │   │   └── world-map.tsx\\n│   │   │   ├── CaseCard.tsx\\n│   │   │   ├── CaseCardSkeleton.tsx\\n│   │   │   ├── DatePicker.tsx\\n│   │   │   ├── FavoriteButton.tsx\\n│   │   │   ├── FromPointDot.tsx\\n│   │   │   ├── HeroBlock.tsx\\n│   │   │   ├── LangSwitcher.tsx\\n│   │   │   ├── LockIcon.tsx\\n│   │   │   ├── Login.tsx\\n│   │   │   ├── LoginModal.tsx\\n│   │   │   ├── MessagesWidget.tsx\\n│   │   │   ├── Navbar.tsx\\n│   │   │   ├── Notifications.tsx\\n│   │   │   ├── PhoneInput.tsx\\n│   │   │   ├── ProfileContainer.tsx\\n│   │   │   ├── ProfileInfo.tsx\\n│   │   │   ├── ProfileSettings.tsx\\n│   │   │   ├── RatingForm.tsx\\n│   │   │   ├── Ratings.tsx\\n│   │   │   ├── RegForm.tsx\\n│   │   │   ├── SortSelect.tsx\\n│   │   │   ├── TimePicker.tsx\\n│   │   │   ├── Timeline.tsx\\n│   │   │   ├── ToPointDot.tsx\\n│   │   │   ├── Upload.tsx\\n│   │   │   ├── UserMenu.tsx\\n│   │   │   ├── UserRating.tsx\\n│   │   │   ├── ValidateUser.tsx\\n│   │   │   └── WebSocketStatus.tsx\\n│   │   ├── hooks\\n│   │   │   ├── useChat.ts\\n│   │   │   └── useMessageRead.ts\\n│   │   ├── lib\\n│   │   │   ├── svg\\n│   │   │   │   ├── CalendarIcon.tsx\\n│   │   │   │   ├── ConnectionIcon.tsx\\n│   │   │   │   ├── EmptyHeartIcon.tsx\\n│   │   │   │   ├── EmptyStarIcon.tsx\\n│   │   │   │   ├── ErrorIcon.tsx\\n│   │   │   │   ├── FillHeartIcon.tsx\\n│   │   │   │   ├── FillStarIcon.tsx\\n│   │   │   │   ├── HalfStarIcon.tsx\\n│   │   │   │   ├── IconLanguage.tsx\\n│   │   │   │   ├── IconPencil.tsx\\n│   │   │   │   ├── LightingIcon.tsx\\n│   │   │   │   ├── LocationIcon.tsx\\n│   │   │   │   ├── MailIcon.tsx\\n│   │   │   │   ├── MapMarkQuestion.tsx\\n│   │   │   │   ├── MenuIcon.tsx\\n│   │   │   │   ├── MessageIcon.tsx\\n│   │   │   │   ├── MoonIcon.tsx\\n│   │   │   │   ├── NotificationIcon.tsx\\n│   │   │   │   ├── PhoneIcon.tsx\\n│   │   │   │   ├── SearchIcon.tsx\\n│   │   │   │   ├── SendIcon.tsx\\n│   │   │   │   ├── SunIcon.tsx\\n│   │   │   │   ├── UserIcon.tsx\\n│   │   │   │   └── WifiErrorIcon.tsx\\n│   │   │   ├── utils\\n│   │   │   │   ├── calcDateDiff.ts\\n│   │   │   │   ├── calcDistance.ts\\n│   │   │   │   ├── debounce.ts\\n│   │   │   │   ├── getApiHeaders.ts\\n│   │   │   │   ├── getCountryAndLocation.ts\\n│   │   │   │   ├── getShortPointName.ts\\n│   │   │   │   ├── index.ts\\n│   │   │   │   ├── mapUtils.ts\\n│   │   │   │   ├── parseDate.ts\\n│   │   │   │   ├── pointColorByType.ts\\n│   │   │   │   ├── statusColor.ts\\n│   │   │   │   ├── statusList.ts\\n│   │   │   │   ├── useClickOutside.ts\\n│   │   │   │   └── waypointIcons.ts\\n│   │   │   ├── auth.ts\\n│   │   │   ├── eventBus.ts\\n│   │   │   └── utils.ts\\n│   │   ├── locales\\n│   │   │   ├── en.json\\n│   │   │   ├── en.ts\\n│   │   │   ├── es.json\\n│   │   │   ├── es.ts\\n│   │   │   ├── ru.json\\n│   │   │   └── ru.ts\\n│   │   ├── pages\\n│   │   │   ├── CasePage.tsx\\n│   │   │   ├── ChatPage.tsx\\n│   │   │   ├── CreateCasePage.tsx\\n│   │   │   ├── IndexPage.tsx\\n│   │   │   ├── IndexPage_old.tsx\\n│   │   │   ├── LoginPage.tsx\\n│   │   │   ├── MyCases.tsx\\n│   │   │   ├── ProfilePage.tsx\\n│   │   │   ├── RatingsPage.tsx\\n│   │   │   ├── RegPage.tsx\\n│   │   │   └── SettingsPage.tsx\\n│   │   ├── types\\n│   │   │   └── chat.ts\\n│   │   ├── App.css\\n│   │   ├── App.tsx\\n│   │   ├── i18n.ts\\n│   │   ├── index.css\\n│   │   ├── main.tsx\\n│   │   ├── map.css\\n│   │   ├── socket.ts\\n│   │   ├── store.ts\\n│   │   ├── trpc.ts\\n│   │   ├── types.ts\\n│   │   └── vite-env.d.ts\\n│   ├── README.md\\n│   ├── components.json\\n│   ├── hero.ts\\n│   ├── index.html\\n│   ├── package-lock.json\\n│   ├── package.json\\n│   ├── postcss.config.js\\n│   ├── tailwind.config.ts\\n│   ├── tsconfig.json\\n│   ├── tsconfig.node.json\\n│   └── vite.config.ts\\n├── server\\n│   ├── prisma\\n│   │   ├── generated\\\\zod\\n│   │   │   ├── inputTypeSchemas\\n│   │   │   │   └── ... 549 files, 0 dirs not shown\\n│   │   │   ├── modelSchema\\n│   │   │   │   └── ... 17 files, 0 dirs not shown\\n│   │   │   ├── outputTypeSchemas\\n│   │   │   │   └── ... 243 files, 0 dirs not shown\\n│   │   │   └── index.ts\\n│   │   └── seeds\\n│   │       ├── UserAuthLogSeed.ts\\n│   │       └── UserSeed.ts\\n│   ├── src\\n│   │   ├── providers\\n│   │   │   ├── auth.ts\\n│   │   │   ├── better-auth.ts\\n│   │   │   ├── prisma.ts\\n│   │   │   └── ws.ts\\n│   │   ├── router\\n│   │   │   ├── routers\\n│   │   │   │   └── ... 7 files, 0 dirs not shown\\n│   │   │   ├── context.ts\\n│   │   │   ├── createRouterHelper.ts\\n│   │   │   ├── index.ts\\n│   │   │   └── trpc.ts\\n│   │   ├── services\\n│   │   │   ├── case.ts\\n│   │   │   ├── depr_ws.ts\\n│   │   │   ├── geo.ts\\n│   │   │   ├── message.ts\\n│   │   │   ├── notification.ts\\n│   │   │   └── user.ts\\n│   │   ├── shemas\\n│   │   │   └── CaseShemas.ts\\n│   │   ├── types\\n│   │   │   ├── CaseWithWay.ts\\n│   │   │   ├── CasesType.ts\\n│   │   │   ├── CreateCasePayload.ts\\n│   │   │   ├── GeoMeta.ts\\n│   │   │   ├── GeoapifyAutocompete.ts\\n│   │   │   ├── GeoapifyGeocode.ts\\n│   │   │   ├── GeoapifyReverse.ts\\n│   │   │   ├── Locationiq.ts\\n│   │   │   ├── SocketNotificationType.ts\\n│   │   │   └── UpdateCasePayload.ts\\n│   │   ├── utils\\n│   │   │   ├── isValidEmail.ts\\n│   │   │   └── rateLimit.ts\\n│   │   ├── config.ts\\n│   │   ├── index.ts\\n│   │   └── server.ts\\n│   ├── test\\n│   │   ├── auth.test.ts\\n│   │   ├── caseService.test.js\\n│   │   └── geoService.test.js\\n│   ├── app.d.ts\\n│   ├── package-lock.json\\n│   ├── package.json\\n│   ├── playground.ts\\n│   └── tsconfig.json\\n├── PROJECT_DESCRIPTION.md\\n└── TECH_STACK.md\\n\"", "current_document_structure": "WikiEncrypted: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", "catalogue_think_content": "WikiEncrypted: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", "recovery_checkpoint": "wiki_generation_completed", "last_commit_id": "f3f815e441842cda55b1b0a6975693be99748b74", "last_commit_update": "2025-09-12T22:49:08+02:00", "gmt_create": "2025-09-12T20:52:48.0724983+02:00", "gmt_modified": "2025-09-12T22:54:42.2817558+02:00", "extend_info": "{\"language\":\"en\",\"active\":true,\"branch\":\"main\",\"shareStatus\":\"\",\"server_error_code\":\"\",\"cosy_version\":\"\"}"}}