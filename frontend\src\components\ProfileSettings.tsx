import { ComponentProps, FC, useEffect, useState } from 'react'
import { Card, Input, Button, Text, Avatar, Select, SelectItem, forwardRef, Textarea } from "@heroui/react"
import { <PERSON><PERSON><PERSON><PERSON>, CardBody, CardFooter } from "@heroui/react"
import { TRPCErrorMessage, User } from '@/types'
import { useUserStore } from '@/store'
import { Upload } from './Upload'
import { trpc } from '@/trpc'
import { PhoneInput } from './PhoneInput'
import { addToast } from '@heroui/react'
import { useTranslation } from 'react-i18next'

export const ProfileSettings: FC = () => {
  const { t } = useTranslation()

  const userStore = useUserStore()
  const user = userStore.data as User

  const [username, setUsername] = useState(user.username)
  const [email, setEmail] = useState(user.email)
  const [phone, setPhone] = useState(user.phone)
  const [avatar, setAvatar] = useState(user.avatar?.base64string)

  const [aboutMe, setAboutMe] = useState(user.about_me)

  const [errors, setErrors] = useState({
    phone: undefined,
    email: undefined,
    username: undefined,
    aboutMe: undefined
  })

  const { isError: updateAvatar_isError, isPending: updateAvatar_isLoading, mutate: updateAvatarQuery } = trpc.users.updateAvatar.useMutation()
  const { isError, isPending: isLoading, isSuccess, mutate, error, data: updateResult } = trpc.users.updateProfile.useMutation()

  const handleSave = () => {
    const payload = {
      ...user,
      username,
      email,
      phone,
      avatar,
      about_me: aboutMe
    }

    mutate(payload)
  }

  function onAvatarUpdate(base64string: string) {
    updateAvatarQuery({ base64string })
  }

  const AvatarComponent = () => (
    <Avatar
      classNames={{
        name: 'font-semibold'
      }}
      className='shadow-xl'
      color='default'
      showFallback
      name={String(username?.split(' ').slice(0, 10) || '')}
      size='lg'
      isBordered
      src={avatar}
    />
  )

  useEffect(() => {
    isSuccess && addToast({ color: 'success', title: t('Profile updated successfully') })
    isError && addToast({ color: 'danger', title: t('Error updating profile') + ' ' + String(error.data?.code || '') })

    if (isSuccess && updateResult?.id) {
      userStore.setData(updateResult)
    }
  }, [isSuccess, isError])

  useEffect(() => {
    if (error?.message) {
      try {
        const err: TRPCErrorMessage[] = JSON.parse(error?.message)

        Object.keys(errors).map((key) => {
          setErrors((state) => ({
            ...state,
            [key]: err?.filter((i) => i.path.includes(key))?.map((i) => i.message)
          }))
        })
      } catch (error) {}
    } else {
      Object.keys(errors).map((key) => {
        setErrors((state) => ({
          ...state,
          [key]: undefined
        }))
      })
    }
  }, [error])

  useEffect(() => {
    console.log('errors', errors)
  }, [errors])

  return (
    <>
      <Card shadow='lg'>
        <CardHeader>
          <Upload Activator={AvatarComponent} onChangeBase64={onAvatarUpdate} setValue={setAvatar} />
        </CardHeader>
        <CardBody className='flex flex-col gap-4'>
          <Input
            // validationState={errors.username?.length ? 'invalid' : 'valid'}
            formNoValidate={errors.username?.length}
            labelPlacement='inside'
            label={t('Username')}
            fullWidth
            variant='flat'
            size='md'
            value={username}
            onChange={(e) => setUsername(e.target.value)}
          />

          <Input
            // validationState={errors.email?.length ? 'invalid' : 'valid'}
            formNoValidate={errors.email?.length}
            errorMessage={errors.email?.join?.(', ')}
            labelPlacement='inside'
            label='Email'
            fullWidth
            variant='flat'
            size='md'
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <PhoneInput
            // validationState={errors.phone?.length ? 'invalid' : 'valid'}
            formNoValidate={errors.phone?.length}
            errorMessage={errors.phone?.join?.(', ')}
            onValueChange={(value) => setPhone(value)}
            value={phone}
          />

          <Textarea
            formNoValidate={errors.aboutMe?.length}
            errorMessage={errors.aboutMe?.join?.(', ')}
            value={aboutMe}
            onValueChange={setAboutMe}
            label={t('About me')}
            placeholder={t('Tell us about yourself if you want')}
            className='max-w-xs'
          />
        </CardBody>
        <CardFooter className='flex justify-end'>
          <Button isLoading={isLoading} className='font-semibold' size='lg' variant='flat' color='primary' onClick={handleSave}>
            {t('Save')}
          </Button>
        </CardFooter>
      </Card>
    </>
  )
}
