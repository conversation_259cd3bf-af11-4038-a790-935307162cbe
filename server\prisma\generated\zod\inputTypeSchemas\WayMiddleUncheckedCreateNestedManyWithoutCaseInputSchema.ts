import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayMiddleCreateWithoutCaseInputSchema } from './WayMiddleCreateWithoutCaseInputSchema';
import { WayMiddleUncheckedCreateWithoutCaseInputSchema } from './WayMiddleUncheckedCreateWithoutCaseInputSchema';
import { WayMiddleCreateOrConnectWithoutCaseInputSchema } from './WayMiddleCreateOrConnectWithoutCaseInputSchema';
import { WayMiddleCreateManyCaseInputEnvelopeSchema } from './WayMiddleCreateManyCaseInputEnvelopeSchema';
import { WayMiddleWhereUniqueInputSchema } from './WayMiddleWhereUniqueInputSchema';

export const WayMiddleUncheckedCreateNestedManyWithoutCaseInputSchema: z.ZodType<Prisma.WayMiddleUncheckedCreateNestedManyWithoutCaseInput> = z.object({
  create: z.union([ z.lazy(() => WayMiddleCreateWithoutCaseInputSchema),z.lazy(() => WayMiddleCreateWithoutCaseInputSchema).array(),z.lazy(() => WayMiddleUncheckedCreateWithoutCaseInputSchema),z.lazy(() => WayMiddleUncheckedCreateWithoutCaseInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => WayMiddleCreateOrConnectWithoutCaseInputSchema),z.lazy(() => WayMiddleCreateOrConnectWithoutCaseInputSchema).array() ]).optional(),
  createMany: z.lazy(() => WayMiddleCreateManyCaseInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => WayMiddleWhereUniqueInputSchema),z.lazy(() => WayMiddleWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export default WayMiddleUncheckedCreateNestedManyWithoutCaseInputSchema;
