# Authentication System

<cite>
**Referenced Files in This Document**   
- [better-auth.ts](file://server/src/providers/better-auth.ts)
- [auth.ts](file://server/src/providers/auth.ts)
- [server.ts](file://server/src/server.ts)
- [user.ts](file://server/src/services/user.ts)
- [context.ts](file://server/src/router/context.ts)
- [auth.test.ts](file://server/test/auth.test.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Authentication Provider Integration](#authentication-provider-integration)
3. [Session Management with JWT](#session-management-with-jwt)
4. [Password Hashing and Credential Storage](#password-hashing-and-credential-storage)
5. [Login, Registration, and Session Validation Flows](#login-registration-and-session-validation-flows)
6. [Role-Based Access Control](#role-based-access-control)
7. [Social Authentication and Multi-Factor Considerations](#social-authentication-and-multi-factor-considerations)
8. [Route Protection and User Context Extraction](#route-protection-and-user-context-extraction)
9. [Conclusion](#conclusion)

## Introduction
The takeNpass application implements a secure authentication system using the Better-Auth provider integrated with Fastify. This system handles user registration, login, session management, and role-based access control. The implementation leverages JWT-based sessions, secure password hashing, and proper credential storage practices to ensure robust security across the platform.

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [server.ts](file://server/src/server.ts#L185-L207)

## Authentication Provider Integration

The authentication system is built on the Better-Auth library, which provides a comprehensive solution for identity management. Better-Auth is integrated with the Fastify server through a custom auth handler that routes all authentication requests to the appropriate endpoints.

Better-Auth is configured with MySQL as the database provider via Prisma adapter, enabling persistent storage of user credentials and session data. The integration supports email and password authentication out of the box, with configuration options for email verification and session expiration policies.

The system exposes standard authentication routes including sign-in, sign-up, sign-out, session retrieval, and email verification. These routes are registered under the `/api/auth` namespace and processed through the centralized `authHandler` function.

```mermaid
flowchart TD
A["Client Request\n(e.g., /api/auth/sign-up)"] --> B{Fastify Server}
B --> C["authHandler"]
C --> D["Better-Auth Provider"]
D --> E["Prisma Adapter"]
E --> F[(MySQL Database)]
F --> E --> D --> C --> B --> G["Response to Client"]
```

**Diagram sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [server.ts](file://server/src/server.ts#L185-L207)

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [server.ts](file://server/src/server.ts#L158-L207)

## Session Management with JWT

Session management in takeNpass is implemented using JWT tokens with configurable expiration policies. The system creates sessions that expire after 7 days of inactivity, with automatic session refresh every 24 hours to maintain user login state.

Sessions are stored in the database through the Prisma adapter and include metadata such as active and idle expiration timestamps. The cookie cache is enabled for improved performance, allowing session data to be temporarily cached while maintaining security.

When a user authenticates successfully, a JWT token is generated and returned to the client. This token contains the session ID and user information, signed with a secret key configured in the environment variables. Subsequent requests include this token in the Authorization header or cookies for session validation.

```mermaid
sequenceDiagram
participant Client
participant Server
participant BetterAuth
participant Database
Client->>Server : POST /api/auth/sign-in
Server->>BetterAuth : Validate credentials
BetterAuth->>Database : Check user existence
Database-->>BetterAuth : Return user data
BetterAuth->>BetterAuth : Generate JWT session token
BetterAuth->>Database : Store session
BetterAuth-->>Server : Return session and user
Server-->>Client : Set session cookie
```

**Diagram sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [context.ts](file://server/src/router/context.ts#L1-L37)

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [context.ts](file://server/src/router/context.ts#L1-L37)

## Password Hashing and Credential Storage

The system implements secure password hashing using industry-standard cryptographic algorithms provided by the Better-Auth library. Passwords are never stored in plain text; instead, they are hashed using strong one-way hashing functions before being persisted to the database.

User credentials are stored in the MySQL database through the Prisma ORM layer, with the Better-Auth adapter handling the encryption and storage process. The Key model in the database contains the hashed password field, ensuring that even database administrators cannot retrieve original passwords.

The authentication flow validates passwords by hashing the provided input and comparing it with the stored hash, preventing any exposure of the actual password during the verification process. This approach protects user credentials even in the event of a database breach.

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [user.ts](file://server/src/services/user.ts#L328-L368)

## Login, Registration, and Session Validation Flows

The authentication system implements three primary flows: registration, login, and session validation.

During registration, users provide their email, username, and password. The system creates a new user record through the Better-Auth API, which handles password hashing and storage. The same user ID from Better-Auth is used in the application's main User table to maintain consistency.

The login flow accepts email and password credentials, validates them against the stored hashes, and creates a new session upon successful authentication. Failed login attempts are logged and rejected with generic error messages to prevent information leakage.

Session validation occurs on every authenticated request through the TRPC context creation process. The system extracts session information from request headers and verifies its validity against the database-stored session data.

```mermaid
flowchart TD
subgraph Registration
A[User submits email, username, password] --> B[Hash password]
B --> C[Store in database via Better-Auth]
C --> D[Create application user record]
D --> E[Return success]
end
subgraph Login
F[User submits credentials] --> G[Validate against hash]
G --> H{Valid?}
H --> |Yes| I[Create JWT session]
H --> |No| J[Return error]
I --> K[Store session in DB]
K --> L[Return session to client]
end
subgraph SessionValidation
M[Incoming request] --> N[Extract session token]
N --> O[Verify with Better-Auth API]
O --> P{Valid session?}
P --> |Yes| Q[Attach user to context]
P --> |No| R[Reject request]
end
```

**Diagram sources**
- [user.ts](file://server/src/services/user.ts#L328-L368)
- [context.ts](file://server/src/router/context.ts#L1-L37)
- [server.ts](file://server/src/server.ts#L185-L207)

**Section sources**
- [user.ts](file://server/src/services/user.ts#L328-L368)
- [context.ts](file://server/src/router/context.ts#L1-L37)
- [auth.test.ts](file://server/test/auth.test.ts#L0-L81)

## Role-Based Access Control

Role-based access control is implemented through the user context available in the TRPC router. The system defines user roles (including ADMIN) that can be checked during route execution to determine access permissions.

The middleware extracts the authenticated user from the session and makes it available throughout the request lifecycle. Route handlers can then inspect the user's role property to enforce authorization policies. This approach allows fine-grained control over which users can access specific endpoints or perform certain actions.

Administrative privileges are granted based on the user's role field, with the ADMIN role having elevated permissions across the system. The role information is included in the session payload and validated on each request to prevent privilege escalation.

**Section sources**
- [context.ts](file://server/src/router/context.ts#L1-L37)
- [trpc.ts](file://server/src/router/trpc.ts#L36-L36)

## Social Authentication and Multi-Factor Considerations

While the current implementation focuses on email and password authentication, the Better-Auth provider supports social authentication through various identity providers. The architecture is designed to accommodate OAuth2 and OpenID Connect integrations for platforms like Google, Facebook, or GitHub.

Multi-factor authentication capabilities are available through the Better-Auth library, including support for WebAuthn and TOTP-based second factors. These features can be enabled by configuring additional authentication methods in the provider setup.

The system's modular design allows for incremental addition of social login providers and multi-factor authentication without disrupting existing functionality. Configuration would involve registering new authentication strategies and updating the user interface to present additional login options.

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)

## Route Protection and User Context Extraction

Route protection is implemented through the TRPC middleware system, which uses the authentication context to secure endpoints. Protected routes automatically validate the incoming session and attach the authenticated user to the request context.

User information is extracted from the session during context creation and made available to all route handlers. This includes the user ID, email, and role, which can be used for personalization, authorization, and audit logging.

The context creation function handles error cases gracefully, logging authentication failures while returning appropriate HTTP status codes to the client. This ensures that unauthorized access attempts are properly blocked while maintaining system stability.

```mermaid
sequenceDiagram
participant Client
participant Fastify
participant TRPC
participant Context
participant Handler
Client->>Fastify : Request to protected route
Fastify->>TRPC : Process request
TRPC->>Context : createContext()
Context->>BetterAuth : getSession(headers)
BetterAuth-->>Context : Session data
Context-->>TRPC : Return context with user
TRPC->>Handler : Execute route handler
Handler-->>TRPC : Return response
TRPC-->>Fastify : Send response
Fastify-->>Client : Return result or 401
```

**Diagram sources**
- [context.ts](file://server/src/router/context.ts#L1-L37)
- [trpc.ts](file://server/src/router/trpc.ts#L36-L36)

**Section sources**
- [context.ts](file://server/src/router/context.ts#L1-L37)

## Conclusion

The takeNpass authentication system provides a secure, scalable foundation for user identity management. By leveraging the Better-Auth provider with Fastify integration, the system delivers robust features including JWT-based session management, secure password hashing, and role-based access control. The architecture supports future expansion with social login and multi-factor authentication capabilities, while the current implementation ensures user data protection and proper access restrictions across the application.