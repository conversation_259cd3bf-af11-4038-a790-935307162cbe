# Messaging API

<cite>
**Referenced Files in This Document**   
- [message.ts](file://server/src/router/routers/message.ts)
- [message.ts](file://server/src/services/message.ts)
- [useChat.ts](file://frontend/src/hooks/useChat.ts)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx)
- [chat.ts](file://frontend/src/types/chat.ts)
- [ws.ts](file://server/src/providers/ws.ts)
- [socket.ts](file://frontend/src/socket.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Messaging Endpoints](#core-messaging-endpoints)
3. [Zod Validation Schemas](#zod-validation-schemas)
4. [tRPC Procedures](#trpc-procedures)
5. [Real-Time Messaging Flow](#real-time-messaging-flow)
6. [Integration with Chat UI Components](#integration-with-chat-ui-components)
7. [Client-Side Invocation Examples](#client-side-invocation-examples)
8. [Message Persistence and Delivery Guarantees](#message-persistence-and-delivery-guarantees)
9. [Performance Considerations](#performance-considerations)
10. [Error Handling and Codes](#error-handling-and-codes)

## Introduction
The Messaging API in takeNpass enables secure, real-time communication between users through a combination of tRPC procedures and WebSocket notifications. It supports sending and receiving messages, retrieving message history, managing read statuses, and listing chat partners. The system integrates tightly with the frontend chat components to provide a responsive user experience with optimistic updates and real-time synchronization.

**Section sources**
- [message.ts](file://server/src/router/routers/message.ts#L1-L70)
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L142)

## Core Messaging Endpoints
The messaging system exposes several key endpoints for managing conversations:
- `send`: Sends a message to another user
- `messagesByPartner`: Retrieves message history with a specific user
- `setAsRead`: Marks a message as read
- `partners`: Lists all chat partners
- `chats`: Retrieves all conversations with metadata
- `unreadCount`: Gets the total number of unread messages

These endpoints are secured via authentication middleware and operate on a one-to-one messaging model.

**Section sources**
- [message.ts](file://server/src/router/routers/message.ts#L1-L70)
- [message.ts](file://server/src/services/message.ts#L1-L263)

## Zod Validation Schemas
The API uses Zod for runtime type validation of input data:

```mermaid
classDiagram
class SendInputSchema {
+string receiverId
+string content (minLength : 1)
}
class SetAsReadInput {
+number messageId
}
SendInputSchema <|-- tRPC : "input validation"
SetAsReadInput <|-- tRPC : "input validation"
```

**Diagram sources**
- [message.ts](file://server/src/router/routers/message.ts#L5-L10)

### Message Content Validation
- `content`: Must be a non-empty string (minimum 1 character)
- Maximum length enforced at 254 characters via database constraint and frontend input

### Recipient Validation
- `receiverId`: Must be a valid string representing a user ID
- Validated against existing users in the database during message processing

### Thread Management
Thread context is managed implicitly through the `senderId`/`receiverId` pair in the message queries, with conversation threads retrieved via `messagesByPartner`.

**Section sources**
- [message.ts](file://server/src/router/routers/message.ts#L5-L10)

## tRPC Procedures
The messaging API is implemented using tRPC with authenticated procedures:

```mermaid
sequenceDiagram
participant Client
participant tRPC
participant Service
participant DB
Client->>tRPC : send({receiverId, content})
tRPC->>Service : MessageService.send()
Service->>DB : Create message
DB-->>Service : New message ID
Service->>Notification : Trigger notification
Service-->>tRPC : Success
tRPC-->>Client : Response
Client->>tRPC : messagesByPartner(partnerId)
tRPC->>Service : MessageService.messagesByPartner()
Service->>DB : Find messages
DB-->>Service : Message array
Service-->>tRPC : Messages
tRPC-->>Client : Message history
```

**Diagram sources**
- [message.ts](file://server/src/router/routers/message.ts#L12-L70)
- [message.ts](file://server/src/services/message.ts#L25-L263)

### sendMessage
- **Procedure**: `message.send`
- **Input**: `SendInputShema` (receiverId, content)
- **Authentication**: Required via `authedProcedure`
- **Response**: Message ID on success

### getMessages
- **Procedure**: `message.messagesByPartner`
- **Input**: Partner user ID (string)
- **Authentication**: Required
- **Response**: Array of messages ordered by creation time (ascending)

### markAsRead
- **Procedure**: `message.setAsRead`
- **Input**: `{ messageId: number }`
- **Authentication**: Required
- **Validation**: Ensures message belongs to the authenticated user as receiver

**Section sources**
- [message.ts](file://server/src/router/routers/message.ts#L12-L70)

## Real-Time Messaging Flow
The system combines tRPC requests with WebSocket push notifications for real-time updates:

```mermaid
flowchart TD
A[tRPC sendMessage] --> B[Create message in DB]
B --> C[Trigger notificationAfterSend]
C --> D[Fetch sender/receiver data]
D --> E[Create notification record]
E --> F[WebSocket push via IO.sendMessageToUser]
F --> G[Client receives xnotification]
G --> H[useBus listens for NEW_MESSAGE]
H --> I[Refetch messagesByPartner]
I --> J[UI updates with new message]
K[User opens chat] --> L[WebSocket connection established]
L --> M[Store socket in IO.clients Map]
M --> N[Send notifications to connected users only]
```

**Diagram sources**
- [message.ts](file://server/src/services/message.ts#L45-L60)
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L38-L42)
- [socket.ts](file://frontend/src/socket.ts#L1-L10)

When a message is sent:
1. The tRPC mutation creates the message in the database
2. The `notificationAfterSend` method triggers a notification
3. The WebSocket provider (`IO.sendMessageToUser`) pushes the event to the recipient
4. The client listens for `NEW_MESSAGE` events via `useBus`
5. The chat interface automatically refetches the latest messages

**Section sources**
- [message.ts](file://server/src/services/message.ts#L45-L60)
- [ws.ts](file://server/src/providers/ws.ts#L1-L50)

## Integration with Chat UI Components
The messaging API integrates with several frontend components to provide a complete chat experience:

```mermaid
classDiagram
class ChatInput {
+string value
+function onChange
+function onSend
+boolean isLoading
+boolean isDisabled
}
class useChat {
+ChatState chatState
+string inputValue
+function sendMessage
+function refetch
+boolean isSending
+boolean isLoading
}
class MessageItem {
+ChatMessage message
+boolean isOwn
+string sessionUserId
}
useChat --> ChatInput : "provides props"
useChat --> MessageItem : "provides messages"
useChat --> MainChat : "composes chat functionality"
ChatInput --> useChat : "triggers sendMessage"
```

**Diagram sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L142)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx#L1-L90)
- [chat.ts](file://frontend/src/types/chat.ts#L1-L83)

Key integration points:
- **Message Status Indicators**: The `isSending` state from `useChat` drives loading indicators in `ChatInput`
- **Read Receipts**: Messages include a `read` field that determines visual indicators in `MessageItem`
- **Auto-Scrolling**: The `chatContainerRef` enables automatic scrolling to the latest message
- **Optimistic Updates**: While not fully implemented, the system is designed to support optimistic message sending

**Section sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L142)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx#L1-L90)

## Client-Side Invocation Examples
The API is consumed through the `useChat` hook and `ChatInput` component:

```mermaid
sequenceDiagram
participant UI
participant ChatInput
participant useChat
participant tRPC
UI->>ChatInput : Render component
ChatInput->>useChat : Receive props (inputValue, sendMessage, etc.)
useChat->>tRPC : Initialize queries/mutations
ChatInput->>useChat : User types message
useChat->>useChat : Update inputValue state
ChatInput->>useChat : User presses Enter/Send
useChat->>tRPC : sendMessageMutation.mutateAsync()
tRPC->>Server : POST /trpc/message.send
Server-->>tRPC : Response
tRPC-->>useChat : onSuccess/onError
useChat->>UI : Show toast, refetch messages
```

**Diagram sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L50-L85)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx#L50-L85)

### useChat Hook Usage
The `useChat` hook provides a comprehensive interface for chat functionality:
- Manages message state and loading statuses
- Handles sending messages with error handling
- Automatically refetches messages when new ones arrive
- Provides auto-scrolling behavior
- Exposes a clean API for the UI components

### ChatInput Component
The `ChatInput` component:
- Enforces message length limits (254 characters)
- Supports Enter to send, Shift+Enter for new lines
- Disables sending when empty or during loading
- Auto-focuses when appropriate

**Section sources**
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L1-L142)
- [ChatInput.tsx](file://frontend/src/components/chat/ChatInput.tsx#L1-L90)

## Message Persistence and Delivery Guarantees
The system ensures reliable message delivery through several mechanisms:

```mermaid
flowchart TD
A[Client sends message] --> B[tRPC mutation begins]
B --> C[Database transaction]
C --> D{Success?}
D --> |Yes| E[Return success to client]
D --> |No| F[Return error]
E --> G[Push notification to recipient]
G --> H{Recipient online?}
H --> |Yes| I[Real-time update]
H --> |No| J[Notification stored]
J --> K[Delivered on next connection]
L[Message stored in DB] --> M[Foreign keys: senderId, receiverId]
M --> N[Indexes on senderId, receiverId, createdAt]
N --> O[Fast retrieval by partner]
```

**Diagram sources**
- [message.ts](file://server/src/services/message.ts#L25-L40)
- [message.ts](file://server/src/services/message.ts#L75-L90)

- **Persistence**: All messages are stored in the database with sender/receiver references
- **Atomic Operations**: Message creation is a single database operation
- **Delivery**: Messages are guaranteed to be delivered when the recipient comes online
- **Read Status**: Read/unread state is tracked per message and updated atomically

**Section sources**
- [message.ts](file://server/src/services/message.ts#L25-L263)

## Performance Considerations
The messaging system is optimized for performance with large message histories:

```mermaid
flowchart TD
A[Initial load] --> B[Fetch messagesByPartner]
B --> C[OrderBy createdAt ASC]
C --> D[Limited by database query]
D --> E[No pagination yet]
F[Unread count] --> G[Separate count queries]
G --> H[Avoids _count in main query]
H --> I[Better performance]
J[Chats list] --> K[Parallel queries for unread counts]
K --> L[Promise.all() optimization]
L --> M[Reduced latency]
```

**Diagram sources**
- [message.ts](file://server/src/services/message.ts#L150-L220)

- **Query Optimization**: Messages are ordered by creation time for chronological display
- **Unread Counting**: Uses separate count queries to avoid performance issues with `_count`
- **Caching**: Frontend uses tRPC's built-in caching with 30-second stale time
- **Future Improvements**: Pagination support is planned (currently loads all messages)

**Section sources**
- [message.ts](file://server/src/services/message.ts#L150-L220)
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L25-L30)

## Error Handling and Codes
The API handles various error conditions with appropriate responses:

```mermaid
flowchart TD
A[Client request] --> B{Valid input?}
B --> |No| C[Return validation error]
B --> |Yes| D{Authenticated?}
D --> |No| E[Return auth error]
D --> |Yes| F{Valid recipient?}
F --> |No| G[Return invalid recipient error]
F --> |Yes| H[Process message]
H --> I{Success?}
I --> |Yes| J[Return success]
I --> |No| K[Return server error]
style C fill:#f9f,stroke:#333
style E fill:#f9f,stroke:#333
style G fill:#f9f,stroke:#333
style K fill:#f9f,stroke:#333
```

**Diagram sources**
- [message.ts](file://server/src/router/routers/message.ts#L1-L70)
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L60-L75)

### Error Codes
- **Validation Errors**: Triggered by invalid input (empty content, invalid receiverId)
- **Authentication Errors**: Occur when session is invalid or missing
- **Recipient Errors**: When attempting to message a non-existent user
- **Content Violations**: Enforced by Zod schema (empty content)
- **Blocked Users**: Not currently implemented but would be handled at the service layer

The frontend displays user-friendly toast notifications for all error conditions using the `addToast` utility.

**Section sources**
- [message.ts](file://server/src/router/routers/message.ts#L5-L10)
- [useChat.ts](file://frontend/src/hooks/useChat.ts#L60-L75)