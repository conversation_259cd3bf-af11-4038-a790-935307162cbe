import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserCreateWithoutUserAuthenticationLogInputSchema } from './UserCreateWithoutUserAuthenticationLogInputSchema';
import { UserUncheckedCreateWithoutUserAuthenticationLogInputSchema } from './UserUncheckedCreateWithoutUserAuthenticationLogInputSchema';
import { UserCreateOrConnectWithoutUserAuthenticationLogInputSchema } from './UserCreateOrConnectWithoutUserAuthenticationLogInputSchema';
import { UserUpsertWithoutUserAuthenticationLogInputSchema } from './UserUpsertWithoutUserAuthenticationLogInputSchema';
import { UserWhereUniqueInputSchema } from './UserWhereUniqueInputSchema';
import { UserUpdateToOneWithWhereWithoutUserAuthenticationLogInputSchema } from './UserUpdateToOneWithWhereWithoutUserAuthenticationLogInputSchema';
import { UserUpdateWithoutUserAuthenticationLogInputSchema } from './UserUpdateWithoutUserAuthenticationLogInputSchema';
import { UserUncheckedUpdateWithoutUserAuthenticationLogInputSchema } from './UserUncheckedUpdateWithoutUserAuthenticationLogInputSchema';

export const UserUpdateOneRequiredWithoutUserAuthenticationLogNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutUserAuthenticationLogNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutUserAuthenticationLogInputSchema),z.lazy(() => UserUncheckedCreateWithoutUserAuthenticationLogInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutUserAuthenticationLogInputSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutUserAuthenticationLogInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => UserUpdateToOneWithWhereWithoutUserAuthenticationLogInputSchema),z.lazy(() => UserUpdateWithoutUserAuthenticationLogInputSchema),z.lazy(() => UserUncheckedUpdateWithoutUserAuthenticationLogInputSchema) ]).optional(),
}).strict();

export default UserUpdateOneRequiredWithoutUserAuthenticationLogNestedInputSchema;
