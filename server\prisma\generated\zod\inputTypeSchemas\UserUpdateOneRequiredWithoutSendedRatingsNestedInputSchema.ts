import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserCreateWithoutSendedRatingsInputSchema } from './UserCreateWithoutSendedRatingsInputSchema';
import { UserUncheckedCreateWithoutSendedRatingsInputSchema } from './UserUncheckedCreateWithoutSendedRatingsInputSchema';
import { UserCreateOrConnectWithoutSendedRatingsInputSchema } from './UserCreateOrConnectWithoutSendedRatingsInputSchema';
import { UserUpsertWithoutSendedRatingsInputSchema } from './UserUpsertWithoutSendedRatingsInputSchema';
import { UserWhereUniqueInputSchema } from './UserWhereUniqueInputSchema';
import { UserUpdateToOneWithWhereWithoutSendedRatingsInputSchema } from './UserUpdateToOneWithWhereWithoutSendedRatingsInputSchema';
import { UserUpdateWithoutSendedRatingsInputSchema } from './UserUpdateWithoutSendedRatingsInputSchema';
import { UserUncheckedUpdateWithoutSendedRatingsInputSchema } from './UserUncheckedUpdateWithoutSendedRatingsInputSchema';

export const UserUpdateOneRequiredWithoutSendedRatingsNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutSendedRatingsNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutSendedRatingsInputSchema),z.lazy(() => UserUncheckedCreateWithoutSendedRatingsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutSendedRatingsInputSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutSendedRatingsInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => UserUpdateToOneWithWhereWithoutSendedRatingsInputSchema),z.lazy(() => UserUpdateWithoutSendedRatingsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutSendedRatingsInputSchema) ]).optional(),
}).strict();

export default UserUpdateOneRequiredWithoutSendedRatingsNestedInputSchema;
