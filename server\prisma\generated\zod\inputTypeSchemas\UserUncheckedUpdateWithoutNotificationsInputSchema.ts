import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { StringFieldUpdateOperationsInputSchema } from './StringFieldUpdateOperationsInputSchema';
import { NullableStringFieldUpdateOperationsInputSchema } from './NullableStringFieldUpdateOperationsInputSchema';
import { BoolFieldUpdateOperationsInputSchema } from './BoolFieldUpdateOperationsInputSchema';
import { DateTimeFieldUpdateOperationsInputSchema } from './DateTimeFieldUpdateOperationsInputSchema';
import { RolesSchema } from './RolesSchema';
import { EnumRolesFieldUpdateOperationsInputSchema } from './EnumRolesFieldUpdateOperationsInputSchema';
import { CaseUncheckedUpdateManyWithoutAuthorNestedInputSchema } from './CaseUncheckedUpdateManyWithoutAuthorNestedInputSchema';
import { FavoriteUncheckedUpdateManyWithoutUserNestedInputSchema } from './FavoriteUncheckedUpdateManyWithoutUserNestedInputSchema';
import { AccountUncheckedUpdateManyWithoutUserNestedInputSchema } from './AccountUncheckedUpdateManyWithoutUserNestedInputSchema';
import { MessageUncheckedUpdateManyWithoutReceiverNestedInputSchema } from './MessageUncheckedUpdateManyWithoutReceiverNestedInputSchema';
import { MessageUncheckedUpdateManyWithoutSenderNestedInputSchema } from './MessageUncheckedUpdateManyWithoutSenderNestedInputSchema';
import { NotificationUncheckedUpdateManyWithoutSenderNestedInputSchema } from './NotificationUncheckedUpdateManyWithoutSenderNestedInputSchema';
import { NotificationSettingsUncheckedUpdateManyWithoutUserNestedInputSchema } from './NotificationSettingsUncheckedUpdateManyWithoutUserNestedInputSchema';
import { SessionUncheckedUpdateManyWithoutUserNestedInputSchema } from './SessionUncheckedUpdateManyWithoutUserNestedInputSchema';
import { UserAuthenticationLogUncheckedUpdateManyWithoutUserNestedInputSchema } from './UserAuthenticationLogUncheckedUpdateManyWithoutUserNestedInputSchema';
import { UserAvatarUncheckedUpdateOneWithoutUserNestedInputSchema } from './UserAvatarUncheckedUpdateOneWithoutUserNestedInputSchema';
import { UserRatingUncheckedUpdateManyWithoutSenderNestedInputSchema } from './UserRatingUncheckedUpdateManyWithoutSenderNestedInputSchema';
import { UserRatingUncheckedUpdateManyWithoutUserNestedInputSchema } from './UserRatingUncheckedUpdateManyWithoutUserNestedInputSchema';
import { CaseUncheckedUpdateManyWithoutClientsNestedInputSchema } from './CaseUncheckedUpdateManyWithoutClientsNestedInputSchema';

export const UserUncheckedUpdateWithoutNotificationsInputSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutNotificationsInput> = z.object({
  id: z.union([ z.string().uuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  username: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  email: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  emailVerified: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  image: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  name: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  about_me: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  phone: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  blocked: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  confirmed: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  role: z.union([ z.lazy(() => RolesSchema),z.lazy(() => EnumRolesFieldUpdateOperationsInputSchema) ]).optional(),
  language: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  authoredCases: z.lazy(() => CaseUncheckedUpdateManyWithoutAuthorNestedInputSchema).optional(),
  favorites: z.lazy(() => FavoriteUncheckedUpdateManyWithoutUserNestedInputSchema).optional(),
  accounts: z.lazy(() => AccountUncheckedUpdateManyWithoutUserNestedInputSchema).optional(),
  receivedMessages: z.lazy(() => MessageUncheckedUpdateManyWithoutReceiverNestedInputSchema).optional(),
  sentMessages: z.lazy(() => MessageUncheckedUpdateManyWithoutSenderNestedInputSchema).optional(),
  sendedNotifications: z.lazy(() => NotificationUncheckedUpdateManyWithoutSenderNestedInputSchema).optional(),
  notificationSettings: z.lazy(() => NotificationSettingsUncheckedUpdateManyWithoutUserNestedInputSchema).optional(),
  auth_session: z.lazy(() => SessionUncheckedUpdateManyWithoutUserNestedInputSchema).optional(),
  userAuthenticationLog: z.lazy(() => UserAuthenticationLogUncheckedUpdateManyWithoutUserNestedInputSchema).optional(),
  avatar: z.lazy(() => UserAvatarUncheckedUpdateOneWithoutUserNestedInputSchema).optional(),
  sendedRatings: z.lazy(() => UserRatingUncheckedUpdateManyWithoutSenderNestedInputSchema).optional(),
  ratings: z.lazy(() => UserRatingUncheckedUpdateManyWithoutUserNestedInputSchema).optional(),
  cases: z.lazy(() => CaseUncheckedUpdateManyWithoutClientsNestedInputSchema).optional()
}).strict();

export default UserUncheckedUpdateWithoutNotificationsInputSchema;
