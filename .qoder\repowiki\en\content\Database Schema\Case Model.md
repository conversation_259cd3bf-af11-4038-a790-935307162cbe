# Case Model

<cite>
**Referenced Files in This Document**   
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts)
- [WayToSchema.ts](file://server/prisma/generated/zod/modelSchema/WayToSchema.ts)
- [WayMiddleSchema.ts](file://server/prisma/generated/zod/modelSchema/WayMiddleSchema.ts)
- [FavoriteSchema.ts](file://server/prisma/generated/zod/modelSchema/FavoriteSchema.ts)
- [case.ts](file://server/src/services/case.ts)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts)
- [UpdateCasePayload.ts](file://server/src/types/UpdateCasePayload.ts)
- [CaseStatusSchema.ts](file://server/prisma/generated/zod/inputTypeSchemas/CaseStatusSchema.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Core Fields](#core-fields)
3. [Status Model and Transitions](#status-model-and-transitions)
4. [Pricing and Validation Rules](#pricing-and-validation-rules)
5. [User Relationships](#user-relationships)
6. [Spatial Data Modeling and Routing](#spatial-data-modeling-and-routing)
7. [Related Entities](#related-entities)
8. [Indexing and Search Optimization](#indexing-and-search-optimization)
9. [Sample Queries](#sample-queries)
10. [Conclusion](#conclusion)

## Introduction
The Case entity in the takeNpass system represents a package delivery request, serving as the central data model for coordinating shipments between users. It encapsulates delivery metadata, spatial routing information, pricing, and lifecycle status. The model is designed to support multi-point routing, user interactions, and real-time discovery based on geographic proximity. This document details the complete data model, relationships, business rules, and query patterns for the Case entity.

**Section sources**
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L17-L59)

## Core Fields
The Case entity contains essential delivery request attributes that define its identity, content, and temporal properties.

| Field | Type | Description |
|-------|------|-------------|
| id | string (UUID) | Unique identifier for the case |
| createdAt | date | Timestamp when the case was created |
| description | string (nullable) | Detailed description of the delivery request |
| baggage | string (nullable) | Comma-separated list of baggage types or specifications |
| price | number | Monetary value of the delivery service |
| isRequest | boolean | Flag indicating if this is a service request (true) or offer (false) |
| expire_at | date (nullable) | Expiration timestamp after which the case is no longer valid |

The baggage field is stored as a comma-separated string in the database but is transformed into a string array during API responses for easier client-side processing.

**Section sources**
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L17-L59)
- [case.ts](file://server/src/services/case.ts#L607-L634)

## Status Model and Transitions
The Case entity implements a finite state machine through its status field, which governs the lifecycle of delivery requests.

### Status Enumeration
The valid status values are defined as:
- OPEN: Case is active and discoverable
- CLOSED: Case is no longer accepting new interactions
- ARCHIVED: Case has been moved to long-term storage
- DONE: Delivery has been successfully completed
- CANCELED: Case has been terminated before completion
- TEST: Case is used for testing purposes

### Transition Constraints
Status transitions are enforced at the application level through service logic. While the database allows any transition, business rules in the CaseService validate permissible state changes based on user roles and case history. The OPEN status is the default initial state for newly created cases.

```mermaid
stateDiagram-v2
[*] --> OPEN
OPEN --> CLOSED : Manual close or expiration
OPEN --> CANCELED : User cancellation
OPEN --> DONE : Successful completion
CLOSED --> ARCHIVED : Periodic cleanup
DONE --> ARCHIVED : Periodic cleanup
CANCELED --> ARCHIVED : Periodic cleanup
```

**Diagram sources**
- [CaseStatusSchema.ts](file://server/prisma/generated/zod/inputTypeSchemas/CaseStatusSchema.ts#L0-L6)

**Section sources**
- [CaseStatusSchema.ts](file://server/prisma/generated/zod/inputTypeSchemas/CaseStatusSchema.ts#L0-L6)

## Pricing and Validation Rules
The pricing model for cases includes validation and formatting rules enforced at both the schema and service levels.

### Price Validation
- Price must be a valid number
- Negative prices are not permitted
- Minimum price constraints may be applied based on system configuration
- Price updates are restricted to the case author

### Baggage Format Validation
The baggage field undergoes transformation from comma-separated string to array format:
- Input string is split by commas
- Whitespace is trimmed from each item
- Empty entries are filtered out
- Result is returned as a string array in API responses

These rules ensure data consistency while maintaining flexibility in user input.

**Section sources**
- [case.ts](file://server/src/services/case.ts#L607-L634)

## User Relationships
The Case entity maintains several critical relationships with users, defining authorship, client interactions, and social features.

### Author Relationship
Every case has exactly one author who creates and owns the delivery request. The author relationship includes:
- User profile information
- Avatar data
- Rating statistics (confirmed ratings only)

### Client Assignment
Clients can be assigned to cases through the assignment mechanism. When a client is assigned:
- Notification is sent to the original author
- Client profile with avatar is included in the notification payload
- Assignment status is toggled (can be removed)

### Favorite System
Users can mark cases as favorites, creating a many-to-many relationship between users and cases.

```mermaid
erDiagram
USER ||--o{ FAVORITE : "creates"
CASE ||--o{ FAVORITE : "is favorited in"
USER {
string id PK
string name
}
CASE {
string id PK
string description
}
FAVORITE {
number id PK
string userId FK
string caseId FK
}
```

**Diagram sources**
- [FavoriteSchema.ts](file://server/prisma/generated/zod/modelSchema/FavoriteSchema.ts#L0-L38)
- [case.ts](file://server/src/services/case.ts#L87-L148)

**Section sources**
- [FavoriteSchema.ts](file://server/prisma/generated/zod/modelSchema/FavoriteSchema.ts#L0-L98)
- [case.ts](file://server/src/services/case.ts#L87-L148)

## Spatial Data Modeling and Routing
The takeNpass system implements sophisticated spatial modeling for delivery routes using a multi-point routing architecture.

### Route Entity Structure
Delivery routes are decomposed into three distinct entity types:
- **WayFrom**: Origin point of the delivery
- **WayTo**: Destination point of the delivery  
- **WayMiddle**: Optional intermediate waypoints

Each waypoint contains:
- Geographic coordinates (lat, lon as Decimal)
- Geocoding metadata (geometa as JSON)
- Optional date constraint
- Optional comment

### Multi-Point Routing Implementation
The routing system allows flexible route configurations:
- Minimum route: WayFrom + WayTo
- Complex routes: WayFrom + multiple WayMiddle + WayTo
- Each waypoint is linked to exactly one Case

### Geospatial Search
The system implements radius-based search using bounding box approximation:
- Converts search radius to latitude/longitude deltas
- Calculates minimum and maximum lat/lon values
- Constructs rectangular search area
- Queries cases with waypoints within the area

```mermaid
flowchart TD
Start([Search Request]) --> ParseCoords["Parse Latitude/Longitude"]
ParseCoords --> CalculateBounds["Calculate Bounding Box"]
CalculateBounds --> ApplyFormula["Apply Radius Formula"]
ApplyFormula --> MinMax["Determine Min/Max Lat/Lon"]
MinMax --> ConstructQuery["Construct Geospatial Query"]
ConstructQuery --> Execute["Execute Database Query"]
Execute --> FilterDates["Apply Date Filters"]
FilterDates --> ReturnResults["Return Matching Cases"]
```

**Diagram sources**
- [WayToSchema.ts](file://server/prisma/generated/zod/modelSchema/WayToSchema.ts#L0-L36)
- [case.ts](file://server/src/services/case.ts#L378-L433)

**Section sources**
- [WayToSchema.ts](file://server/prisma/generated/zod/modelSchema/WayToSchema.ts#L0-L36)
- [WayMiddleSchema.ts](file://server/prisma/generated/zod/modelSchema/WayMiddleSchema.ts#L72-L94)
- [case.ts](file://server/src/services/case.ts#L245-L287)

## Related Entities
The Case entity integrates with several auxiliary systems to provide full functionality.

### Message System
Cases serve as conversation contexts between authors and clients. Message threads are implicitly linked to cases through participant relationships, enabling direct communication about delivery details.

### Notification System
Case interactions trigger notifications for relevant users:
- New client assignments
- Message receipts
- Status changes
- Favorite additions

Notifications include serialized case data and involved user profiles.

### User Rating System
After case completion (DONE status), users can exchange ratings. The rating system captures:
- Numerical rating value
- Confirmation status
- Author and recipient information

Ratings are aggregated in user profiles to establish reputation.

**Section sources**
- [case.ts](file://server/src/services/case.ts#L607-L634)
- [UpdateCasePayload.ts](file://server/src/types/UpdateCasePayload.ts#L0-L5)

## Indexing and Search Optimization
The system implements targeted indexing strategies to optimize case discovery performance.

### Primary Indexes
- **ID Index**: Unique index on case.id for direct lookups
- **Author Index**: Index on authorId for user-specific case retrieval
- **Status Index**: Index on status field for filtering by lifecycle state
- **Expiration Index**: Index on expire_at for automatic case management

### Geospatial Search Optimization
While the current implementation uses lat/lon ranges, the schema supports future geospatial indexing:
- Composite indexes on (from.lat, from.lon) and (to.lat, to.lon)
- Potential for spatial indexes on waypoint coordinates
- Radius-based queries optimized through bounding box filtering

Search parameters are validated through the CaseListParamsSchema, which includes:
- Optional from/to coordinates
- Search radius
- Date ranges
- Language preferences
- Pagination controls

**Section sources**
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L48-L70)
- [CaseSchema.ts](file://server/prisma/generated/zod/modelSchema/CaseSchema.ts#L17-L59)

## Sample Queries
This section provides example queries for common use cases.

### Retrieve User's Cases
```typescript
const userCases = await caseService.db.findMany({
  where: { authorId: userId },
  include: {
    author: { include: { avatar: true } },
    from: true,
    to: true,
    middlepoints: true
  },
  orderBy: { createdAt: 'desc' }
});
```

### Discover Cases by Location
```typescript
const nearbyCases = await caseService.find({
  from: { lat: 50.45, lon: 30.52 },
  to: { lat: 53.89, lon: 27.57 },
  radius: 100,
  dates: [startDate, endDate]
});
```

### Find Case with Complete Relations
```typescript
const caseWithRelations = await caseService.db.findUniqueOrThrow({
  where: { id: caseId },
  include: {
    author: { include: { avatar: true, ratings: true } },
    from: true,
    to: true,
    middlepoints: true,
    favorites: { include: { user: true } }
  }
});
```

### Toggle Favorite Status
```typescript
const result = await caseService.toFavorite({
  caseId: 'uuid-here',
  userId: 'user-uuid-here'
});
// Returns { isFavorited: boolean }
```

**Section sources**
- [case.ts](file://server/src/services/case.ts#L87-L148)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L48-L70)

## Conclusion
The Case model in takeNpass serves as a comprehensive data structure for package delivery requests, integrating spatial routing, user relationships, and business logic. Its design supports flexible multi-point routes through dedicated waypoint entities, enables efficient geospatial discovery, and maintains data integrity through validation rules and status constraints. The model's relationships with users, favorites, and notifications create a rich ecosystem for delivery coordination. Future enhancements could include native geospatial indexing and more sophisticated routing algorithms.