import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserWhereInputSchema } from './UserWhereInputSchema';
import { UserUpdateWithoutAuth_sessionInputSchema } from './UserUpdateWithoutAuth_sessionInputSchema';
import { UserUncheckedUpdateWithoutAuth_sessionInputSchema } from './UserUncheckedUpdateWithoutAuth_sessionInputSchema';

export const UserUpdateToOneWithWhereWithoutAuth_sessionInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutAuth_sessionInput> = z.object({
  where: z.lazy(() => UserWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => UserUpdateWithoutAuth_sessionInputSchema),z.lazy(() => UserUncheckedUpdateWithoutAuth_sessionInputSchema) ]),
}).strict();

export default UserUpdateToOneWithWhereWithoutAuth_sessionInputSchema;
