import { useUserStore } from '@/store'
import { authClient } from '@/lib/auth'
import { addToast } from '@heroui/react'
import {
  Navbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  DropdownItem,
  DropdownTrigger,
  Dropdown,
  DropdownMenu,
  Avatar,
  Switch,
  user,
  Button,
  Listbox,
  ListboxItem,
  Image
} from "@heroui/react"
import { useEffect, useState } from 'react'

import { SunIcon } from '@/lib/svg/SunIcon'
import { MoonIcon } from '@/lib/svg/MoonIcon'
import { Link, useMatch, useRouter } from '@tanstack/react-router'
import { useWindowScroll } from '@uidotdev/usehooks'
import { useTranslation } from 'react-i18next'
import { LangSwitcher } from './LangSwitcher'
import { Notifications } from './Notifications'
import { WebSocketStatus } from './WebSocketStatus'
import MenuIcon from '@/lib/svg/MenuIcon'
import { motion } from 'framer-motion'
import { Drawer, <PERSON>er<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DrawerHeader } from "@heroui/react"
import { useMediaQuery } from '@uidotdev/usehooks'
import { UserMenu } from './UserMenu'
import IconUser from '@/lib/svg/UserIcon'
import { MessagesWidget } from './MessagesWidget'

interface Props {
  wsIsConnect?: boolean
}

export const NavBar = ({ wsIsConnect = false }) => {
  const { t } = useTranslation()

  const [darkToggle, setDarkToggle] = useState(false)
  const userStore = useUserStore((state) => state)
  // const [s, scrollTo] = useWindowScroll()
  const [showSidebar, setShowSidebar] = useState(false)


  const router = useRouter()
  const location = router.state.location

  const isSmallDevice = useMediaQuery('only screen and (max-width : 680px)')
  const isExtraSmallDevice = useMediaQuery('only screen and (max-width : 380px)')



  function themeSwitchHandler(e: React.ChangeEvent<HTMLInputElement>) {
    setDarkToggle(!e.target.checked)

    window.document.querySelector('html')?.classList.toggle('dark')
    window.localStorage.setItem('theme', !darkToggle ? 'dark' : 'light')
  }

  useEffect(() => {
    let isDark = window.localStorage.getItem('theme') == 'dark'
    window.document.querySelector('html')?.classList[isDark ? 'add' : 'remove']('dark')

    setDarkToggle(isDark)
  }, [])

  useEffect(() => {
    console.log('route:', location.pathname)
    setShowSidebar(false)
  }, [location.pathname])

  function clickHandler() {
    window.scrollTo({ top: 0, left: 0, behavior: 'smooth' })
  }

  function goToLogin() {
    router.navigate({
      to: '/login'
    })
  }

  return (
    <>
      <Navbar
        // d
        // position='static'
        className=' bg-default-50 w-full justify-between items-center p-0 h-14'
        classNames={{ wrapper: 'pl-2 pr-4 md:px-10 py-0 max-w-none my-0 justify-between items-center', content: 'p-0' }}
      >
        <NavbarBrand onClick={clickHandler}>
          {isSmallDevice && (
            <motion.div initial={{ scale: 1, opacity: 0 }} animate={{ opacity: 1 }} whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
              <Button size='sm' onClick={() => setShowSidebar((v) => !v)} className='mr-2' isIconOnly variant='flat'>
                <MenuIcon className='w-5 h-5' />
              </Button>
            </motion.div>
          )}
          <div className='flex items-center' onClick={clickHandler}>
            {/* <Image alt='Breathing app icon' className='rounded-full w-24' src='/heroblock_3.png' /> */}
            <Link to='/'>
              <span className='font-bold dark:text-slate-200 text-slate-600 text-2xl sm:text-3xl'>
                <span className='text-primary-500 font-extrabold'>take</span>
                <span>N</span>
                <span className='text-secondary-400 font-extrabold'>pass</span>
              </span>
            </Link>
          </div>
        </NavbarBrand>
        <NavbarContent as='ul' justify='end' className=''>
          {!isSmallDevice && <LangSwitcher />}
          {!isSmallDevice && (
            <Switch
              isSelected={!darkToggle}
              onChange={themeSwitchHandler}
              defaultSelected
              size='md'
              color='primary'
              startContent={<SunIcon />}
              endContent={<MoonIcon />}
              aria-label={t('Toggle theme')}
            />
          )}

          {/* <WebSocketStatus isConnected={wsIsConnect} /> */}
          {userStore.data?.id && <Notifications />}
          {userStore.data?.id && !isExtraSmallDevice && <MessagesWidget />}

          {!userStore.data?.id && !userStore.isFetching && (
            <Button
              isIconOnly={isExtraSmallDevice}
              onClick={goToLogin}
              variant='flat'
              aria-label={isExtraSmallDevice ? t('Login') : undefined}
            >
              <IconUser />
              {!isExtraSmallDevice && <span>{t('Login')}</span>}
            </Button>
          )}
          {!isSmallDevice && userStore.data?.id && (
            <Dropdown>
              <DropdownTrigger>
                <Button variant="bordered">
                  {userStore.data?.name}
                </Button>
              </DropdownTrigger>
              <DropdownMenu aria-label="Profile Actions">
                <DropdownItem key="profile" className="h-14 gap-2">
                  <p className="font-semibold">{userStore.data?.name}</p>
                  <p className="font-semibold">{userStore.data?.email}</p>
                </DropdownItem>
                <DropdownItem
                  key="profile-link"
                  onPress={() => window.location.href = '/profile'}
                >
                  {t('Profile')}
                </DropdownItem>
                <DropdownItem
                  key="settings-link"
                  onPress={() => window.location.href = '/settings'}
                >
                  {t('Settings')}
                </DropdownItem>
                <DropdownItem
                  key="logout"
                  color="danger"
                  onPress={async () => {
                    try {
                      const { authClient } = await import('@/lib/auth');

                      const result = await authClient.signOut();
                      if (result.data) {
                        userStore.setData(null);
                        addToast({ color: 'success', title: t('Logged out successfully') });
                        window.location.href = '/';
                      }
                    } catch (error) {

                      addToast({ color: 'danger', title: t('Logout failed') });
                    }
                  }}
                >
                  {t('Logout')}
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          )}
        </NavbarContent>
      </Navbar>

      {showSidebar && (
        <Drawer isOpen={showSidebar} onOpenChange={setShowSidebar} placement='left' size='sm'>
          <DrawerContent className='bg-default-50 dark:bg-zinc-950 w-80'>
            {(onClose) => (
              <>
                <DrawerHeader className='hidden' />
                <DrawerBody className='p-2 pt-0'>
                  <div className='flex flex-col space-y-3'>
                    <div className='flex justify-between items-center'>
                      <LangSwitcher />
                      <Switch
                        isSelected={!darkToggle}
                        onChange={themeSwitchHandler}
                        defaultSelected
                        size='md'
                        color='primary'
                        startContent={<SunIcon />}
                        endContent={<MoonIcon />}
                        aria-label={t('Toggle theme')}
                      />
                    </div>

                    <ul>
                      <li className='my-5 flex justify-center'>
                        <Avatar
                          isBordered
                          as='button'
                          className='transition-transform'
                          color='primary'
                          name={userStore?.data?.name}
                          size='lg'
                          src={userStore.data?.image}
                        />
                      </li>
                      <li key='profile' className='gap-2 flex justify-center'>
                        <div>
                          <p className='capitalize font-semibold'>{userStore.data?.name}</p>
                        </div>
                      </li>
                    </ul>
                    <div className='mx-auto'>
                      <UserMenu />
                    </div>
                  </div>
                </DrawerBody>
              </>
            )}
          </DrawerContent>
        </Drawer>
      )}
    </>
  )
}
