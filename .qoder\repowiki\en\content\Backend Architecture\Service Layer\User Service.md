# User Service

<cite>
**Referenced Files in This Document**   
- [user.ts](file://server/src/services/user.ts)
- [better-auth.ts](file://server/src/providers/better-auth.ts)
- [prisma.ts](file://server/src/providers/prisma.ts)
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts)
- [UserAuthenticationLogSchema.ts](file://server/prisma/generated/zod/modelSchema/UserAuthenticationLogSchema.ts)
- [UserAuthLogSeed.ts](file://server/prisma/seeds/UserAuthLogSeed.ts)
- [UserSeed.ts](file://server/prisma/seeds/UserSeed.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Domain Model](#domain-model)
3. [Authentication Implementation](#authentication-implementation)
4. [Profile Management](#profile-management)
5. [Session Handling](#session-handling)
6. [Service Interfaces](#service-interfaces)
7. [Security Considerations](#security-considerations)
8. [Common Issues and Solutions](#common-issues-and-solutions)
9. [Conclusion](#conclusion)

## Introduction
The User Service in the takeNpass backend provides core functionality for user management, authentication, and profile operations. Built on top of Better Auth and Prisma ORM, this service handles user registration, login, profile updates, and avatar management. The implementation follows a static class pattern with well-defined methods for each operation, ensuring consistency and ease of use across the application. This document provides a comprehensive overview of the service's architecture, implementation details, and integration points.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L1-L390)

## Domain Model

The domain model for the User Service consists of several interconnected entities that define user data, roles, authentication logs, and avatar management.

```mermaid
erDiagram
USER {
string id PK
string username
string email UK
string phone
string language
boolean emailVerified
string image
Roles role
timestamp createdAt
timestamp updatedAt
text about_me
boolean blocked
boolean confirmed
}
USER_AVATAR {
string userId PK,FK
string base64string
}
USER_AUTHENTICATION_LOG {
number id PK
string user_id FK
string ipAddress
string userAgent
timestamp createdAt
}
USER_RATING {
string caseId PK,FK
string userId PK,FK
string senderId PK,FK
number rating
text comment
boolean confirm
timestamp createdAt
}
USER ||--o{ USER_AUTHENTICATION_LOG : "has"
USER ||--o{ USER_RATING : "receives"
USER ||--|| USER_AVATAR : "has"
```

**Diagram sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L23-L69)
- [UserAuthenticationLogSchema.ts](file://server/prisma/generated/zod/modelSchema/UserAuthenticationLogSchema.ts#L65-L91)

### User Roles
The system implements a role-based access control system with five distinct roles:

```mermaid
classDiagram
class Roles {
+GUEST
+REG
+MODER
+ADMIN
+SU
}
```

The roles are defined in the `RolesSchema` and are used to control access to different parts of the application. The hierarchy appears to follow a progression from GUEST (unregistered users) to REG (registered users) to administrative roles (MODER, ADMIN, SU).

**Section sources**
- [RolesSchema.ts](file://server/prisma/generated/zod/inputTypeSchemas/RolesSchema.ts#L0-L6)

### Authentication Logs
The system maintains authentication logs to track user login activities:

```mermaid
classDiagram
class UserAuthenticationLog {
+number id
+string user_id
+string ipAddress
+string userAgent
+datetime createdAt
+User user
}
```

Each authentication attempt is recorded with the user's IP address and user agent string, providing valuable security information for monitoring and auditing purposes.

**Section sources**
- [UserAuthenticationLogSchema.ts](file://server/prisma/generated/zod/modelSchema/UserAuthenticationLogSchema.ts#L65-L91)
- [UserAuthLogSeed.ts](file://server/prisma/seeds/UserAuthLogSeed.ts#L0-L30)

## Authentication Implementation

The authentication system leverages Better Auth as the primary authentication provider, integrating with Prisma for database operations.

```mermaid
sequenceDiagram
participant Client
participant UserService
participant BetterAuth
participant Database
Client->>UserService : createSession(email, password)
UserService->>BetterAuth : signInEmail({email, password})
BetterAuth->>Database : Verify credentials
Database-->>BetterAuth : User data
BetterAuth-->>UserService : Sign in result
UserService->>Database : Find user by email
Database-->>UserService : User with avatar
UserService->>UserService : Extract session cookie
UserService-->>Client : {session, sessionCookie, user}
alt Registration
Client->>UserService : create(payload)
UserService->>BetterAuth : signUpEmail({email, password, name})
BetterAuth-->>UserService : Sign up result
UserService->>Database : Create user record
Database-->>UserService : Created user
UserService-->>Client : User object
end
```

**Diagram sources**
- [user.ts](file://server/src/services/user.ts#L250-L300)
- [better-auth.ts](file://server/src/providers/better-auth.ts#L0-L34)

### Registration Process
The user registration process involves creating a user account through Better Auth and synchronizing the user data with the application's database:

1. The `create` method in UserService initiates the registration
2. Better Auth handles email/password validation and password hashing
3. A user record is created in the application database with the ID from Better Auth
4. Additional profile information is stored in the local database

This approach allows the application to benefit from Better Auth's secure password handling while maintaining flexibility in the user data model.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L333-L389)

### Login Process
The login process follows a similar pattern, leveraging Better Auth for credential verification:

1. The `createSession` method receives email and password
2. Better Auth's `signInEmail` API verifies the credentials
3. Upon successful authentication, the session information is extracted
4. The user's profile is retrieved from the database, including avatar information
5. Session cookie and user data are returned to the client

The implementation includes comprehensive logging to aid in debugging authentication issues.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L250-L293)

## Profile Management

The User Service provides several methods for managing user profiles, including updates to personal information and avatar management.

```mermaid
flowchart TD
Start([Update Profile]) --> ValidateInput["Validate Input Data"]
ValidateInput --> ProcessPhone["Process Phone Number"]
ProcessPhone --> UpdateDB["Update Database"]
UpdateDB --> ReturnResult["Return Updated User"]
Start --> UpdateAvatar["Update Avatar"]
UpdateAvatar --> CheckExist["Check Avatar Exists"]
CheckExist --> |Yes| UpdateExisting["Update Base64 String"]
CheckExist --> |No| CreateNew["Create Avatar Record"]
UpdateExisting --> ReturnAvatar["Return Avatar Info"]
CreateNew --> ReturnAvatar
```

**Diagram sources**
- [user.ts](file://server/src/services/user.ts#L274-L332)

### Profile Update
The `update` method allows users to modify their profile information:

- Email, phone, username, and about_me fields can be updated
- Phone numbers are sanitized by removing non-numeric characters
- The method returns the updated user object with avatar information

The implementation uses Prisma's update operation with proper error handling to ensure data consistency.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L274-L293)

### Avatar Management
Avatar management is implemented through the `updateAvatar` method:

- Uses Prisma's connectOrCreate pattern to handle both creation and updates
- Stores avatar images as base64-encoded strings in the database
- Associates avatars with users through a one-to-one relationship
- Returns the updated avatar information along with the user ID

This approach simplifies avatar management by avoiding file storage complexities while still allowing for custom avatars.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L301-L320)

### Language Preferences
Users can update their language preferences through the `updateLanguage` method:

- Accepts a language code and user ID
- Validates that the user exists before updating
- Returns the updated language setting
- Language preferences are used for localization throughout the application

**Section sources**
- [user.ts](file://server/src/services/user.ts#L240-L249)

## Session Handling

Session management is handled by Better Auth with configuration options optimized for security and usability.

```mermaid
classDiagram
class SessionConfig {
+string baseURL
+string secret
+SessionOptions session
+string[] trustedOrigins
}
class SessionOptions {
+CookieCache cookieCache
+number expiresIn
+number updateAge
}
class CookieCache {
+boolean enabled
+number maxAge
}
SessionConfig --> SessionOptions
SessionOptions --> CookieCache
```

**Diagram sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L0-L34)

### Session Configuration
The session configuration includes the following key parameters:

- **Expires In**: 7 days (604800 seconds) - sessions expire after one week
- **Update Age**: 24 hours (86400 seconds) - sessions are refreshed daily
- **Cookie Cache**: Enabled with 24-hour max age for improved performance
- **Trusted Origins**: Specific localhost addresses for development

This configuration balances security (relatively short session duration) with usability (automatic session refresh).

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L15-L25)

### Session Flow
The session flow integrates Better Auth's session management with the application's user data:

1. Authentication requests are forwarded to Better Auth
2. Better Auth creates and manages the session
3. Session cookies are extracted from the response headers
4. User profile data is retrieved separately from the application database
5. Both session information and user data are returned to the client

This separation of concerns allows Better Auth to handle the complex aspects of session security while the application maintains control over user profile data.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L250-L293)

## Service Interfaces

The User Service exposes a well-defined interface with methods for common user operations.

### Public Methods

| Method | Parameters | Return Type | Description |
|-------|------------|-------------|-------------|
| `create` | `{email, username, phone?, password, avatar?}` | `Promise<User>` | Creates a new user account |
| `createSession` | `{login, password}` | `Promise<{session, sessionCookie, user}>` | Authenticates a user and creates a session |
| `update` | `{id} & Partial{email, phone, username, about_me}` | `Promise<User>` | Updates user profile information |
| `updateAvatar` | `{userId, base64string}` | `Promise<{avatar, id}>` | Updates or creates user avatar |
| `findBy` | `{field, value}` | `Promise<User \| null>` | Finds user by email, username, id, or phone |
| `profile` | `{userId}` | `Promise<User>` | Retrieves user profile with ratings |
| `setRating` | `{userId, senderId, caseId, rating, comment}` | `Promise<UserRating>` | Sets or updates a user rating |

**Section sources**
- [user.ts](file://server/src/services/user.ts#L1-L390)

### Method Implementation Details

#### createUser
```mermaid
sequenceDiagram
participant Client
participant UserService
participant BetterAuth
participant Database
Client->>UserService : create(payload)
UserService->>BetterAuth : signUpEmail(body)
BetterAuth-->>UserService : signUpResult
UserService->>Database : create user record
Database-->>UserService : created user
UserService-->>Client : user object
```

The method creates a user in Better Auth and synchronizes the data with the application database, ensuring consistent user IDs across systems.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L333-L389)

#### createSession
```mermaid
sequenceDiagram
participant Client
participant UserService
participant BetterAuth
participant Database
Client->>UserService : createSession(credentials)
UserService->>BetterAuth : signInEmail(body)
BetterAuth-->>UserService : signInResult
UserService->>Database : findUnique user
Database-->>UserService : user with avatar
UserService->>UserService : extract session cookie
UserService-->>Client : session data
```

The method authenticates with Better Auth and enriches the response with additional user data from the application database.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L250-L293)

#### update
```mermaid
sequenceDiagram
participant Client
participant UserService
participant Database
Client->>UserService : update(userData)
UserService->>UserService : sanitize phone
UserService->>Database : update user
Database-->>UserService : updated user
UserService-->>Client : user object
```

The method updates user profile information with phone number sanitization and returns the updated user object.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L274-L293)

## Security Considerations

The User Service implementation incorporates several security measures to protect user data and prevent common vulnerabilities.

### Password Management
The system relies on Better Auth for password management, which provides:

- Secure password hashing using industry-standard algorithms
- Protection against brute force attacks
- Secure storage of password hashes
- No direct handling of plaintext passwords in the application code

By delegating password management to Better Auth, the application benefits from specialized security expertise and reduces its attack surface.

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L10-L13)

### Input Validation
The service implements input validation for various fields:

- Email validation through Better Auth's built-in mechanisms
- Phone number sanitization by removing non-numeric characters
- Use of Zod schemas for type safety and validation
- Prisma's type system for database-level validation

These layers of validation help prevent injection attacks and ensure data consistency.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L280-L282)
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L23-L69)

### Session Security
The session configuration includes several security features:

- Cryptographically secure session IDs generated by Better Auth
- HTTP-only cookies to prevent XSS attacks
- Secure cookie flags for HTTPS transmission
- Reasonable session expiration (7 days) to balance security and usability
- Daily session refresh to mitigate session fixation attacks

The trusted origins list restricts cross-origin requests to known development addresses, preventing unauthorized access during development.

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L15-L28)

### Data Protection
The system protects sensitive user data through:

- Database-level constraints and type checking
- Selective field exposure in API responses
- Proper error handling that doesn't leak implementation details
- Use of Prisma's include/exclude patterns to control data retrieval

The implementation avoids exposing sensitive fields like password hashes or internal IDs unnecessarily.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L100-L130)

## Common Issues and Solutions

### Session Expiration
**Issue**: Users are unexpectedly logged out before the expected 7-day period.

**Solution**: The system automatically refreshes sessions every 24 hours through the updateAge configuration. However, if a user doesn't interact with the application for more than 7 days, their session will expire. To mitigate this:

1. Implement client-side session refresh before expiration
2. Provide clear UI indicators of session status
3. Implement graceful re-authentication flows
4. Consider extending the expiresIn value in production if user feedback indicates it's too short

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L21-L22)

### Phone Number Formatting
**Issue**: Inconsistent phone number storage due to different input formats.

**Solution**: The update method includes phone number sanitization that removes all non-numeric characters (except +):

```typescript
data.phone = data.phone.replace(/[^\d+]/g, '')
```

This ensures consistent storage of phone numbers. For international applications, consider integrating a more sophisticated library like libphonenumber-js for validation and formatting.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L280-L282)

### Avatar Storage
**Issue**: Storing avatars as base64 strings in the database can lead to performance issues as the database grows.

**Solution**: The current implementation is suitable for development and small-scale applications. For production at scale:

1. Consider migrating to cloud storage (S3, Google Cloud Storage, etc.)
2. Store only image URLs in the database
3. Implement caching for frequently accessed avatars
4. Add image size limits and compression

The connectOrCreate pattern used in updateAvatar provides a solid foundation for future storage migrations.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L301-L320)

### Error Handling
**Issue**: Generic error messages may make debugging difficult.

**Solution**: The implementation includes comprehensive logging:

- Login attempts are logged with the email address
- Authentication results are logged for debugging
- Errors are logged with stack traces
- User creation and updates are logged

In production, consider implementing structured logging and monitoring to track authentication patterns and detect potential security issues.

**Section sources**
- [user.ts](file://server/src/services/user.ts#L251-L252)

## Conclusion
The User Service in the takeNpass backend provides a robust foundation for user management, authentication, and profile operations. By leveraging Better Auth for core authentication functionality and Prisma for database operations, the implementation achieves a good balance between security, maintainability, and flexibility. The service's well-defined interface makes it easy to integrate with other parts of the application, while the comprehensive logging and error handling facilitate debugging and monitoring. For production deployment, consider enhancing the avatar storage strategy and fine-tuning the session configuration based on user behavior and security requirements.