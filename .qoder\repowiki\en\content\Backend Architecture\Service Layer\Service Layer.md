# Service Layer

<cite>
**Referenced Files in This Document**   
- [case.ts](file://server/src/services/case.ts)
- [user.ts](file://server/src/services/user.ts)
- [geo.ts](file://server/src/services/geo.ts)
- [message.ts](file://server/src/services/message.ts)
- [notification.ts](file://server/src/services/notification.ts)
- [prisma.ts](file://server/src/providers/prisma.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Service Layer Architecture](#service-layer-architecture)
3. [Case Service](#case-service)
4. [User Service](#user-service)
5. [Geo Service](#geo-service)
6. [Message Service](#message-service)
7. [Notification Service](#notification-service)
8. [Service-Prisma ORM Relationship](#service-prisma-orm-relationship)
9. [Error Handling Patterns](#error-handling-patterns)
10. [Conclusion](#conclusion)

## Introduction
The service layer in the takeNpass backend represents the core business logic implementation, completely separated from routing concerns. This documentation provides a comprehensive analysis of the services architecture, focusing on the five primary service classes: CaseService, UserService, GeoService, MessageService, and NotificationService. Each service encapsulates specific domain functionality while maintaining clean separation of concerns and leveraging Prisma ORM for data persistence. The services are designed as static classes with well-defined methods that handle business operations, data transformation, and inter-service communication.

## Service Layer Architecture
The service layer follows a clean architecture pattern where business logic is isolated from infrastructure concerns. All services are implemented as static classes with static methods, eliminating the need for instantiation and providing a straightforward API for business operations. This approach enables direct method calls from routing handlers while maintaining statelessness. The services are located in the `server/src/services` directory and follow a consistent pattern: each service class interacts with Prisma through the `$prisma` client, handles data transformation, implements business rules, and coordinates with other services when necessary. The architecture promotes reusability, testability, and maintainability by separating business logic from HTTP concerns and database access patterns.

```mermaid
graph TB
subgraph "Service Layer"
CS[CaseService]
US[UserService]
GS[GeoService]
MS[MessageService]
NS[NotificationService]
end
subgraph "Data Access"
Prisma[$prisma]
end
subgraph "External Services"
GeoAPI[Geoapify/LocationIQ]
end
CS --> Prisma
US --> Prisma
GS --> GeoAPI
MS --> Prisma
NS --> Prisma
CS --> NS
US --> NS
MS --> NS
CS --> US
MS --> US
style CS fill:#f9f,stroke:#333
style US fill:#f9f,stroke:#333
style GS fill:#f9f,stroke:#333
style MS fill:#f9f,stroke:#333
style NS fill:#f9f,stroke:#333
```

**Diagram sources**
- [case.ts](file://server/src/services/case.ts)
- [user.ts](file://server/src/services/user.ts)
- [geo.ts](file://server/src/services/geo.ts)
- [message.ts](file://server/src/services/message.ts)
- [notification.ts](file://server/src/services/notification.ts)
- [prisma.ts](file://server/src/providers/prisma.ts)

**Section sources**
- [case.ts](file://server/src/services/case.ts)
- [user.ts](file://server/src/services/user.ts)
- [geo.ts](file://server/src/services/geo.ts)
- [message.ts](file://server/src/services/message.ts)
- [notification.ts](file://server/src/services/notification.ts)

## Case Service
The CaseService handles all business logic related to delivery case management, including creation, search, assignment, and lifecycle operations. It serves as the central coordinator for case-related functionality, interacting with multiple Prisma models including Case, WayFrom, WayTo, WayMiddle, and Favorite. The service implements sophisticated search capabilities with geospatial filtering, allowing users to find cases based on origin, destination, date ranges, and proximity radius. Case creation includes automatic subscription detection, where the system identifies users who have requested similar routes and notifies them of the new case. The service also manages client assignment, enabling users to express interest in cases and facilitating the connection between case authors and potential clients.

```mermaid
classDiagram
class CaseService {
+static onServerInit() Promise~UpdateManyOutput~
+static findSubscribesAfterCreate(createdCase) Promise~SubscribersResult~
+static toFavorite(params) Promise~FavoriteResult~
+static id(id) Promise~FormattedCase~
+static myList(type, userId) Promise~FormattedCase[]~
+static list(params) Promise~CaseListResult~
+static makeLatLonNearbyPoints(params) LatLonType
+static update(payload) Promise~UpdateResult~
+static create(payload) Promise~FormattedCase~
+static assignClient(caseId, client) Promise~AssignmentResult~
-static formatCaseForApi(casE) FormattedCase
}
class CaseListParams {
+from : LatLon
+to : LatLon
+page : number
+limit : number
+pointsGeoreverse : boolean
+lang : string
+radius : number
+dates : Date[]
+isRequest : boolean
+expire_at : Date
}
class CreateCasePayload {
+authorId : string
+data : CaseData
}
class UpdateCasePayload {
+authorId : string
+data : CaseUpdateData
}
CaseService --> NotificationService : "notifies"
CaseService --> UserService : "retrieves user data"
CaseService --> GeoService : "geocoding"
CaseService --> $prisma : "data persistence"
```

**Diagram sources**
- [case.ts](file://server/src/services/case.ts#L1-L635)

**Section sources**
- [case.ts](file://server/src/services/case.ts#L1-L635)

## User Service
The UserService manages all user-related functionality including authentication, profile management, and rating operations. It serves as the primary interface for user data operations, handling both basic profile information and more complex relationships such as ratings and reviews. The authentication system integrates with Better Auth, which handles password hashing and session management, while the UserService focuses on application-specific user operations. Profile management includes updating personal information, language preferences, and avatar images. The service also implements a comprehensive rating system that allows users to rate each other after completing cases, with functionality to retrieve both received and sent ratings. The user search functionality supports lookup by email, username, or phone number, with special handling for username searches since this field is not unique in the database.

```mermaid
classDiagram
class UserService {
+static myRatings(params) Promise~RatingResult~
+static profile(userId) Promise~UserProfile~
+static myProfile(userId) Promise~UserProfile~
+static sendedRatings(params) Promise~SentRatingResult~
+static setRating(params) Promise~Rating~
+static updateLanguage(params) Promise~string~
+static createSession(login, password) Promise~SessionResult~
+static update(user) Promise~UpdatedUser~
+static updateAvatar(params) Promise~AvatarResult~
+static findBy(field, value) Promise~User | null~
+static create(payload) Promise~User~
}
class RatingParams {
+userId : string
+senderId : string
+caseId : string
+rating : number
+comment : string
}
class UpdateUserPayload {
+id : string
+email? : string
+phone? : string
+username? : string
+about_me? : string
}
UserService --> NotificationService : "notifies"
UserService --> $prisma : "data persistence"
UserService --> auth : "authentication"
```

**Diagram sources**
- [user.ts](file://server/src/services/user.ts#L1-L389)

**Section sources**
- [user.ts](file://server/src/services/user.ts#L1-L389)

## Geo Service
The GeoService provides location processing capabilities and integrates with external geocoding APIs to convert between addresses and geographic coordinates. It implements a flexible provider system that supports multiple geocoding services, currently including Geoapify and LocationIQ, with configuration determining which services are active. The service follows a fallback pattern, attempting to fulfill requests with each enabled provider in sequence until a successful response is received or all providers have been tried. This approach enhances reliability by providing redundancy in case one service is unavailable. The GeoService handles both forward geocoding (address to coordinates) and reverse geocoding (coordinates to address), with results normalized into a consistent GeoMeta format regardless of the underlying provider. The service also includes functionality to retrieve location suggestions based on partial queries, supporting the user interface's autocomplete features for destination selection.

```mermaid
classDiagram
class GeoService {
+static enabledServiceList : string[]
+static services : Object
+static findLocation(query, lang) Promise~GeoMeta[]~
+static reverse(lat, lng, lang) Promise~GeoMeta | GeoMeta[]~
}
class GeoHelper {
<<abstract>>
}
class Geoapify {
+static api_key : string
+static api_url : string
+static transformRes(data) GeoMeta[]
+static _autocomplete(query) Promise~GeoMeta[]~
+static findLocation(query, lang) Promise~GeoMeta[]~
+static reverse(lat, lng, lang) Promise~GeoMeta[]~
}
class LocationIQ {
+static api_key : string
+static api_url : string
+static transformRes(data) GeoMeta[]
+static findLocation(query, lang) Promise~GeoMeta[]~
+static reverse(lat, lng, lang) Promise~GeoMeta | GeoMeta[]~
}
GeoService --> Geoapify : "delegates"
GeoService --> LocationIQ : "delegates"
Geoapify --|> GeoHelper
LocationIQ --|> GeoHelper
```

**Diagram sources**
- [geo.ts](file://server/src/services/geo.ts#L1-L218)

**Section sources**
- [geo.ts](file://server/src/services/geo.ts#L1-L218)

## Message Service
The MessageService implements the chat functionality for user communication within the takeNpass platform. It manages the creation, retrieval, and status tracking of messages between users, supporting both individual message operations and conversation-level interactions. The service provides methods to send messages, mark them as read, and retrieve message histories with specific partners. It also includes functionality to identify chat partners and calculate unread message counts, enabling the user interface to display notification badges. The service maintains referential integrity by linking messages to sender and receiver users through their IDs, and includes avatar information in message queries to support rich chat interfaces. When a message is sent, the service triggers notification creation through the NotificationService, ensuring that recipients are alerted to new messages even when not actively using the application.

```mermaid
classDiagram
class MessageService {
+static setAsRead(userId, messageId) Promise~void~
+static unreadCount(userId) Promise~number~
+static getPartnerById(userId) Promise~User~
+static send(params) Promise~number~
+static notificationAfterSend(params) Promise~void~
+static messagesByPartner(userId, partnerUserId) Promise~Message[]~
+static chats(userId) Promise~Chat[]~
+static partners(userId) Promise~User[]~
}
class SendMessageParams {
+receiverId : string
+senderId : string
+content : string
}
class Chat {
+partner : User
+messages : Message[]
+undreadCount : number
}
MessageService --> UserService : "retrieves user data"
MessageService --> NotificationService : "triggers notifications"
MessageService --> $prisma : "data persistence"
```

**Diagram sources**
- [message.ts](file://server/src/services/message.ts#L1-L263)

**Section sources**
- [message.ts](file://server/src/services/message.ts#L1-L263)

## Notification Service
The NotificationService manages alert creation and delivery across the takeNpass platform, providing a centralized system for user notifications. It supports various notification types including new messages, case assignments, and rating requests, with internationalization support through i18next for multilingual message content. The service creates notification records in the database and simultaneously pushes them to connected clients via WebSocket, ensuring real-time delivery. Each notification includes structured data about the event, a localized message string, and metadata such as creation timestamp and read status. The service provides comprehensive CRUD operations for notifications, including retrieval of all notifications for a user, marking notifications as read (individually or in bulk), and deletion. Notifications are automatically associated with relevant entities such as cases, users, and senders, creating a rich context that supports detailed notification displays in the user interface.

```mermaid
classDiagram
class NotificationService {
+static createNotification(params) Promise~void~
+static getNotificationsForUser(userId) Promise~SocketNotification[]~
+static markAllNotificationAsRead(params) Promise~number~
+static markNotificationAsRead(params) Promise~boolean~
+static getUnreadNotificationsForUser(params) Promise~Notification[]~
+static deleteNotification(params) Promise~void~
}
class CreateNotificationParams {
+type : NotificationType
+data : NotificationData
}
class SocketNotification {
+data : NotificationData
+message : string
+type : string
}
NotificationService --> IO : "real-time delivery"
NotificationService --> i18next : "localization"
NotificationService --> $prisma : "data persistence"
```

**Diagram sources**
- [notification.ts](file://server/src/services/notification.ts#L1-L197)

**Section sources**
- [notification.ts](file://server/src/services/notification.ts#L1-L197)

## Service-Prisma ORM Relationship
The services in the takeNpass backend maintain a direct and consistent relationship with Prisma ORM, using the shared `$prisma` client instance for all database operations. Each service typically defines static references to specific Prisma model clients, such as `CaseService.db` for case operations or `UserService.db` for user operations, creating a clean abstraction layer over the Prisma client. This pattern allows services to encapsulate data access logic while maintaining type safety through Prisma's generated types. The services leverage Prisma's powerful query capabilities, including complex filtering, relational queries with include/select clauses, and transaction support where needed. The architecture demonstrates proper separation of concerns, with services handling business logic and data transformation while delegating raw data persistence to Prisma. This relationship enables efficient database operations with proper error handling and ensures that business rules are consistently applied regardless of the entry point to the service methods.

**Section sources**
- [case.ts](file://server/src/services/case.ts#L1-L635)
- [user.ts](file://server/src/services/user.ts#L1-L389)
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [notification.ts](file://server/src/services/notification.ts#L1-L197)
- [prisma.ts](file://server/src/providers/prisma.ts#L1-L5)

## Error Handling Patterns
The service layer implements consistent error handling patterns across all services, prioritizing both user experience and system reliability. Services typically use Prisma's built-in error handling for database operations, allowing Prisma-specific exceptions to propagate when appropriate while catching and transforming them into application-specific errors when necessary. Authentication and authorization checks are performed at the beginning of sensitive operations, with unauthorized access attempts resulting in descriptive error messages. The services employ defensive programming techniques, including input validation and null checks, to prevent runtime errors. For external service integrations like geocoding APIs, the services implement try-catch blocks with fallback mechanisms and error logging, ensuring that temporary service outages do not completely disrupt functionality. The error handling strategy balances thoroughness with simplicity, avoiding overly complex error hierarchies while providing sufficient information for debugging and user feedback.

**Section sources**
- [case.ts](file://server/src/services/case.ts#L1-L635)
- [user.ts](file://server/src/services/user.ts#L1-L389)
- [geo.ts](file://server/src/services/geo.ts#L1-L218)
- [message.ts](file://server/src/services/message.ts#L1-L263)
- [notification.ts](file://server/src/services/notification.ts#L1-L197)

## Conclusion
The service layer in the takeNpass backend effectively separates business logic from routing concerns, creating a maintainable and scalable architecture. Each service focuses on a specific domain area while maintaining appropriate dependencies and interactions with other services. The consistent static class pattern provides a clean API for business operations, and the integration with Prisma ORM enables efficient data persistence with type safety. The services demonstrate well-considered design decisions around error handling, external service integration, and data transformation, resulting in a robust foundation for the application's core functionality. This architecture supports the platform's requirements for delivery case management, user communication, and location-based services while providing clear extension points for future features.