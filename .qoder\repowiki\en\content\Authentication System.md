# Authentication System

<cite>
**Referenced Files in This Document**   
- [better-auth.ts](file://server/src/providers/better-auth.ts)
- [auth.ts](file://server/src/providers/auth.ts)
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts)
- [VerificationTokenSchema.ts](file://server/prisma/generated/zod/modelSchema/VerificationTokenSchema.ts)
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx)
- [Login.tsx](file://frontend/src/components/Login.tsx)
- [ProfileSettings.tsx](file://frontend/src/components/ProfileSettings.tsx)
- [LoginPage.tsx](file://frontend/src/pages/LoginPage.tsx)
- [RegPage.tsx](file://frontend/src/pages/RegPage.tsx)
- [ProfilePage.tsx](file://frontend/src/pages/ProfilePage.tsx)
- [SettingsPage.tsx](file://frontend/src/pages/SettingsPage.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Authentication Implementation with better-auth](#authentication-implementation-with-better-auth)
3. [Domain Model](#domain-model)
4. [Authentication Flow](#authentication-flow)
5. [User Interface Components](#user-interface-components)
6. [Security Measures](#security-measures)
7. [Error Handling](#error-handling)
8. [Session Management](#session-management)
9. [Password Reset and Account Verification](#password-reset-and-account-verification)
10. [Conclusion](#conclusion)

## Introduction
The takeNpass application implements a robust authentication system using the better-auth library, supporting both credential-based and social login strategies. This document provides a comprehensive overview of the authentication architecture, covering user registration, login, profile management, session handling, and security practices. The system is designed to be secure, user-friendly, and maintainable, with clear separation between frontend and backend components.

## Authentication Implementation with better-auth

The authentication system is built on better-auth, a modern authentication library that provides a clean API for handling user sessions, password hashing, and token management. The configuration is centralized in the `better-auth.ts` file, which sets up the core authentication parameters including session expiration, cookie handling, and database integration with Prisma.

```mermaid
classDiagram
class AuthConfig {
+string baseURL
+string secret
+boolean emailAndPassword.enabled
+boolean emailAndPassword.requireEmailVerification
+SessionConfig session
+string[] trustedOrigins
}
class SessionConfig {
+CookieCacheConfig cookieCache
+number expiresIn
+number updateAge
}
class CookieCacheConfig {
+boolean enabled
+number maxAge
}
AuthConfig --> SessionConfig : "contains"
SessionConfig --> CookieCacheConfig : "contains"
```

**Diagram sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [auth.ts](file://server/src/providers/auth.ts#L1-L7)

## Domain Model

The authentication system revolves around three core entities: User, Session, and VerificationToken. These entities are defined in the Prisma schema and exposed through Zod validation schemas for type safety and data integrity.

### User Entity
The User entity represents application users and contains essential profile information, authentication data, and account status flags.

```mermaid
erDiagram
USER {
string id PK
string username
string email UK
boolean emailVerified
string name
datetime createdAt
datetime updatedAt
string about_me
string phone
boolean blocked
boolean confirmed
string language
string role
}
```

**Diagram sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L23-L69)

### Session Entity
The Session entity tracks active user sessions, enabling persistent login states and secure session management.

```mermaid
erDiagram
SESSION {
string id PK
string userId FK
string sessionToken UK
timestamp expires
timestamp createdAt
timestamp updatedAt
}
USER ||--o{ SESSION : "has"
```

**Diagram sources**
- [SessionFindManyArgsSchema.ts](file://server/prisma/generated/zod/outputTypeSchemas/SessionFindManyArgsSchema.ts#L23-L34)

### VerificationToken Entity
The VerificationToken entity handles temporary tokens for account verification and password reset workflows.

```mermaid
erDiagram
VERIFICATION_TOKEN {
string id PK
string identifier
string token UK
timestamp expires
}
```

**Diagram sources**
- [VerificationTokenSchema.ts](file://server/prisma/generated/zod/modelSchema/VerificationTokenSchema.ts#L0-L33)

**Section sources**
- [UserSchema.ts](file://server/prisma/generated/zod/modelSchema/UserSchema.ts#L23-L69)
- [VerificationTokenSchema.ts](file://server/prisma/generated/zod/modelSchema/VerificationTokenSchema.ts#L0-L33)

## Authentication Flow

The authentication flow in takeNpass follows a standard pattern for modern web applications, with clear separation between frontend interactions and backend processing.

```mermaid
sequenceDiagram
participant User as "User"
participant Frontend as "Frontend (React)"
participant Backend as "Backend (better-auth)"
participant Database as "Database (Prisma)"
User->>Frontend : Navigate to /login or /reg
Frontend->>User : Display login/registration form
User->>Frontend : Enter credentials and submit
Frontend->>Backend : POST /api/auth/sign-in or /api/auth/sign-up
Backend->>Database : Validate credentials and create session
Database-->>Backend : User and session data
Backend->>Backend : Hash password and generate tokens
Backend->>Frontend : Return session and user data
Frontend->>Frontend : Store session in state management
Frontend->>User : Redirect to protected route
```

**Diagram sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [Login.tsx](file://frontend/src/components/Login.tsx#L0-L37)
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx#L0-L85)

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)
- [Login.tsx](file://frontend/src/components/Login.tsx#L0-L37)
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx#L0-L85)

## User Interface Components

The frontend authentication interface consists of several React components that provide a seamless user experience for registration, login, and profile management.

### Login Component
The Login component handles user authentication with email and password credentials.

```mermaid
flowchart TD
Start([Login Form]) --> EmailInput["Email Input Field"]
EmailInput --> PasswordInput["Password Input Field"]
PasswordInput --> SubmitButton["Sign In Button"]
SubmitButton --> Validation["Validate Input"]
Validation --> InputValid{"Valid Input?"}
InputValid --> |No| ShowError["Show Error Messages"]
InputValid --> |Yes| APIRequest["Call authClient.signIn.email()"]
APIRequest --> Response{"Success?"}
Response --> |No| HandleError["Show Error Toast"]
Response --> |Yes| UpdateStore["Update User Store"]
UpdateStore --> Redirect["Navigate to Home"]
ShowError --> End([Form])
HandleError --> End
Redirect --> End
```

**Diagram sources**
- [Login.tsx](file://frontend/src/components/Login.tsx#L0-L37)

### Registration Component
The Registration component manages new user onboarding with form validation and error handling.

```mermaid
flowchart TD
Start([Registration Form]) --> AvatarUpload["Avatar Upload"]
AvatarUpload --> UsernameInput["Username Input"]
UsernameInput --> EmailInput["Email Input"]
EmailInput --> PasswordInput["Password Input"]
PasswordInput --> SubmitButton["Sign Up Button"]
SubmitButton --> Validation["Validate Input"]
Validation --> InputValid{"Valid Input?"}
InputValid --> |No| ShowError["Show Field Errors"]
InputValid --> |Yes| APIRequest["Call authClient.signUp.email()"]
APIRequest --> Response{"Success?"}
Response --> |No| HandleError["Parse and Display Errors"]
Response --> |Yes| UpdateStore["Update User Store"]
UpdateStore --> Redirect["Navigate to Home"]
ShowError --> End([Form])
HandleError --> End
Redirect --> End
```

**Diagram sources**
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx#L39-L85)

### Profile Management Components
The profile management system allows users to view and update their information through dedicated components.

```mermaid
classDiagram
class ProfileContainer {
+children : JSX.Element
}
class ProfileInfo {
+user : User
+isSelf : boolean
}
class ProfileSettings {
+username : string
+email : string
+phone : string
+avatar : string
+aboutMe : string
}
ProfileContainer --> ProfileInfo : "contains"
ProfileContainer --> ProfileSettings : "contains"
ProfileSettings --> Upload : "uses"
ProfileSettings --> trpc : "uses"
```

**Diagram sources**
- [ProfileContainer.tsx](file://frontend/src/components/ProfileContainer.tsx#L0-L30)
- [ProfileInfo.tsx](file://frontend/src/components/ProfileInfo.tsx#L0-L31)
- [ProfileSettings.tsx](file://frontend/src/components/ProfileSettings.tsx#L0-L85)

**Section sources**
- [LoginPage.tsx](file://frontend/src/pages/LoginPage.tsx#L0-L10)
- [RegPage.tsx](file://frontend/src/pages/RegPage.tsx#L0-L10)
- [ProfilePage.tsx](file://frontend/src/pages/ProfilePage.tsx#L0-L35)
- [SettingsPage.tsx](file://frontend/src/pages/SettingsPage.tsx#L0-L23)

## Security Measures

The authentication system implements several security best practices to protect user data and prevent common vulnerabilities.

### Password Handling
Passwords are securely hashed using industry-standard algorithms provided by better-auth. The system does not store plain text passwords and uses salted hashes for additional protection.

### Session Security
Sessions are protected with secure cookie settings and have a limited lifespan of 7 days, with automatic renewal every 24 hours to balance security and user convenience.

### Input Validation
All user inputs are validated both on the client and server side to prevent injection attacks and ensure data integrity. The system uses Zod schemas for consistent validation across the application.

### Trusted Origins
The authentication system restricts requests to specific trusted origins to prevent cross-site request forgery (CSRF) attacks.

```mermaid
flowchart TD
Request["Incoming Request"] --> OriginCheck["Check Origin Against Trusted List"]
OriginCheck --> ValidOrigin{"Origin Trusted?"}
ValidOrigin --> |No| Reject["Reject Request"]
ValidOrigin --> |Yes| Continue["Continue Processing"]
Continue --> Authentication["Authenticate User"]
Authentication --> Authenticated{"User Authenticated?"}
Authenticated --> |No| Unauthorized["Return 401"]
Authenticated --> |Yes| ProcessRequest["Process Request"]
ProcessRequest --> Response["Return Response"]
Reject --> Response
Unauthorized --> Response
```

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)

## Error Handling

The authentication system provides comprehensive error handling for both expected and exceptional conditions.

### Client-Side Error Handling
Frontend components use try-catch blocks and error boundaries to gracefully handle authentication failures and display meaningful error messages to users.

```mermaid
flowchart TD
Attempt["Authentication Attempt"] --> Try["Try Block"]
Try --> API["Call authClient Method"]
API --> Catch["Catch Block"]
Catch --> Error{"Error Object?"}
Error --> |No| Rethrow["Rethrow Error"]
Error --> |Yes| CheckType["Check Error Type"]
CheckType --> ValidationError["Validation Error?"]
CheckType --> NetworkError["Network Error?"]
CheckType --> ServerError["Server Error?"]
ValidationError --> ShowFieldErrors["Show Field-Specific Errors"]
NetworkError --> ShowNetworkError["Show Network Error Toast"]
ServerError --> ShowGenericError["Show Generic Error Toast"]
ShowFieldErrors --> End
ShowNetworkError --> End
ShowGenericError --> End
Rethrow --> End
```

**Diagram sources**
- [Login.tsx](file://frontend/src/components/Login.tsx#L35-L74)
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx#L82-L118)

### Server-Side Error Handling
The better-auth library handles server-side errors and returns standardized error objects that can be easily consumed by the frontend.

**Section sources**
- [Login.tsx](file://frontend/src/components/Login.tsx#L35-L74)
- [RegForm.tsx](file://frontend/src/components/RegForm.tsx#L82-L118)

## Session Management

The session management system in takeNpass provides persistent user authentication across application sessions.

### Session Configuration
Sessions are configured with the following parameters:
- **Expiration**: 7 days
- **Update Age**: 24 hours (sessions are refreshed daily)
- **Cookie Cache**: Enabled for improved performance
- **Secure Storage**: Sessions are stored in the database with secure tokens

### Session Flow
```mermaid
sequenceDiagram
participant User as "User"
participant Frontend as "Frontend"
participant Backend as "Backend"
participant Database as "Database"
User->>Frontend : Visit site
Frontend->>Backend : Check for active session
Backend->>Database : Query session by cookie
Database-->>Backend : Return session data
Backend-->>Frontend : Return user data
Frontend->>Frontend : Update application state
Frontend->>User : Display personalized content
User->>Frontend : Log out
Frontend->>Backend : DELETE /api/auth/session
Backend->>Database : Delete session record
Database-->>Backend : Confirmation
Backend-->>Frontend : Success response
Frontend->>Frontend : Clear local state
Frontend->>User : Redirect to login
```

**Section sources**
- [better-auth.ts](file://server/src/providers/better-auth.ts#L1-L34)

## Password Reset and Account Verification

The system supports password reset and account verification workflows through the VerificationToken entity.

### Password Reset Flow
When a user requests a password reset, the system:
1. Creates a VerificationToken with a unique token and expiration time
2. Sends an email with a reset link containing the token
3. Validates the token when the user accesses the reset page
4. Allows password change if the token is valid and not expired
5. Deletes the token after successful password reset

### Account Verification Flow
For account verification (when enabled), the system:
1. Creates a VerificationToken after user registration
2. Sends a verification email with a confirmation link
3. Validates the token when the user clicks the link
4. Updates the user's emailVerified status
5. Deletes the token after successful verification

These workflows use time-limited tokens to prevent abuse and ensure security.

**Section sources**
- [VerificationTokenSchema.ts](file://server/prisma/generated/zod/modelSchema/VerificationTokenSchema.ts#L0-L33)

## Conclusion
The authentication system in takeNpass provides a secure and user-friendly experience for managing user identities. By leveraging better-auth, the system implements industry-standard security practices while maintaining a clean and maintainable codebase. The separation of concerns between frontend components and backend services allows for flexible development and easy maintenance. The system supports essential features including credential-based login, session management, profile editing, and account recovery, making it suitable for production use in a real-world application.