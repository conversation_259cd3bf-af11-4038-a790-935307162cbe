import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { RolesSchema } from './RolesSchema';
import { CaseUncheckedCreateNestedManyWithoutAuthorInputSchema } from './CaseUncheckedCreateNestedManyWithoutAuthorInputSchema';
import { FavoriteUncheckedCreateNestedManyWithoutUserInputSchema } from './FavoriteUncheckedCreateNestedManyWithoutUserInputSchema';
import { AccountUncheckedCreateNestedManyWithoutUserInputSchema } from './AccountUncheckedCreateNestedManyWithoutUserInputSchema';
import { MessageUncheckedCreateNestedManyWithoutReceiverInputSchema } from './MessageUncheckedCreateNestedManyWithoutReceiverInputSchema';
import { MessageUncheckedCreateNestedManyWithoutSenderInputSchema } from './MessageUncheckedCreateNestedManyWithoutSenderInputSchema';
import { NotificationUncheckedCreateNestedManyWithoutSenderInputSchema } from './NotificationUncheckedCreateNestedManyWithoutSenderInputSchema';
import { NotificationUncheckedCreateNestedManyWithoutUserInputSchema } from './NotificationUncheckedCreateNestedManyWithoutUserInputSchema';
import { NotificationSettingsUncheckedCreateNestedManyWithoutUserInputSchema } from './NotificationSettingsUncheckedCreateNestedManyWithoutUserInputSchema';
import { SessionUncheckedCreateNestedManyWithoutUserInputSchema } from './SessionUncheckedCreateNestedManyWithoutUserInputSchema';
import { UserAvatarUncheckedCreateNestedOneWithoutUserInputSchema } from './UserAvatarUncheckedCreateNestedOneWithoutUserInputSchema';
import { UserRatingUncheckedCreateNestedManyWithoutSenderInputSchema } from './UserRatingUncheckedCreateNestedManyWithoutSenderInputSchema';
import { UserRatingUncheckedCreateNestedManyWithoutUserInputSchema } from './UserRatingUncheckedCreateNestedManyWithoutUserInputSchema';
import { CaseUncheckedCreateNestedManyWithoutClientsInputSchema } from './CaseUncheckedCreateNestedManyWithoutClientsInputSchema';

export const UserUncheckedCreateWithoutUserAuthenticationLogInputSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutUserAuthenticationLogInput> = z.object({
  id: z.string().uuid().optional(),
  username: z.string().optional().nullable(),
  email: z.string(),
  emailVerified: z.boolean().optional(),
  image: z.string().optional().nullable(),
  name: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  about_me: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  blocked: z.boolean().optional(),
  confirmed: z.boolean().optional(),
  role: z.lazy(() => RolesSchema).optional(),
  language: z.string().optional(),
  authoredCases: z.lazy(() => CaseUncheckedCreateNestedManyWithoutAuthorInputSchema).optional(),
  favorites: z.lazy(() => FavoriteUncheckedCreateNestedManyWithoutUserInputSchema).optional(),
  accounts: z.lazy(() => AccountUncheckedCreateNestedManyWithoutUserInputSchema).optional(),
  receivedMessages: z.lazy(() => MessageUncheckedCreateNestedManyWithoutReceiverInputSchema).optional(),
  sentMessages: z.lazy(() => MessageUncheckedCreateNestedManyWithoutSenderInputSchema).optional(),
  sendedNotifications: z.lazy(() => NotificationUncheckedCreateNestedManyWithoutSenderInputSchema).optional(),
  notifications: z.lazy(() => NotificationUncheckedCreateNestedManyWithoutUserInputSchema).optional(),
  notificationSettings: z.lazy(() => NotificationSettingsUncheckedCreateNestedManyWithoutUserInputSchema).optional(),
  auth_session: z.lazy(() => SessionUncheckedCreateNestedManyWithoutUserInputSchema).optional(),
  avatar: z.lazy(() => UserAvatarUncheckedCreateNestedOneWithoutUserInputSchema).optional(),
  sendedRatings: z.lazy(() => UserRatingUncheckedCreateNestedManyWithoutSenderInputSchema).optional(),
  ratings: z.lazy(() => UserRatingUncheckedCreateNestedManyWithoutUserInputSchema).optional(),
  cases: z.lazy(() => CaseUncheckedCreateNestedManyWithoutClientsInputSchema).optional()
}).strict();

export default UserUncheckedCreateWithoutUserAuthenticationLogInputSchema;
