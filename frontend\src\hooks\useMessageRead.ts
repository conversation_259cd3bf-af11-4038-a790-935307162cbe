import { useCallback, useRef } from 'react'
import { trpc } from '@/trpc'
import { ChatMessage } from '@/types/chat'

interface UseMessageReadOptions {
  sessionUserId?: string
  onMarkAsRead?: (messageId: number) => void
}

export const useMessageRead = ({ sessionUserId, onMarkAsRead }: UseMessageReadOptions = {}) => {
  const processedMessages = useRef(new Set<number>())
  
  const markAsReadMutation = trpc.message.setAsRead.useMutation({
    onSuccess: (_, variables) => {
      onMarkAsRead?.(variables.messageId)
    },
    onError: (error) => {
      console.error('Failed to mark message as read:', error)
      // Remove from processed set on error so it can be retried
      processedMessages.current.delete(error.data?.messageId as number)
    }
  })

  const markMessageAsRead = useCallback(async (message: ChatMessage, isVisible: boolean) => {
    // Don't mark own messages as read
    if (message.senderId === sessionUserId) {
      return
    }

    // Don't mark already read messages
    if (message.read) {
      return
    }

    // Don't mark if not visible
    if (!isVisible) {
      return
    }

    // Don't process the same message multiple times
    if (processedMessages.current.has(message.id)) {
      return
    }

    // Add to processed set immediately to prevent duplicate requests
    processedMessages.current.add(message.id)

    try {
      await markAsReadMutation.mutateAsync({
        messageId: message.id
      })
    } catch (error) {
      // Error handling is done in mutation onError
    }
  }, [sessionUserId, markAsReadMutation])

  const clearProcessedMessages = useCallback(() => {
    processedMessages.current.clear()
  }, [])

  return {
    markMessageAsRead,
    clearProcessedMessages,
    isMarkingAsRead: markAsReadMutation.isPending
  }
}
