# Utilities and Helpers

<cite>
**Referenced Files in This Document**  
- [isValidEmail.ts](file://server/src/utils/isValidEmail.ts)
- [rateLimit.ts](file://server/src/utils/rateLimit.ts)
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts)
- [GeoMeta.ts](file://server/src/types/GeoMeta.ts)
- [GeoapifyAutocompete.ts](file://server/src/types/GeoapifyAutocompete.ts)
- [GeoapifyGeocode.ts](file://server/src/types/GeoapifyGeocode.ts)
- [GeoapifyReverse.ts](file://server/src/types/GeoapifyReverse.ts)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [Email Validation with isValidEmail.ts](#email-validation-with-isvalidemailts)
3. [Rate Limiting Mechanism with rateLimit.ts](#rate-limiting-mechanism-with-ratelimitts)
4. [Input Validation and Type Safety with CaseShemas.ts](#input-validation-and-type-safety-with-caseshemasts)
5. [Custom Types and Payload Interfaces](#custom-types-and-payload-interfaces)
6. [Extending Utility Functions and Creating New Schemas](#extending-utility-functions-and-creating-new-schemas)
7. [Conclusion](#conclusion)

## Introduction
This document provides comprehensive documentation for the utility functions and helper modules in the takeNpass backend. It details the implementation and usage of key utilities including email validation, API rate limiting, Zod-based input validation schemas, and custom type definitions used across the system. The goal is to provide developers with a clear understanding of how these utilities work, how they are integrated into services, and how they can be extended for future development.

## Email Validation with isValidEmail.ts

The `isValidEmail` function is a lightweight utility used to perform basic email format validation across the backend services. It ensures that user-provided email addresses meet a minimal structural requirement before being processed further.

The implementation uses a simple regular expression pattern to verify that the input string contains both a local part and a domain separated by an "@" symbol. While not enforcing full RFC compliance, this approach provides a fast and effective first-line check suitable for real-time validation scenarios.

This utility is typically invoked during user registration, authentication, and profile update operations to prevent malformed email addresses from entering the system.

**Section sources**  
- [isValidEmail.ts](file://server/src/utils/isValidEmail.ts#L1-L3)

## Rate Limiting Mechanism with rateLimit.ts

The `rateLimit.ts` module implements a robust rate limiting mechanism designed to protect API endpoints from abuse and excessive usage. It provides two main classes: `SimpleRateLimitedQueue` and `RateLimitedQueue`, enabling both basic and advanced throttling strategies.

### Core Features
- **Concurrency Control**: Limits the number of simultaneous operations.
- **Delay Enforcement**: Introduces configurable delays between task executions.
- **Error Handling**: Supports retry logic with configurable maximum attempts.
- **Dynamic Control**: Allows pausing, resuming, freezing, and stopping the queue at runtime.
- **Task Monitoring**: Tracks active tasks and optionally stores completed task results.

### Usage in API Protection
The rate limiter is integrated into critical API routes such as authentication, case creation, and messaging systems. By controlling the frequency and volume of incoming requests, it helps prevent brute-force attacks, denial-of-service scenarios, and unintended system overloads.

Developers can configure the rate limiter with parameters like delay (in milliseconds), concurrency level, maximum retries, and error handling behavior. The queue operates on a promise-based model, allowing asynchronous tasks to be added and executed under controlled conditions.

```mermaid
flowchart TD
A["API Request Received"] --> B{Rate Limit Check}
B --> |Within Limits| C["Add Task to Queue"]
B --> |Exceeded| D["Reject with 429"]
C --> E["Execute Task with Delay/Concurrency Rules"]
E --> F["Return Response"]
D --> F
```

**Diagram sources**  
- [rateLimit.ts](file://server/src/utils/rateLimit.ts#L1-L260)

**Section sources**  
- [rateLimit.ts](file://server/src/utils/rateLimit.ts#L1-L260)

## Input Validation and Type Safety with CaseShemas.ts

The `CaseShemas.ts` file defines a comprehensive set of Zod validation schemas used for input validation and type inference throughout the backend. These schemas ensure data integrity, provide runtime type checking, and generate TypeScript types automatically.

### Key Schemas
- **GeoMetaSchema**: Validates geolocation metadata including address components, coordinates, and display information.
- **WayPointSchema**: Defines structure for route waypoints with optional date, comment, and geometry data.
- **LatLonSchema**: Simple latitude/longitude coordinate pair validation.
- **CaseCreateSchema**: Full schema for creating new cases, including baggage, price, and route points.
- **SearchCaseSchema**: Validation for case search queries based on origin and destination.
- **CaseListParamsSchema**: Complex filtering parameters for retrieving case lists with optional constraints.

These schemas are used in conjunction with tRPC routers to validate incoming requests, ensuring that only properly structured data reaches the business logic layer. They also enable automatic type generation via `z.infer<>`, reducing the need for manual type definitions and improving code consistency.

```mermaid
classDiagram
class GeoMetaSchema {
+address : Object
+lat : number|string
+lon : number|string
+display_name : string?
+type : string?
}
class WayPointSchema {
+id : number?
+geometa : GeoMetaSchema
+date : Date?
+lat : number|string
+lon : number|string
+comment : string?|null
}
class CaseCreateSchema {
+description : string?
+baggage : string[]
+price : number?
+isRequest : boolean?
+expire_at : Date?
+from : WayPointSchema
+to : WayPointSchema
+middlepoints : WayPointSchema[]
}
GeoMetaSchema <|-- WayPointSchema
WayPointSchema <|-- CaseCreateSchema
```

**Diagram sources**  
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L1-L72)

**Section sources**  
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L1-L72)

## Custom Types and Payload Interfaces

The `types/` directory contains a collection of custom type definitions that standardize data structures used across the application, particularly for third-party API integrations and internal payloads.

### Geoapify API Types
- **GeoapifyAutocompete.ts**: Defines response structure for address autocomplete suggestions.
- **GeoapifyGeocode.ts**: Specifies format for forward geocoding results.
- **GeoapifyReverse.ts**: Describes schema for reverse geocoding responses.

### Internal Payload Types
- **CaseWithWay.ts**: Combines case data with full waypoint information.
- **CreateCasePayload.ts**: Input structure for case creation requests.
- **UpdateCasePayload.ts**: Structure for case update operations.
- **SocketNotificationType.ts**: Defines real-time notification message formats.

These types ensure consistent data handling between external services and internal logic, reduce errors from incorrect assumptions about data shape, and improve developer experience through better IDE support and compile-time checks.

**Section sources**  
- [GeoMeta.ts](file://server/src/types/GeoMeta.ts)
- [GeoapifyAutocompete.ts](file://server/src/types/GeoapifyAutocompete.ts)
- [GeoapifyGeocode.ts](file://server/src/types/GeoapifyGeocode.ts)
- [GeoapifyReverse.ts](file://server/src/types/GeoapifyReverse.ts)

## Extending Utility Functions and Creating New Schemas

Developers can extend the existing utility framework by following established patterns for consistency and maintainability.

### Extending Utility Functions
To add new utility functions:
1. Create a new `.ts` file in `server/src/utils/`
2. Export pure functions with clear input/output contracts
3. Include JSDoc comments for documentation
4. Add unit tests in the `test/` directory

Example extension pattern:
```typescript
// utils/newUtility.ts
export function validatePhone(phone: string): boolean {
  return /^\+?[1-9]\d{1,14}$/.test(phone);
}
```

### Creating New Validation Schemas
When defining new Zod schemas:
1. Place them in appropriate schema files or create new ones in `shemas/`
2. Use consistent naming (`PascalCase` with `Schema` suffix)
3. Leverage existing sub-schemas for reusability
4. Provide clear refinement rules when needed

Example schema creation:
```typescript
export const UserPreferencesSchema = z.object({
  language: z.enum(['en', 'es', 'ru']),
  notifications: z.boolean().default(true),
  theme: z.enum(['light', 'dark']).optional()
})
```

Integration with routers ensures automatic validation:
```typescript
appRouter.post('/update-preferences', validateInput(UserPreferencesSchema, handler))
```

**Section sources**  
- [CaseShemas.ts](file://server/src/shemas/CaseShemas.ts#L1-L72)
- [isValidEmail.ts](file://server/src/utils/isValidEmail.ts#L1-L3)
- [rateLimit.ts](file://server/src/utils/rateLimit.ts#L1-L260)

## Conclusion
The utility functions and helper modules in the takeNpass backend provide essential infrastructure for data validation, API protection, and type safety. The `isValidEmail.ts` utility offers lightweight email format checking, while `rateLimit.ts` delivers a sophisticated queuing system for controlling request rates. The Zod schemas in `CaseShemas.ts` ensure robust input validation and enable automatic type generation, and the custom types in the `types/` directory standardize data exchange with external services. Together, these components form a solid foundation that supports secure, reliable, and maintainable backend operations. Developers can confidently extend these utilities following the established patterns to meet evolving requirements.