# API Reference

<cite>
**Referenced Files in This Document**  
- [case.ts](file://server\src\router\routers\case.ts) - *Updated with case management logic*
- [geo.ts](file://server\src\router\routers\geo.ts) - *Updated with geolocation services*
- [message.ts](file://server\src\router\routers\message.ts) - *Messaging functionality*
- [notification.ts](file://server\src\router\routers\notification.ts) - *Notification handling*
- [sub.ts](file://server\src\router\routers\sub.ts) - *Real-time subscriptions*
- [CaseShemas.ts](file://server\src\shemas\CaseShemas.ts) - *Zod schemas for case validation*
- [trpc.ts](file://server\src\router\trpc.ts) - *tRPC base procedures*
- [frontend/src/trpc.ts](file://frontend\src\trpc.ts) - *Frontend tRPC client setup*
</cite>

## Update Summary
- Updated **Case Management** section with accurate input/output types and authentication requirements
- Enhanced **Geolocation Services** with correct schema definitions and rate limiting details
- Verified all endpoint signatures against current implementation
- Added precise file references with line numbers where applicable
- Ensured all documentation aligns with actual code in repository

## Table of Contents
1. [Introduction](#introduction)
2. [Authentication](#authentication)
3. [Case Management](#case-management)
4. [Geolocation Services](#geolocation-services)
5. [Messaging](#messaging)
6. [Notifications](#notifications)
7. [Real-Time Subscriptions](#real-time-subscriptions)
8. [Client-Side Invocation Patterns](#client-side-invocation-patterns)
9. [Error Handling and Rate Limiting](#error-handling-and-rate-limiting)
10. [Migration Guide](#migration-guide)

## Introduction
This document provides comprehensive API documentation for the tRPC-based endpoints in the takeNpass application. The API is structured around modular routers for case management, geolocation, messaging, notifications, and real-time subscriptions. All endpoints are type-safe, leveraging Zod for input validation and TypeScript interfaces for end-to-end typing from frontend to backend.

The architecture uses tRPC with SuperJSON for data serialization, enabling seamless transfer of complex types such as dates and BigInts. Authentication is enforced via middleware, and rate limiting is implemented for external service calls.

## Authentication

The authentication system is exposed through the `_user` router, providing login and session validation procedures.

### Endpoints
- **`login`**: Authenticates a user with login and password.
  - Input: `{ login: string, password: string | number }`
  - Output: Session token and user data
  - Authentication: Public
  - Error Codes: `UNAUTHORIZED`

- **`check`**: Returns current session user data.
  - Input: None
  - Output: User object
  - Authentication: Public
  - Error Codes: `UNAUTHORIZED`

- **`notifications`**: Retrieves user-specific notifications.
  - Input: None
  - Output: Array of notification objects
  - Authentication: Required
  - Error Codes: `UNAUTHORIZED`

**Section sources**
- [\_user.ts](file://server\src\router\routers\_user.ts)

## Case Management

The case management API enables creation, retrieval, and modification of transport cases, including assignment and favoriting.

### Input Validation Schema
Defined in `CaseCreateSchema` and `CaseListParamsSchema`:
- **`CaseCreateSchema`**: Validates case creation/update payload including waypoints, price, and status.
- **`CaseListParamsSchema`**: Filters for case listing by location, date, and radius.

### Endpoints
- **`updateOrCreate`**: Creates or updates a case.
  - Input: `CaseCreateSchema` extended with `status` and optional `id`
  - Output: Created/updated case object
  - Authentication: Required
  - Rate Limiting: Applied via `RateLimitedQueue`
  - Error Codes: `UNAUTHORIZED`, `BAD_REQUEST`

- **`list`**: Retrieves filtered list of cases.
  - Input: `CaseListParamsSchema`
  - Output: Paginated list of cases
  - Authentication: Public

- **`myList`**: Retrieves user-specific cases (created, assigned, favorites).
  - Input: Enum `['assigned', 'favorites', 'created', 'subscribes']`
  - Output: Array of user-related cases
  - Authentication: Required

- **`id`**: Retrieves a specific case by ID.
  - Input: `string` (UUID)
  - Output: Case object with relations
  - Authentication: Public

- **`assignClient`**: Assigns current user as client to a case.
  - Input: `string` (case ID)
  - Output: Updated case
  - Authentication: Required

- **`toFavofite`**: Toggles case in user's favorites.
  - Input: `string` (case ID)
  - Output: Success status
  - Authentication: Required

**Section sources**
- [case.ts](file://server\src\router\routers\case.ts#L10-L73)
- [CaseShemas.ts](file://server\src\shemas\CaseShemas.ts#L0-L71)

## Geolocation Services

Provides forward and reverse geocoding with rate-limited external API calls.

### Input Validation Schema
- **`findQuerySchema`**: `{ query: string, lang?: string }`
- **`reverseSchema`**: `{ lat: number | string, lng: number | string, lang?: string }`

### Endpoints
- **`findLocation`**: Forward geocoding via query.
  - Input: `findQuerySchema`
  - Output: Array of location suggestions
  - Authentication: Public
  - Rate Limiting: 2 concurrent, 1s delay

- **`lazyFindLocation`**: Mutation version of `findLocation`.
  - Same schema and behavior

- **`reverse`**: Reverse geocoding from coordinates.
  - Input: `reverseSchema`
  - Output: Address details
  - Authentication: Public
  - Rate Limiting: 2 concurrent, 1s delay

- **`lazyReverse`**: Mutation version of `reverse`.

**Section sources**
- [geo.ts](file://server\src\router\routers\geo.ts#L10-L40)

## Messaging

The messaging API handles chat functionality between users.

### Endpoints
- **`create`**: Sends a message in a case context.
  - Input: `{ caseId: string, content: string }`
  - Output: Created message object
  - Authentication: Required

- **`list`**: Retrieves message history for a case.
  - Input: `{ caseId: string, limit?: number, cursor?: string }`
  - Output: Paginated messages
  - Authentication: Required

- **`markAsRead`**: Marks messages as read.
  - Input: `{ caseId: string, upToMessageId: string }`
  - Output: Success status
  - Authentication: Required

**Section sources**
- [message.ts](file://server\src\router\routers\message.ts)

## Notifications

Manages user notifications including real-time updates.

### Endpoints
- **`list`**: Retrieves user notifications.
  - Input: None
  - Output: Array of notification objects
  - Authentication: Required

- **`markAsRead`**: Marks notification as read.
  - Input: `{ id: string }`
  - Output: Success status
  - Authentication: Required

- **`subscribe`**: Establishes WebSocket subscription for real-time notifications.
  - Input: None
  - Output: WebSocket stream
  - Authentication: Required

**Section sources**
- [notification.ts](file://server\src\router\routers\notification.ts)

## Real-Time Subscriptions

The subscription router enables real-time updates via WebSockets.

### Endpoints
- **`onCaseUpdate`**: Streams case updates.
  - Input: `{ caseId: string }`
  - Output: Case change events
  - Authentication: Required

- **`onMessage`**: Streams new messages.
  - Input: `{ caseId: string }`
  - Output: Message objects
  - Authentication: Required

- **`onNotification`**: Streams user notifications.
  - Input: None
  - Output: Notification objects
  - Authentication: Required

**Section sources**
- [sub.ts](file://server\src\router\routers\sub.ts)

## Client-Side Invocation Patterns

Frontend uses tRPC React hooks for type-safe API consumption.

### Setup
```ts
import { trpc } from '../trpc'
```

### Examples
#### Fetching Cases
```ts
const { data, isLoading } = trpc.case.list.useQuery(params)
```

#### Creating a Case
```ts
const createCase = trpc.case.updateOrCreate.useMutation()
createCase.mutate(caseData)
```

#### Real-Time Updates
```ts
trpc.sub.onCaseUpdate.useSubscription(
  { caseId },
  { onData: (update) => console.log(update) }
)
```

#### Authentication Check
```ts
const { data: user } = trpc._user.check.useQuery()
```

**Section sources**
- [trpc.ts](file://frontend\src\trpc.ts#L0-L3)

## Error Handling and Rate Limiting

### Error Codes
- `UNAUTHORIZED`: Missing or invalid authentication
- `BAD_REQUEST`: Validation failure
- `TOO_MANY_REQUESTS`: Rate limit exceeded
- `NOT_FOUND`: Resource not found
- `INTERNAL_SERVER_ERROR`: Unexpected server error

### Rate Limiting
Implemented via `RateLimitedQueue`:
- Geolocation: 2 concurrent requests, 1s delay
- Case operations: 5 concurrent, 500ms delay
- Configurable retry and error handling

All rate-limited operations use queuing to prevent overwhelming external services.

**Section sources**
- [rateLimit.ts](file://server\src\utils\rateLimit.ts)
- [case.ts](file://server\src\router\routers\case.ts#L10)
- [geo.ts](file://server\src\router\routers\geo.ts#L10)

## Migration Guide

### Deprecated Endpoints
- `depr_ws.ts`: Legacy WebSocket service replaced by tRPC subscriptions.
- Use `sub.ts` router for real-time functionality.

### Authentication Migration
- Old `createRouterHelper.ts` authentication pattern deprecated.
- Migrate to `trpc.ts` with `authedProcedure`.
- Update context handling to use `checkIsAuthed` function.

### Geolocation API
- `lazyFindLocation` and `lazyReverse` provided for mutation-based calls.
- Prefer `findLocation` and `reverse` for queries to maintain RESTful semantics.

### Case Management
- Unified `updateOrCreate` replaces separate create/update procedures.
- Use `CaseCreateSchema` for consistent validation.

**Section sources**
- [depr_ws.ts](file://server\src\services\depr_ws.ts)
- [createRouterHelper.ts](file://server\src\router\createRouterHelper.ts)
- [trpc.ts](file://server\src\router\trpc.ts)