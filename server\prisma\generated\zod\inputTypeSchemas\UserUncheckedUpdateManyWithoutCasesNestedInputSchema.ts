import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserCreateWithoutCasesInputSchema } from './UserCreateWithoutCasesInputSchema';
import { UserUncheckedCreateWithoutCasesInputSchema } from './UserUncheckedCreateWithoutCasesInputSchema';
import { UserCreateOrConnectWithoutCasesInputSchema } from './UserCreateOrConnectWithoutCasesInputSchema';
import { UserUpsertWithWhereUniqueWithoutCasesInputSchema } from './UserUpsertWithWhereUniqueWithoutCasesInputSchema';
import { UserWhereUniqueInputSchema } from './UserWhereUniqueInputSchema';
import { UserUpdateWithWhereUniqueWithoutCasesInputSchema } from './UserUpdateWithWhereUniqueWithoutCasesInputSchema';
import { UserUpdateManyWithWhereWithoutCasesInputSchema } from './UserUpdateManyWithWhereWithoutCasesInputSchema';
import { UserScalarWhereInputSchema } from './UserScalarWhereInputSchema';

export const UserUncheckedUpdateManyWithoutCasesNestedInputSchema: z.ZodType<Prisma.UserUncheckedUpdateManyWithoutCasesNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutCasesInputSchema),z.lazy(() => UserCreateWithoutCasesInputSchema).array(),z.lazy(() => UserUncheckedCreateWithoutCasesInputSchema),z.lazy(() => UserUncheckedCreateWithoutCasesInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => UserCreateOrConnectWithoutCasesInputSchema),z.lazy(() => UserCreateOrConnectWithoutCasesInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => UserUpsertWithWhereUniqueWithoutCasesInputSchema),z.lazy(() => UserUpsertWithWhereUniqueWithoutCasesInputSchema).array() ]).optional(),
  set: z.union([ z.lazy(() => UserWhereUniqueInputSchema),z.lazy(() => UserWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => UserWhereUniqueInputSchema),z.lazy(() => UserWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => UserWhereUniqueInputSchema),z.lazy(() => UserWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => UserWhereUniqueInputSchema),z.lazy(() => UserWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => UserUpdateWithWhereUniqueWithoutCasesInputSchema),z.lazy(() => UserUpdateWithWhereUniqueWithoutCasesInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => UserUpdateManyWithWhereWithoutCasesInputSchema),z.lazy(() => UserUpdateManyWithWhereWithoutCasesInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => UserScalarWhereInputSchema),z.lazy(() => UserScalarWhereInputSchema).array() ]).optional(),
}).strict();

export default UserUncheckedUpdateManyWithoutCasesNestedInputSchema;
