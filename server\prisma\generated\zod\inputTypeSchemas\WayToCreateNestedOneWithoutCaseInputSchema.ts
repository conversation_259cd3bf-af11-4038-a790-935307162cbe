import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayToCreateWithoutCaseInputSchema } from './WayToCreateWithoutCaseInputSchema';
import { WayToUncheckedCreateWithoutCaseInputSchema } from './WayToUncheckedCreateWithoutCaseInputSchema';
import { WayToCreateOrConnectWithoutCaseInputSchema } from './WayToCreateOrConnectWithoutCaseInputSchema';
import { WayToWhereUniqueInputSchema } from './WayToWhereUniqueInputSchema';

export const WayToCreateNestedOneWithoutCaseInputSchema: z.ZodType<Prisma.WayToCreateNestedOneWithoutCaseInput> = z.object({
  create: z.union([ z.lazy(() => WayToCreateWithoutCaseInputSchema),z.lazy(() => WayToUncheckedCreateWithoutCaseInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => WayToCreateOrConnectWithoutCaseInputSchema).optional(),
  connect: z.lazy(() => WayToWhereUniqueInputSchema).optional()
}).strict();

export default WayToCreateNestedOneWithoutCaseInputSchema;
