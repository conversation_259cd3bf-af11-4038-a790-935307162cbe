import { z } from 'zod'
import { authedProcedure, checkIsAuthed, publicProcedure, router } from '../trpc'
import { UserService } from '../../services/user'
// removed lucia
import { TRPCError } from '@trpc/server'

import PhoneNumberV from 'libphonenumber-js'
// removed prisma zod generated schema usage

const loginSchema = z.object({ login: z.string().min(4).max(30), password: z.string().min(4).max(30) })
const updateAvatarSchema = z.object({ base64string: z.string() })

const updateUserSchema = z.object({
  username: z.string().min(4).max(30).optional(),
  email: z.string().email().optional(),
  password: z.string().min(4).max(30).optional(),
  phone: z
    .string()
    .min(7)
    .max(15)
    .refine((phone) => PhoneNumberV(phone)?.isValid())
    .optional(),
  about_me: z.string().min(0).max(100).optional()
})

const createUserSchema = z.object({
  username: z.string().min(4).max(30),
  email: z.string().email(),
  password: z.string().min(4).max(30),
  avatar: z.string().max(3000).optional(),
  phone: z
    .string()
    .min(7)
    .max(15)
    .refine((phone) => PhoneNumberV(phone)?.isValid())
    .optional()
})

const setUserRatingSchema = z.object({
  // id: z.number().int(),
  rating: z.number().int(),
  comment: z.string().nullable(),
  userId: z.string(),
  // senderId: z.string(),
  // createdAt: z.coerce.date(),
  // updatedAt: z.coerce.date(),
  caseId: z.string()
})

export const userRouter = router({
  //---------
  profileInfo: publicProcedure.input(z.string()).query(async ({ ctx, input }) => {
    // const sessionUser = await checkIsAuthed(ctx)

    return await UserService.profile(input)
  }),
  //---------
  //---------
  myProfileInfo: authedProcedure.query(async ({ ctx, input }) => {
    // const sessionUser = await checkIsAuthed(ctx)
    // return await UserService.profile(input)
  }),
  //---------
  //---------
  setRating: authedProcedure.input(setUserRatingSchema).mutation(async ({ ctx, input }) => {
    const sessionUser = await checkIsAuthed(ctx)

    return await UserService.setRating({
      comment: input.comment,
      userId: input.userId,
      rating: input.rating,
      caseId: input.caseId,
      senderId: sessionUser.id
    })
  }),

  //---------
  myRatings: authedProcedure.query(async ({ ctx, input }) => {
    const sessionUser = await checkIsAuthed(ctx)

    return UserService.myRatings({
      userId: sessionUser.id
    })
  }),
  //---------

  sendedRatings: authedProcedure
    .input(z.object({ includeCase: z.boolean().optional() }).optional())
    .query(async ({ ctx, input }) => {
      const sessionUser = await checkIsAuthed(ctx)

      return UserService.sendedRatings({
        senderId: sessionUser.id,
        wherePayload: undefined,
        includeCase: input?.includeCase
      })
    }),

  //---------

  logout: publicProcedure.mutation(async ({ ctx }) => {
    try {
      console.log('🔐 Attempting logout for session:', ctx.session?.id);

      if (ctx.session?.id) {
        // Используем better-auth API для выхода
        const logoutResult = await auth.api.signOut({
          headers: ctx.req.headers as any,
        });

        console.log('🔐 Logout result:', logoutResult ? 'Success' : 'Failed');

        // Устанавливаем заголовки для очистки cookies
        if (logoutResult && (logoutResult as any).headers) {
          const headers = (logoutResult as any).headers;
          if (headers['set-cookie']) {
            const cookies = Array.isArray(headers['set-cookie'])
              ? headers['set-cookie']
              : [headers['set-cookie']];

            cookies.forEach(cookie => {
              ctx.res.header('set-cookie', cookie);
            });
          }
        }

        ctx.res.status(200);
        return true;
      }

      throw new TRPCError({ code: 'FORBIDDEN', message: 'No active session' });
    } catch (e) {
      console.error('Logout error:', e);
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Logout failed' });
    }
  }),

  //-------------
  login: publicProcedure.input(loginSchema).mutation(async ({ input, ctx }) => {
    try {
      const data = await UserService.createSession(input.login, input.password)

      if (data?.sessionCookie) {
        // Устанавливаем cookies для сессии
        if (Array.isArray(data.sessionCookie)) {
          data.sessionCookie.forEach(cookie => {
            ctx.res.header('set-cookie', cookie);
          });
        } else {
          ctx.res.header('set-cookie', data.sessionCookie);
        }

        return data.user
      }
      throw new TRPCError({ code: 'UNAUTHORIZED', message: 'Invalid credentials' })
    } catch (error) {
      console.error('Login error:', error);
      if (error instanceof Error) {
        if (error.message.includes('Invalid email or password')) {
          throw new TRPCError({ code: 'UNAUTHORIZED', message: 'Invalid email or password' });
        }
        if (error.message.includes('User not found')) {
          throw new TRPCError({ code: 'NOT_FOUND', message: 'User not found' });
        }
      }
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Login failed' });
    }
  }),
  //-------------

  validate: publicProcedure.query(async ({ ctx }) => {
    try {
      const sessionUser = ctx.user
      console.log('🚀 ~ validate:publicProcedure.query ~ sessionUser:', sessionUser?.email || 'No user')

      if (sessionUser) {
        // Дополнительно получаем пользователя из базы данных для актуальных данных
        const dbUser = await UserService.findBy('id', sessionUser.id);
        if (dbUser) {
          return dbUser;
        }
      }

      throw new TRPCError({ code: 'UNAUTHORIZED', message: 'Session not found or expired' })
    } catch (error) {
      console.error('Validation error:', error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Session validation failed' });
    }
  }),

  updateLanguage: authedProcedure.input(z.object({ lang: z.string().min(2).max(2) })).mutation(async ({ ctx, input }) => {
    const sessionUser = ctx.user

    if (!sessionUser?.id) {
      throw new TRPCError({ code: 'UNAUTHORIZED' })
    }

    return await UserService.updateLanguage({ lang: input.lang, userId: sessionUser?.id })
  }),

  updateAvatar: authedProcedure.input(updateAvatarSchema).mutation(async ({ ctx, input }) => {
    const sessionUser = ctx.user

    if (!sessionUser?.id) {
      throw new TRPCError({ code: 'UNAUTHORIZED' })
    }

    const { base64string } = input

    return await UserService.updateAvatar({ base64string, userId: sessionUser?.id })
  }),

  updateProfile: authedProcedure.input(updateUserSchema).mutation(async ({ ctx, input }) => {
    const sessionUser = ctx.user

    if (!sessionUser?.id) {
      throw new TRPCError({ code: 'UNAUTHORIZED' })
    }

    return await UserService.update({ id: sessionUser.id, ...input })
  }),

  createUser: publicProcedure.input(createUserSchema).mutation(async ({ ctx, input }) => {
    try {
      const sessionUser = ctx.user

      if (sessionUser?.id) {
        throw new TRPCError({ code: 'FORBIDDEN', message: 'User already logged in' })
      }

      // Проверяем, не существует ли уже пользователь с таким email
      const existingUser = await UserService.findBy('email', input.email);
      if (existingUser) {
        throw new TRPCError({ code: 'CONFLICT', message: 'User with this email already exists' });
      }

      const newUser = await UserService.create({
        ...input
      })

      if (!newUser) {
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Failed to create user' })
      }

      return newUser;
    } catch (error) {
      console.error('Create user error:', error);
      if (error instanceof TRPCError) {
        throw error;
      }
      if (error instanceof Error) {
        if (error.message.includes('already exists')) {
          throw new TRPCError({ code: 'CONFLICT', message: 'User with this email already exists' });
        }
        if (error.message.includes('validation')) {
          throw new TRPCError({ code: 'BAD_REQUEST', message: 'Invalid user data' });
        }
      }
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'User creation failed' });
    }
  })
})

// const userCaller = userRouter.createCaller({})
