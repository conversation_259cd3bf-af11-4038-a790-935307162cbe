# User Router

<cite>
**Referenced Files in This Document**  
- [user.ts](file://server/src/router/routers/user.ts)
- [_user.ts](file://server/src/router/routers/_user.ts)
- [user.ts](file://server/src/services/user.ts)
- [ProfilePage.tsx](file://frontend/src/pages/ProfilePage.tsx)
- [SettingsPage.tsx](file://frontend/src/pages/SettingsPage.tsx)
</cite>

## Table of Contents
1. [Introduction](#introduction)
2. [User Router Implementation](#user-router-implementation)
3. [Authentication and Session Management](#authentication-and-session-management)
4. [Profile and User Data Procedures](#profile-and-user-data-procedures)
5. [User Ratings and Feedback System](#user-ratings-and-feedback-system)
6. [Input Validation and Security](#input-validation-and-security)
7. [Frontend Integration and Usage](#frontend-integration-and-usage)
8. [Utility Support and Internal Structure](#utility-support-and-internal-structure)
9. [Conclusion](#conclusion)

## Introduction
The User Router in the takeNpass application provides a comprehensive tRPC-based interface for managing user profiles, authentication states, and user-specific operations. It separates public and authenticated endpoints to ensure secure access control while enabling flexible user interactions. This document details the implementation of user-related procedures, their integration with the better-auth authentication system, and how frontend components consume these endpoints.

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)
- [user.ts](file://server/src/services/user.ts#L1-L390)

## User Router Implementation
The User Router is implemented as a tRPC router with distinct procedures for public and authenticated access. It leverages the `authedProcedure` and `publicProcedure` helpers to enforce authentication requirements. The router handles user creation, login, profile management, and rating operations through well-defined input schemas and service integrations.

```mermaid
flowchart TD
A[User Request] --> B{Public or Authenticated?}
B --> |Public| C[Login, Validate, CreateUser]
B --> |Authenticated| D[UpdateProfile, SetRating, MyRatings]
C --> E[UserService Authentication]
D --> F[UserService Operations]
E --> G[Session Management via better-auth]
F --> H[Database Operations]
```

**Diagram sources**
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)
- [user.ts](file://server/src/services/user.ts#L1-L390)

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)

## Authentication and Session Management
Authentication is handled through the better-auth integration, which provides email/password sign-in and sign-up functionality. The `login` procedure validates credentials and returns a session cookie, while the `logout` procedure clears the session via better-auth's signOut API. The `validate` procedure checks the current session state and returns user data if authenticated.

The `createSession` method in `UserService` orchestrates the authentication flow by calling better-auth's signInEmail API and retrieving the corresponding user record from the database. Session cookies are properly propagated to the client through response headers.

```mermaid
sequenceDiagram
participant Client
participant UserRouter
participant UserService
participant BetterAuth
participant Database
Client->>UserRouter : login(login, password)
UserRouter->>UserService : createSession()
UserService->>BetterAuth : signInEmail()
BetterAuth-->>UserService : session + cookies
UserService->>Database : find user by email
Database-->>UserService : user data
UserService-->>UserRouter : session + user
UserRouter->>Client : set-cookie + user data
```

**Diagram sources**
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)
- [user.ts](file://server/src/services/user.ts#L1-L390)

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)
- [user.ts](file://server/src/services/user.ts#L1-L390)

## Profile and User Data Procedures
The User Router provides several procedures for accessing and modifying user profile data. Public endpoints like `profileInfo` allow viewing user profiles by ID, while authenticated endpoints like `myProfileInfo` and `updateProfile` provide access to the current user's data.

Profile updates are validated through the `updateUserSchema` which enforces constraints on username, email, password, phone, and about_me fields. Phone numbers are validated using libphonenumber-js and sanitized to remove non-numeric characters. The `updateAvatar` procedure accepts base64-encoded image data for profile picture updates.

```mermaid
flowchart TD
A[Profile Update Request] --> B[Input Validation]
B --> C{Valid?}
C --> |No| D[Return Error]
C --> |Yes| E[Sanitize Phone Number]
E --> F[Update Database]
F --> G[Return Updated User]
```

**Diagram sources**
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)
- [user.ts](file://server/src/services/user.ts#L1-L390)

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)
- [user.ts](file://server/src/services/user.ts#L1-L390)

## User Ratings and Feedback System
The application includes a comprehensive rating system that allows users to rate each other after completing cases. The `setRating` procedure creates or updates a user rating with a numeric value and optional comment. Ratings are associated with a specific case and require confirmation before being displayed publicly.

Users can retrieve their received ratings through the `myRatings` procedure and their sent ratings through `sendedRatings`. The `includeCase` parameter in `sendedRatings` allows clients to optionally include case details in the response for richer display.

```mermaid
classDiagram
class UserRating {
+string userId
+string senderId
+string caseId
+number rating
+string comment
+boolean confirm
+Date createdAt
}
class User {
+string id
+string username
+string email
+string avatar
+string phone
+string language
}
class Case {
+string id
+string from
+string to
+Date createdAt
+string status
}
User "1" --> "0..*" UserRating : receives
User "1" --> "0..*" UserRating : sends
Case "1" --> "0..*" UserRating : has
```

**Diagram sources**
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)
- [user.ts](file://server/src/services/user.ts#L1-L390)

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)
- [user.ts](file://server/src/services/user.ts#L1-L390)

## Input Validation and Security
Input validation is implemented using Zod schemas that define strict requirements for all user-facing operations. The `createUserSchema` enforces minimum and maximum lengths for username, email, and password fields, while the phone field is validated for proper formatting using libphonenumber-js.

Security considerations include:
- Password hashing handled by better-auth
- Session management through secure cookies
- Authentication state validation for protected endpoints
- Input sanitization for phone numbers
- Conflict detection during user creation

Error handling is comprehensive, with specific TRPCError codes for unauthorized access, validation failures, conflicts, and internal server errors.

**Section sources**
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)
- [user.ts](file://server/src/services/user.ts#L1-L390)

## Frontend Integration and Usage
Frontend components consume the User Router endpoints through the tRPC client. The `ProfilePage.tsx` component uses the `profileInfo` query to display user profiles, with special handling for error states and loading indicators.

The `SettingsPage.tsx` component (implied by documentation objective) would use `updateProfile`, `updateAvatar`, and `updateLanguage` mutations to allow users to modify their account settings. The `useUserStore` hook provides access to the current session user, while tRPC hooks enable reactive data fetching and mutation.

```mermaid
sequenceDiagram
participant ProfilePage
participant trpc
participant UserRouter
participant UserService
ProfilePage->>trpc : profileInfo.useQuery(userId)
trpc->>UserRouter : profileInfo(input)
UserRouter->>UserService : profile(userId)
UserService->>Database : findUniqueOrThrow()
Database-->>UserService : user data
UserService-->>UserRouter : user
UserRouter-->>trpc : response
trpc-->>ProfilePage : data
ProfilePage->>ProfilePage : render ProfileInfo
```

**Diagram sources**
- [ProfilePage.tsx](file://frontend/src/pages/ProfilePage.tsx#L1-L37)
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)

**Section sources**
- [ProfilePage.tsx](file://frontend/src/pages/ProfilePage.tsx#L1-L37)
- [user.ts](file://server/src/router/routers/user.ts#L1-L265)

## Utility Support and Internal Structure
The `_user.ts` file contains an alternative UserRouter class implementation using dependency injection with @tsed/di decorators. While not currently in use, it demonstrates a modular approach to router organization with injected UserService and LocalProtocol dependencies.

The UserService class provides static methods that abstract database operations and integrate with better-auth for authentication. It uses Prisma for database access and includes specialized methods for profile retrieval, rating management, and user updates. The service layer ensures separation of concerns between routing logic and business operations.

**Section sources**
- [_user.ts](file://server/src/router/routers/_user.ts#L1-L59)
- [user.ts](file://server/src/services/user.ts#L1-L390)

## Conclusion
The User Router implementation provides a robust foundation for user management in the takeNpass application. By leveraging tRPC for type-safe APIs, better-auth for secure authentication, and Zod for input validation, it delivers a reliable and maintainable user interface. The clear separation between public and authenticated endpoints, comprehensive error handling, and well-structured service layer make it easy to extend and maintain. Frontend integration is streamlined through tRPC hooks, enabling reactive user interfaces that respond to authentication state changes and data updates.