import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { SortOrderSchema } from './SortOrderSchema';

export const WayFromSumOrderByAggregateInputSchema: z.ZodType<Prisma.WayFromSumOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  lat: z.lazy(() => SortOrderSchema).optional(),
  lon: z.lazy(() => SortOrderSchema).optional()
}).strict();

export default WayFromSumOrderByAggregateInputSchema;
