import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserUpdateWithoutNotificationSettingsInputSchema } from './UserUpdateWithoutNotificationSettingsInputSchema';
import { UserUncheckedUpdateWithoutNotificationSettingsInputSchema } from './UserUncheckedUpdateWithoutNotificationSettingsInputSchema';
import { UserCreateWithoutNotificationSettingsInputSchema } from './UserCreateWithoutNotificationSettingsInputSchema';
import { UserUncheckedCreateWithoutNotificationSettingsInputSchema } from './UserUncheckedCreateWithoutNotificationSettingsInputSchema';
import { UserWhereInputSchema } from './UserWhereInputSchema';

export const UserUpsertWithoutNotificationSettingsInputSchema: z.ZodType<Prisma.UserUpsertWithoutNotificationSettingsInput> = z.object({
  update: z.union([ z.lazy(() => UserUpdateWithoutNotificationSettingsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutNotificationSettingsInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutNotificationSettingsInputSchema),z.lazy(() => UserUncheckedCreateWithoutNotificationSettingsInputSchema) ]),
  where: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export default UserUpsertWithoutNotificationSettingsInputSchema;
